package com.medusa.gruul.account.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 9:17 2024/10/29
 */
@Data
@ApiModel(value = "修改小程序客户dto")
public class MiniAccountDetailDto {

    @ApiModelProperty(value = "id")
    private String id;
    @ApiModelProperty(value = "客户名称")
    private String userName;
    @ApiModelProperty(value = "年龄")
    private Integer age;
    @ApiModelProperty(value = "性别 0：未知、1：男、2：女")
    private Integer gender;
    @ApiModelProperty(value = "生日")
    private String birthday;
    @ApiModelProperty(value = "身份证")
    private String card;


}
