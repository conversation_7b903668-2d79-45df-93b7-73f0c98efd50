package com.medusa.gruul.platform.model.param;

import com.medusa.gruul.common.core.param.QueryParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 16:44 2025/5/27
 */
@ApiModel(value = "AuthRoleMessageParam 实体", description = "角色关联消息 param")
@Data
public class AuthRoleMessageParam extends QueryParam {


    /**
     * 角色id
     */
    @ApiModelProperty(value = "角色id")
    private Long roleId;

}
