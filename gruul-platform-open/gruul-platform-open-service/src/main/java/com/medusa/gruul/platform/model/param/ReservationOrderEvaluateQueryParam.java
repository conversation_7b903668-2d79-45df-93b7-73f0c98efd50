package com.medusa.gruul.platform.model.param;

import com.medusa.gruul.common.core.param.QueryParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 预约单评价查询参数
 *
 * <AUTHOR>
 * @Date: 2025-06-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "ReservationOrderEvaluateQueryParam", description = "预约单评价查询参数")
public class ReservationOrderEvaluateQueryParam extends QueryParam {

    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    @ApiModelProperty(value = "用户ID")
    private Long userId;

    /**
     * 用户名
     */
    @ApiModelProperty(value = "用户名")
    private String userName;

    /**
     * 预约单ID
     */
    @ApiModelProperty(value = "预约单ID")
    private Long reservationOrderId;

    /**
     * 满意度(1-不满意，3-良好，5-满意)
     */
    @ApiModelProperty(value = "满意度(1-不满意，3-良好，5-满意)")
    private Integer successScore;

    /**
     * 评分
     */
    @ApiModelProperty(value = "评分")
    private Integer rate;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    private String startTime;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    private String endTime;
} 