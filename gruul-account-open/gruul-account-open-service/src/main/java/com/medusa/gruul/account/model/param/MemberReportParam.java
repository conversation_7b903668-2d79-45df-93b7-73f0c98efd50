package com.medusa.gruul.account.model.param;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.medusa.gruul.common.core.param.QueryParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * 会员汇总查询参数
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "MemberReportParam 实体", description = "会员汇总查询参数")
@Data
public class MemberReportParam extends QueryParam {

    @ApiModelProperty(value = "会员名称")
    private String userName;

    @ApiModelProperty(value = "会员电话")
    private String userPhone;

    @ApiModelProperty(value = "会员类型ID")
    private List<String> memberTypeIds;

    @ApiModelProperty(value = "注册开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startTime;

    @ApiModelProperty(value = "注册结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endTime;
}
