package com.medusa.gruul.platform.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.medusa.gruul.account.api.conf.MiniInfoProperty;
import com.medusa.gruul.account.api.feign.RemoteMiniAccountService;
import com.medusa.gruul.common.core.constant.CommonConstants;
import com.medusa.gruul.common.core.constant.RegexConstants;
import com.medusa.gruul.common.core.constant.TimeConstants;
import com.medusa.gruul.common.core.constant.enums.AuthCodeEnum;
import com.medusa.gruul.common.core.constant.enums.LoginTerminalEnum;
import com.medusa.gruul.common.core.exception.ServiceException;
import com.medusa.gruul.common.core.util.*;
import com.medusa.gruul.common.data.tenant.ShopContextHolder;
import com.medusa.gruul.common.data.tenant.TenantContextHolder;
import com.medusa.gruul.common.dto.CurPcUserInfoDto;
import com.medusa.gruul.common.dto.CurUserDto;
import com.medusa.gruul.common.redis.RedisManager;
import com.medusa.gruul.goods.api.entity.Warehouse;
import com.medusa.gruul.goods.api.feign.RemoteGoodsService;
import com.medusa.gruul.platform.api.entity.*;
import com.medusa.gruul.platform.api.feign.RemoteMiniInfoService;
import com.medusa.gruul.platform.api.model.dto.OssConfigDto;
import com.medusa.gruul.platform.api.model.vo.AuthMenuButtonVo;
import com.medusa.gruul.platform.api.model.vo.RelationInfoVo;
import com.medusa.gruul.platform.api.model.vo.StoreFrontVo;
import com.medusa.gruul.platform.api.model.vo.UserInfoVo;
import com.medusa.gruul.platform.conf.MeConstant;
import com.medusa.gruul.platform.conf.PlatformRedis;
import com.medusa.gruul.platform.conf.WechatOpenProperties;
import com.medusa.gruul.platform.constant.RedisConstant;
import com.medusa.gruul.platform.constant.ScanCodeScenesEnum;
import com.medusa.gruul.platform.mapper.AccountInfoMapper;
import com.medusa.gruul.platform.mapper.PlatformDepartmentMapper;
import com.medusa.gruul.platform.mapper.PlatformEmployeeMapper;
import com.medusa.gruul.platform.mapper.PlatformStoreFrontMapper;
import com.medusa.gruul.platform.model.dto.*;
import com.medusa.gruul.platform.model.vo.*;
import com.medusa.gruul.platform.service.*;
import com.medusa.gruul.shops.api.entity.ShopsPartner;
import com.medusa.gruul.shops.api.feign.RemoteShopsService;
import lombok.extern.log4j.Log4j2;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.bean.result.WxMpOAuth2AccessToken;
import me.chanjar.weixin.mp.bean.result.WxMpUser;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <p>
 * 平台用户表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-09-07
 */
@Service
@Log4j2
@EnableConfigurationProperties(MiniInfoProperty.class)
public class AccountInfoServiceImpl extends ServiceImpl<AccountInfoMapper, AccountInfo> implements IAccountInfoService {

    @Autowired
    private WxMpService wxMpService;
    @Autowired
    private ISendCodeService sendCodeService;

    @Autowired
    private WechatOpenProperties wechatOpenProperties;
    @Autowired
    private IPlatformShopInfoService platformShopInfoService;
    @Autowired
    private IAuthUserRoleService authUserRoleService;
    @Autowired
    private IAuthMenuButtonService authMenuButtonService;


    @Autowired
    private IAuthRoleInfoService authRoleInfoService;

    @Autowired
    private RemoteShopsService remoteShopsService;

    @Autowired
    private RemoteMiniAccountService remoteMiniAccountService;

    @Autowired
    private PlatformStoreFrontMapper storeFrontMapper;
    @Autowired

    private PlatformDepartmentMapper departmentMapper;

    @Autowired
    private PlatformEmployeeMapper employeeMapper;

    @Autowired
    private IPlatformDepartmentService departmentService;

    @Autowired
    private IPlatformStoreFrontService storeFrontService;

    @Autowired
    private RemoteGoodsService remoteGoodsService;

    @Override
    public void checkoutAccount(String phone, Integer type) {
        AccountInfo accountInfo = this.baseMapper.selectOne(new QueryWrapper<AccountInfo>().eq("phone", phone));
        //账号存在校验
        if (type.equals(CommonConstants.NUMBER_ONE)) {
            if (accountInfo == null) {
                throw new ServiceException("账号不存在", SystemCode.DATA_NOT_EXIST.getCode());
            }
            return;
        }
        //账号不存在校验
        if (type.equals(CommonConstants.NUMBER_TWO)) {
            if (accountInfo != null) {
                throw new ServiceException("账号已存在");
            }
            return;
        }
        throw new ServiceException("非法操作");

    }




    /**
     * 缓存用户数据到缓存中
     * 每天12点失效
     *
     * @param info 用户基本信息
     * @param vo   前端封装用户信息
     * @param resetTokenFlag   是否重置token
     * @return java.lang.String   redisKey token
     */
    private String cachePlatformCurUserDto(AccountInfo info, AccountInfoVo vo, boolean resetTokenFlag) {
        CurUserDto curUserDto = new CurUserDto();
        curUserDto.setUserId(info.getId().toString());
        curUserDto.setUserType(1);
        curUserDto.setAvatarUrl(info.getAvatarUrl());
        curUserDto.setGender(info.getGender());
        curUserDto.setOpenId(info.getOpenId());
        curUserDto.setNikeName(info.getNikeName());
        curUserDto.setIsSuper(vo.getIsSupper());
        LoginShopInfoVo shopInfoVo = vo.getShopInfoVo();
        if (shopInfoVo != null && StrUtil.isNotEmpty(shopInfoVo.getShopId())) {
            curUserDto.setShopId(shopInfoVo.getShopId());
        }
        PlatformRedis platformRedis = new PlatformRedis();
        long between = getTodayEndTime();
        String jwtToken = new JwtUtils(MeConstant.JWT_PRIVATE_KEY).createJwtToken(MeConstant.PLATFORM);
        String redisKey = RedisConstant.TOKEN_KEY.concat(jwtToken);
        platformRedis.setNxPx(redisKey, JSON.toJSONString(curUserDto), between);
        if(resetTokenFlag){
            String userRedisKey = RedisConstant.USER_KEY.concat(info.getId() + "");
            //重置token，则先拿到用户id对应的token记录，然后删除，接着将新的token放到该id记录。目的：相同用户登录会将之前的用户踢出，重置密码也能将已经登录的用户登出
            String tokenKey = platformRedis.get(userRedisKey);
            removeAccountLogin(info);
            if (StrUtil.isNotBlank(tokenKey)) {
                platformRedis.set(userRedisKey, redisKey);
                int expireTime =  ((Long)(between/1000)).intValue();
                platformRedis.expire(userRedisKey, expireTime);
            }else{
                platformRedis.setNxPx(userRedisKey, redisKey, between);
            }
        }

        //新版
        CurPcUserInfoDto curPcUserInfoDto = new CurPcUserInfoDto();
        curPcUserInfoDto.setUserId(info.getId().toString());
        curPcUserInfoDto.setTerminalType(LoginTerminalEnum.PC);
        curPcUserInfoDto.setAvatarUrl(info.getAvatarUrl());
        curPcUserInfoDto.setGender(info.getGender());
        curPcUserInfoDto.setOpenId(info.getOpenId());
        curPcUserInfoDto.setNikeName(info.getNikeName());
        PlatformRedis allRedis = new PlatformRedis(CommonConstants.PC_INFO_REDIS_KEY);
        allRedis.setNxPx(jwtToken, JSON.toJSONString(curPcUserInfoDto), between);

        return platformRedis.getBaseKey().concat(":").concat(redisKey);

    }

    /**
     * 获取当前时间距离当天结束时间还有多久毫秒值
     *
     * @return 1234ms
     */
    private long getTodayEndTime() {
        Date date = new Date();
        DateTime endOfDay = DateUtil.endOfDay(date);
        return DateUtil.between(date, endOfDay, DateUnit.MS);
    }

    /**
     * 更新用户最后登录时间
     *
     * @param accountInfoId com.medusa.gruul.platform.api.entity.AccountInfo
     */
    private void updateAccountLastLoignTime(Long accountInfoId) {
        CompletableFuture.runAsync(() -> {
            AccountInfo info = new AccountInfo();
            info.setLastLoginTime(LocalDateTime.now());
            info.setId(accountInfoId);
            this.updateById(info);
        });
    }

    /**
     * 更新用户设备id号
     *
     * @param accountInfoId com.medusa.gruul.platform.api.entity.AccountInfo
     */
    private void updateAccountRegistrationId(Long accountInfoId,String registrationId) {
        CompletableFuture.runAsync(() -> {
            AccountInfo info = new AccountInfo();
            info.setRegistrationId(registrationId);
            info.setId(accountInfoId);
            this.updateById(info);
        });
    }
    @Override
    public String preAccountScanCode(PreAccountVerifyDto preAccountVerifyDto) {
        if (!ScanCodeScenesEnum.findScenes(preAccountVerifyDto.getScenes())) {
            throw new ServiceException("场景类型无效");
        }
        CurUserDto httpCurUser = CurUserUtil.getHttpCurUser();
        if (httpCurUser != null) {
            preAccountVerifyDto.setUserId(Long.valueOf(httpCurUser.getUserId()));
        }
        if (preAccountVerifyDto.getScenes().equals(ScanCodeScenesEnum.ACCOUNT_SHOP_INFO_CHECK.getScenes())) {
            Long shopInfoId = preAccountVerifyDto.getShopInfoId();
            if (shopInfoId == null) {
                throw new ServiceException("shopInfoId不能为空");
            }
            PlatformShopInfo platformShopInfo = platformShopInfoService.getById(shopInfoId);
            if (platformShopInfo == null) {
                throw new ServiceException("店铺不存在");
            }
        }

        String redirectUrl = wechatOpenProperties.getDomain().concat("/account-info/account/verify/notify");
        //网页应用目前仅填写snsapi_login即
        String scope = "snsapi_login";
        String state = SecureUtil.md5(System.currentTimeMillis() + "");
        new PlatformRedis().setNxPx(state, JSONObject.toJSONString(preAccountVerifyDto), TimeConstants.TEN_MINUTES);

        return wxMpService.switchoverTo(preAccountVerifyDto.getAppId()).buildQrConnectUrl(redirectUrl, scope, state);
    }

    @Override
    public void accountScanCodeNotify(String code, String state, HttpServletResponse response) {
        if (StrUtil.isEmpty(state)) {
            throw new ServiceException("非法请求");
        }
        PlatformRedis platformRedis = new PlatformRedis();
        String jsonData = platformRedis.get(state);
        if (StrUtil.isEmpty(jsonData)) {
            throw new ServiceException("无效数据或数据已过期");
        }
        PreAccountVerifyDto preAccountVerifyDto = JSONObject.parseObject(jsonData, PreAccountVerifyDto.class);
        Result result = Result.failed();
        //微信换绑
        if (preAccountVerifyDto.getScenes().equals(ScanCodeScenesEnum.ACCOUNT_SWITCHING.getScenes())) {
            result = this.changeTie(preAccountVerifyDto.getAppId(), code, preAccountVerifyDto.getUserId());
            //账号注册
        } else if (preAccountVerifyDto.getScenes().equals(ScanCodeScenesEnum.ACCOUNT_REGISTER.getScenes())) {
            result = this.createTempAccount(preAccountVerifyDto.getAppId(), code);
        } else if (preAccountVerifyDto.getScenes().equals(ScanCodeScenesEnum.ACCOUNT_LOGGIN.getScenes())) {
            result = this.scanCodeLogin(preAccountVerifyDto.getAppId(), code);
        } else if (preAccountVerifyDto.getScenes().equals(ScanCodeScenesEnum.ACCOUNT_SHOP_INFO_CHECK.getScenes())) {
            result = this.verifyShopAccount(preAccountVerifyDto.getAppId(), code, preAccountVerifyDto.getShopInfoId());
        } else {
            throw new ServiceException("错误数据");
        }

        //用户同意授权,跳转成功后页面
        StringBuilder redirectUrl = new StringBuilder(preAccountVerifyDto.getRedirectUrl());
        //判断是否已存在路径参数
        if (preAccountVerifyDto.getRedirectUrl().contains(MeConstant.WENHAO)) {
            redirectUrl.append("&");
        } else {
            redirectUrl.append("?");
        }
        code = SecureUtil.md5(System.currentTimeMillis() + "");
        redirectUrl.append("code=").append(code);
        //获取二维码时如存在shopInfoId则返回，供校验扫码前和扫码后是否一致
        if (preAccountVerifyDto.getShopInfoId() != null) {
            redirectUrl.append("&shopInfoId=").append(preAccountVerifyDto.getShopInfoId());
        }
        //存储查询之后不删除但只有5分钟有效的数据
        platformRedis.setNxPx(code.concat(":inside"), JSONObject.toJSONString(result), TimeConstants.TEN_MINUTES);
        //存储返回结果,提供一次查询,查询即失效
        platformRedis.setNxPx(code, JSONObject.toJSONString(result), TimeConstants.TEN_MINUTES);

        try {
            response.sendRedirect(redirectUrl.toString());
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 扫码校验用户是否是店铺拥有者
     *
     * @param appId      网站应用appId
     * @param code       回调code
     * @param shopInfoId 店铺id
     * @return com.medusa.gruul.common.core.util.Result
     */
    private Result verifyShopAccount(String appId, String code, Long shopInfoId) {
        try {
            wxMpService.switchoverTo(appId);
            AccountInfo accountInfo = null;
            WxMpOAuth2AccessToken wxMpOauth2AccessToken = wxMpService.oauth2getAccessToken(code);
            if (StrUtil.isNotEmpty(wxMpOauth2AccessToken.getUnionId())) {
                accountInfo = this.getByAccountUnionId(wxMpOauth2AccessToken.getUnionId());
            }
            if (accountInfo == null) {
                accountInfo = this.getByAccountOpenId(wxMpOauth2AccessToken.getOpenId());
                if (accountInfo == null) {
                    return Result.failed("请使用正确账户");
                }
            }
            PlatformShopInfo platformShopInfo = platformShopInfoService.getById(shopInfoId);
            if (platformShopInfo == null) {
                return Result.failed("店铺数据不存在,非法校验");
            }
            if (!platformShopInfo.getAccountId().equals(accountInfo.getId())) {
                return Result.failed("请使用店铺所有者的微信账号扫码");
            }
            return Result.ok(accountInfo);
        } catch (WxErrorException e) {
            e.printStackTrace();
            return Result.failed(e.getError().getErrorMsg());
        }
    }

    /**
     * 扫码登录回调
     *
     * @param appId 网站应用appId
     * @param code  回调code
     * @return com.medusa.gruul.common.core.util.Result
     */
    private Result scanCodeLogin(String appId, String code) {

        try {
            wxMpService.switchoverTo(appId);
            AccountInfo accountInfo = null;
            WxMpOAuth2AccessToken wxMpOauth2AccessToken = wxMpService.oauth2getAccessToken(code);
            if (StrUtil.isNotEmpty(wxMpOauth2AccessToken.getUnionId())) {
                accountInfo = this.getByAccountUnionId(wxMpOauth2AccessToken.getUnionId());
                if (accountInfo != null) {
                    return Result.ok(accountInfo);
                }
            }
            accountInfo = this.getByAccountOpenId(wxMpOauth2AccessToken.getOpenId());
            //openId也查不出来则说明是未注册账号,存储临时数据,可提供跳转注册
            if (accountInfo == null) {
                accountInfo = new AccountInfo();
                getMpInfo(accountInfo, wxMpOauth2AccessToken, appId);
            }
            return Result.ok(accountInfo);
        } catch (WxErrorException e) {
            e.printStackTrace();
            return Result.failed(e.getError().getErrorMsg());
        }
    }

    /**
     * 创建临时账号数据,不存储至数据库,待账号注册成功时正式存入
     *
     * @param appId 网站应用的appid
     * @param code  授权返回的code
     * @return com.medusa.gruul.common.core.util.Result
     */
    private Result<AccountInfo> createTempAccount(String appId, String code) {
        this.wxMpService.switchoverTo(appId);
        try {
            AccountInfo accountInfo = null;
            WxMpOAuth2AccessToken wxMpOauth2AccessToken = wxMpService.oauth2getAccessToken(code);
            if (StrUtil.isNotEmpty(wxMpOauth2AccessToken.getUnionId())) {
                accountInfo = this.getByAccountUnionId(wxMpOauth2AccessToken.getUnionId());
                if (accountInfo != null) {
                    return Result.failed("该微信账号已存在绑定账号");
                }
            }
            accountInfo = this.getByAccountOpenId(wxMpOauth2AccessToken.getOpenId());
            if (accountInfo != null) {
                return Result.failed("该微信账号已存在绑定账号");
            }
            accountInfo = new AccountInfo();
            getMpInfo(accountInfo, wxMpOauth2AccessToken, appId);
            return Result.ok(accountInfo);
        } catch (WxErrorException e) {
            e.printStackTrace();
            return Result.failed(e.getError().getErrorMsg());
        }

    }

    /**
     * 根据openId 获取用户信息
     *
     * @param openId 微信openId
     * @return com.medusa.gruul.platform.api.entity.AccountInfo
     */
    private AccountInfo getByAccountOpenId(String openId) {
        return this.getBaseMapper().selectOne(new QueryWrapper<AccountInfo>().eq("open_id", openId));
    }

    /**
     * 根据unionId 获取用户信息
     *
     * @param unionId 平台互通unionId
     * @return com.medusa.gruul.platform.api.entity.AccountInfo
     */
    private AccountInfo getByAccountUnionId(String unionId) {
        return this.getBaseMapper().selectOne(new QueryWrapper<AccountInfo>().eq("union_id", unionId));
    }

    @Override
    public LoginAccountInfoDetailVo info() {
        CurPcUserInfoDto curUser = CurUserUtil.getPcRqeustAccountInfo();
        if (curUser == null) {
            throw new ServiceException("非法查询", SystemCode.DATA_NOT_EXIST.getCode());
        }
        AccountInfo accountInfo = this.getById(curUser.getUserId());
        AccountInfoVo loginInfoVo = getLoginInfoVo(accountInfo, false);
        LoginAccountInfoDetailVo vo = new LoginAccountInfoDetailVo();
        BeanUtils.copyProperties(loginInfoVo, vo);
        vo.setBalance(accountInfo.getBalance());
        vo.setAccountType(accountInfo.getAccountType());
        vo.setPhone(accountInfo.getPhone());
        return vo;
    }


    @Override
    public Result<AccountInfo> changeTie(String appId, String code, Long userId) {
        AccountInfo accountInfo = null;
        try {
            WxMpOAuth2AccessToken wxMpOauth2AccessToken = wxMpService.switchoverTo(appId).oauth2getAccessToken(code);
            //判断是否已存在绑定账号
            AccountInfo old = null;
            if (StrUtil.isNotEmpty(wxMpOauth2AccessToken.getUnionId())) {
                old = this.baseMapper.selectOne(new QueryWrapper<AccountInfo>().eq("union_id", wxMpOauth2AccessToken.getUnionId()).notIn("id", userId));
            }
            if (old == null) {
                old = this.baseMapper.selectOne(new QueryWrapper<AccountInfo>().eq("open_id", wxMpOauth2AccessToken.getUnionId()).notIn("id", userId));
            }
            if (old != null) {
                throw new ServiceException("该微信号已绑定账号");
            }
            accountInfo = this.baseMapper.selectById(userId);
            AccountInfo info = getMpInfo(accountInfo, wxMpOauth2AccessToken, appId);

            this.updateById(info);
        } catch (WxErrorException e) {
            e.printStackTrace();
            return Result.failed(e.getMessage());
        } catch (ServiceException e) {
            return Result.failed(e.getMessage());
        }
        return Result.ok(accountInfo);
    }


    @Override
    public void phoneChangeTie(PhoneChangeTieDto phoneChangeTieDto) {
        sendCodeService.certificateCheck(phoneChangeTieDto.getOneCertificate(), phoneChangeTieDto.getOldPhone(), AuthCodeEnum.ACCOUNT_PHONE_IN_TIE.getType());
        AccountInfo phoneAccount = this.getByPhone(phoneChangeTieDto.getNewPhone());
        if (phoneAccount != null) {
            throw new ServiceException("换绑手机账号已被使用");
        }
        sendCodeService.certificateCheck(phoneChangeTieDto.getTwoCertificate(), phoneChangeTieDto.getNewPhone(), AuthCodeEnum.ACCOUNT_PHONE_IN_TIE.getType());
        AccountInfo accountInfo = this.getById(CurUserUtil.getPcRqeustAccountInfo().getUserId());
        if (accountInfo == null) {
            throw new ServiceException("无效token");
        }
        accountInfo.setId(accountInfo.getId());
        accountInfo.setPhone(phoneChangeTieDto.getNewPhone());
        this.baseMapper.updateById(accountInfo);
        removeAccountLogin(accountInfo);

    }

    @Override
    public void passChangeTie(PassChangeTieDto passChangeTieDto) {
        AccountInfo accountInfo = this.getById(CurUserUtil.getPcRqeustAccountInfo().getUserId());
        if (accountInfo == null) {
            throw new ServiceException("无效账号");
        }
        if (!accountInfo.getPhone().equals(passChangeTieDto.getPhone())) {
            throw new ServiceException("手机号不正确");
        }
        //sendCodeService.certificateCheck(passChangeTieDto.getCertificate(), accountInfo.getPhone(), AuthCodeEnum.ACCOUNT_PASSWORD_IN_TIE.getType());
        removeAccountLogin(accountInfo);
        accountInfo.setPassword(passChangeTieDto.getPasswd());
        String salt = RandomUtil.randomString(6);
        accountInfo.setSalt(salt);
        accountInfo.setPasswd(SecureUtil.md5(accountInfo.getPassword().concat(salt)));
        this.baseMapper.updateById(accountInfo);


    }


    @Override
    public AccountInfoVo login(LoginDto loginDto, boolean resetTokenFlag) {
        AccountInfoVo vo = null;
        switch (loginDto.getLoginType()) {
            case 1:
                vo = passwdLogin(loginDto.getPhone(), loginDto.getPassword(), loginDto.getTenantId(), resetTokenFlag);
                break;
            case 2:
                vo = phoneCodeLogin(loginDto.getPhone(), loginDto.getCertificate(), resetTokenFlag);
                break;
            case 3:
                vo = wxScanCodeLogin(loginDto.getCode(), resetTokenFlag);
                break;
            default:
                throw new ServiceException("非法登录请求");
        }

        updateAccountLastLoignTime(vo.getId());
        if(StringUtils.isNotEmpty(loginDto.getRegistrationId())){
            updateAccountRegistrationId(vo.getId(),loginDto.getRegistrationId());
        }
        return vo;
    }

    /**
     * @param code code
     * @return
     */
    @Override
    public Result verifyStateResult(String code) {
        PlatformRedis platformRedis = new PlatformRedis();
        String jsonData = platformRedis.get(code);
        if (StrUtil.isEmpty(jsonData)) {
            return Result.failed("code已失效");
        }
        platformRedis.del(code);
        return JSONObject.parseObject(jsonData, Result.class);
    }

    @Override
    public void passwordRetrieve(PasswordRetrieveDto passwordRetrieveDto) {
        AccountInfo accountInfo = getByPhone(passwordRetrieveDto.getPhone());
        if (accountInfo == null) {
            throw new ServiceException("不存在该账号");
        }
        removeAccountLogin(accountInfo);
        //校验验证与手机号是否正确
        sendCodeService.certificateCheck(passwordRetrieveDto.getCertificate(), passwordRetrieveDto.getPhone(), AuthCodeEnum.ACCOUNT_FORGET_PASSWD.getType());

        accountInfo.setPassword(passwordRetrieveDto.getPasswd());
        String salt = RandomUtil.randomString(6);
        accountInfo.setSalt(salt);
        accountInfo.setPasswd(SecureUtil.md5(accountInfo.getPassword().concat(salt)));
        this.baseMapper.updateById(accountInfo);


    }

    /**
     * 清除指定用户缓存token
     *
     * @param accountInfo 用户数据
     */
    private void removeAccountLogin(AccountInfo accountInfo) {
        PlatformRedis platformRedis = new PlatformRedis();
        String userRedisKey = RedisConstant.USER_KEY.concat(accountInfo.getId() + "");
        //删除token
        String tokenKey = platformRedis.get(userRedisKey);
        if (StrUtil.isNotBlank(tokenKey)) {
            long num = platformRedis.del(tokenKey);
        }
    }

    /**
     * @param state
     * @return
     */
    private AccountInfoVo wxScanCodeLogin(String state, boolean resetTokenFlag) {
        String jsonData = new PlatformRedis().get(state);
        if (StrUtil.isEmpty(jsonData)) {
            throw new ServiceException("不存在该账号");
        }
        Result result = JSONObject.parseObject(jsonData, Result.class);
        if (result.getCode() != CommonConstants.SUCCESS) {
            throw new ServiceException(result.getMsg());
        }
        AccountInfo accountInfo = ((JSONObject) result.getData()).toJavaObject(AccountInfo.class);
        if (accountInfo.getId() == null) {
            throw new ServiceException("该微信账号未注册");
        }
        return getLoginInfoVo(accountInfo, resetTokenFlag);
    }

    private AccountInfoVo phoneCodeLogin(String phone, String certificate, boolean resetTokenFlag) {
        AccountInfo accountInfo = this.getByPhone(phone);
        if (accountInfo == null) {
            throw new ServiceException("账号不存在");
        }
        sendCodeService.certificateCheck(certificate, phone, AuthCodeEnum.MINI_LOGIN.getType());
        return getLoginInfoVo(accountInfo, resetTokenFlag);
    }

    /**
     * 手机号登录
     *
     * @param phone    手机号
     * @param password 密码
     * @return
     */
    private AccountInfoVo passwdLogin(String phone, String password, String tenantId, boolean resetTokenFlag) {
        AccountInfo accountInfo = StrUtil.isBlank(tenantId) ? this.getByPhone(phone) : this.getByPhone(phone, tenantId);
        if (accountInfo == null) {
            throw new ServiceException("账号或密码错误");
        }
        String md5Pw = SecureUtil.md5(password.concat(accountInfo.getSalt()));
        if (!md5Pw.equals(accountInfo.getPasswd())) {
            throw new ServiceException("账号或密码错误");
        }
        return getLoginInfoVo(accountInfo, resetTokenFlag);
    }

    /**
     * 封装返回用户登录信息
     *
     * @param accountInfo 账号信息
     * @return com.medusa.gruul.platform.model.vo.AccountInfoVo
     */
    @Override
    public AccountInfoVo getLoginInfoVo(AccountInfo accountInfo, boolean resetTokenFlag) {
        if (accountInfo.getForbidStatus().equals(CommonConstants.NUMBER_ONE)) {
            throw new ServiceException("账号当前无法登陆，请联系客服");
        }
        AccountInfoVo vo = new AccountInfoVo();
        BeanUtils.copyProperties(accountInfo, vo);
        //获取店铺信息 -- 改为直接获取shopsPartner表的信息
        /*LambdaQueryWrapper<PlatformShopInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PlatformShopInfo::getTenantId, accountInfo.getTenantId());
        PlatformShopInfo shopInfo = platformShopInfoService.getOne(wrapper);
        if (shopInfo != null) {
            LoginShopInfoVo infoVo = platformShopInfoService.getLoginShopInfoVo(shopInfo);
            infoVo.setShopId(accountInfo.getShopId());
            ShopsPartner shopsPartner = remoteShopsService.getByShopId(Long.valueOf(accountInfo.getShopId()));
            Integer mainFlag = shopsPartner.getMainFlag();
            infoVo.setMainFlag(mainFlag);
            vo.setShopInfoVo(infoVo);
        }*/
        ShopsPartner shopsPartner = remoteShopsService.getByShopId(Long.valueOf(accountInfo.getShopId()));
        LoginShopInfoVo infoVo = new LoginShopInfoVo();
        if(null != shopsPartner){
            infoVo.setMiniId(null);
            infoVo.setPlatformShopId(null);
            infoVo.setMpId(null);
            infoVo.setLogoUrl(shopsPartner.getLogo());
            infoVo.setShopName(shopsPartner.getName());
            infoVo.setTenantId(shopsPartner.getTenantId());
            infoVo.setShopId(accountInfo.getShopId());
            infoVo.setMainFlag(shopsPartner.getMainFlag());
            infoVo.setShopKeyId(shopsPartner.getId());
        }
        vo.setShopInfoVo(infoVo);
        //获取角色信息
        List<AuthUserMenuVo> list =  authUserRoleService.getAuthUserMenuVo(accountInfo.getId());
        if(list!=null&&list.size()>0){
            for (AuthUserMenuVo authUserMenuVo : list) {
                Long id = authUserMenuVo.getId();
                List<AuthUserMenuVo> authUserMenuSecondVo = authUserRoleService.getAuthUserMenuSecondVo(accountInfo.getId(), id);
                authUserMenuVo.setChildren(authUserMenuSecondVo);
            }
            vo.setAuthUserMenuVos(list);
        }

        List<AuthMenuButtonVo> authMenuButtonVos = authMenuButtonService.getAuthMenuButtonVoByUserId(accountInfo.getId());
        if(authMenuButtonVos!=null&&authMenuButtonVos.size()>0){
            vo.setAuthMenuButtonVos(authMenuButtonVos);
        }
        //判断是否为超级管理员
        if(vo.getShopInfoVo()!=null){
            if(vo.getAccountType()!=null&&vo.getAccountType()==0&&vo.getShopInfoVo().getMainFlag()!=null&&vo.getShopInfoVo().getMainFlag()==1){
                vo.setIsSupper(1);
            }
        }
        //设置平台用户Token redisKey
        String userToken = cachePlatformCurUserDto(accountInfo, vo, resetTokenFlag);
        vo.setToken(userToken);

        // 获取绑定的店铺信息
        if(accountInfo.getId()!=null){
            List<StoreFrontVo> storeFrontVoList = storeFrontMapper.getStoreFrontVoByAccountId(accountInfo.getId()+"");
            if(storeFrontVoList!=null && storeFrontVoList.size()>0){
                StoreFrontVo storeFrontVo = storeFrontVoList.get(0);
                vo.setStoreFrontVo(storeFrontVo);
            }
        }
        return vo;
    }



    @Override
    public Boolean affirmLessee(String token) {
        String tenantId = TenantContextHolder.getTenantId();
        if (StrUtil.isEmpty(tenantId)) {
            return Boolean.FALSE;
        }
        RedisManager redisManager = RedisManager.getInstance();
        String user = redisManager.get(token);
        if (StrUtil.isEmpty(user)) {
            return Boolean.FALSE;
        }
        CurUserDto curUserDto = JSON.parseObject(user, CurUserDto.class);
        PlatformShopInfo platformShopInfo = platformShopInfoService.getByTenantId(tenantId);
        if (!curUserDto.getUserId().equals(platformShopInfo.getAccountId().toString())) {
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }


    @Override
    public void emailChange(EmailChangeDto emailChangeDto) {
        AccountInfo accountInfo = this.getById(CurUserUtil.getPcRqeustAccountInfo().getUserId());
        if (ObjectUtil.isNull(accountInfo)) {
            throw new ServiceException("非法数据");
        }
        AccountInfo up = new AccountInfo();
        up.setId(accountInfo.getId());
        up.setEmail(emailChangeDto.getEmail());
        this.updateById(up);
    }




    @Override
    public Boolean verifyData(VerifyDataDto verifyDataDto) {
        CurUserDto curUser = CurUserUtil.getHttpCurUser();
        if (curUser == null) {
            throw new ServiceException("非法查询", SystemCode.DATA_NOT_EXIST.getCode());
        }
        AccountInfo accountInfo = this.getById(curUser.getUserId());
        Boolean flag = Boolean.FALSE;
        if (verifyDataDto.getOption().equals(CommonConstants.NUMBER_ONE)) {
            flag = accountInfo.getPhone().equals(verifyDataDto.getPhone());
        }
        return flag;
    }

    /**
     * 添加平台用户
     * @param accountInfoDto
     * @return
     */
    @Override
    public AccountInfo addAccountInfo(PlatformAccountInfoDto accountInfoDto){
        if(null == accountInfoDto.getForbidStatus()){
            //默认禁用状态
            accountInfoDto.setForbidStatus(CommonConstants.NUMBER_ONE);
        }
        if(null == accountInfoDto.getAccountType()){
            //默认子账户
            accountInfoDto.setAccountType(CommonConstants.NUMBER_ONE);
        }
        //新增一个正常状态的用户，判断用户数是否已经超过规定的用户数
        if(CommonConstants.NUMBER_ZERO.intValue() == accountInfoDto.getForbidStatus()){
            PlatformShopInfo platformShopInfo = this.platformShopInfoService.getInfo();
            String pcUserNum = StrUtil.isBlank(platformShopInfo.getPcUserNum()) ? "0" : platformShopInfo.getPcUserNum();
            LambdaQueryWrapper<AccountInfo> countWrapper = new LambdaQueryWrapper<>();
            countWrapper.eq(AccountInfo::getForbidStatus, CommonConstants.NUMBER_ZERO);
            int nowCount = this.baseMapper.selectCount(countWrapper) + 1;
            if(nowCount > Integer.parseInt(pcUserNum)){
                throw new ServiceException("超过购买的用户数，如需增加购买，请联系厂家", SystemCode.DATA_ADD_FAILED.getCode());
            }
        }
        //判断电话是否已存在
        AccountInfo accountInfo = this.getByPhone(accountInfoDto.getPhone());
        if (accountInfo != null) {
            throw new ServiceException("手机号码已存在");
        }
        accountInfo = new AccountInfo();
        BeanUtils.copyProperties(accountInfoDto, accountInfo);
        if(StrUtil.isNotBlank(accountInfoDto.getPasswd())){
            String salt = RandomUtil.randomString(6);
            accountInfo.setSalt(salt);
            accountInfo.setPasswd(SecureUtil.md5(accountInfoDto.getPasswd().concat(salt)));
        }
        this.save(accountInfo);

        List<Long> roleIds = accountInfoDto.getRoleIds();
        AuthUserRoleParamDto authUserRoleParamDto = new AuthUserRoleParamDto();
        authUserRoleParamDto.setUserId(accountInfo.getId());
        authUserRoleParamDto.setRoleIds(roleIds);
        authUserRoleService.saveAuthUserRole(authUserRoleParamDto);

        return accountInfo;
    }

    /**
     * 编辑平台用户
     * @param accountInfoDto
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public AccountInfo editAccountInfo(PlatformAccountInfoDto accountInfoDto){
        //是否为主店铺
        Boolean isMainFlag = false;
        String shopId = ShopContextHolder.getShopId();
        if(StringUtils.isNotEmpty(shopId)){
            ShopsPartner shopsPartner = remoteShopsService.getByShopId(Long.valueOf(shopId));
            if(shopsPartner!=null&&shopsPartner.getMainFlag()==1){
                isMainFlag = true;
            }
        }

        if(isMainFlag){
            ShopContextHolder.setShopId(CommonConstants.DEFAULT_SHOP_ID);
        }

        if(null == accountInfoDto.getForbidStatus()){
            accountInfoDto.setForbidStatus(CommonConstants.NUMBER_ONE);
        }
        //判断用户是否存在
        AccountInfo accountInfo = this.getById(accountInfoDto.getId());
        if (accountInfo == null) {
            throw new ServiceException("用户不存在");
        }
        //编辑变为一个正常状态的用户，判断用户数是否已经超过规定的用户数
        if(CommonConstants.NUMBER_ZERO.intValue() == accountInfoDto.getForbidStatus()){
            PlatformShopInfo platformShopInfo = this.platformShopInfoService.getInfo();
            String pcUserNum = StrUtil.isBlank(platformShopInfo.getPcUserNum()) ? "0" : platformShopInfo.getPcUserNum();
            LambdaQueryWrapper<AccountInfo> countWrapper = new LambdaQueryWrapper<>();
            countWrapper.eq(AccountInfo::getForbidStatus, CommonConstants.NUMBER_ZERO).
                    ne(AccountInfo::getId, accountInfoDto.getId());
            int nowCount = this.baseMapper.selectCount(countWrapper) + 1;
            if(nowCount > Integer.parseInt(pcUserNum)){
                throw new ServiceException("超过购买的用户数，如需增加购买，请联系厂家", SystemCode.DATA_ADD_FAILED.getCode());
            }
        }
        //判断电话是否已存在其他记录
        AccountInfo phoneAccount = this.getByPhone(accountInfoDto.getPhone());
        if (phoneAccount != null && phoneAccount.getId().longValue() != accountInfoDto.getId().longValue()) {
            throw new ServiceException("手机号码已存在");
        }
        String dbPwd = accountInfo.getPasswd();
        BeanUtils.copyProperties(accountInfoDto, accountInfo);
        accountInfo.setPasswd(dbPwd);
        this.updateById(accountInfo);

        if(isMainFlag){
            ShopContextHolder.setShopId(CommonConstants.DEFAULT_SHOP_ID);
        }
        List<Long> roleIds = accountInfoDto.getRoleIds();
        AuthUserRoleParamDto authUserRoleParamDto = new AuthUserRoleParamDto();
        authUserRoleParamDto.setUserId(accountInfo.getId());
        authUserRoleParamDto.setRoleIds(roleIds);
        authUserRoleService.saveAuthUserRole(authUserRoleParamDto);
        return accountInfo;
    }

    /**
     * 查询平台用户列表
     * @param paramDto
     * @return
     */
    @Override
    public PageUtils searchAccountInfo(PlatformAccountInfoParamDto paramDto){
        LambdaQueryWrapper<AccountInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(StrUtil.isNotBlank(paramDto.getNikeName()), AccountInfo::getNikeName, paramDto.getNikeName()).
                like(StrUtil.isNotBlank(paramDto.getPhone()), AccountInfo::getPhone, paramDto.getPhone()).
                eq(null!= paramDto.getForbidStatus(), AccountInfo::getForbidStatus, paramDto.getForbidStatus()).
                orderByDesc(AccountInfo::getId);
        IPage<AccountInfo> page = this.page(new Page<AccountInfo>(paramDto.getCurrent(), paramDto.getSize()), wrapper);
        return new PageUtils(page);
    }


    @Override
    public PageUtils<UserInfoVo> searchUserInfoVo(PlatformAccountInfoParamDto paramDto) {
        //是否为主店铺
        Boolean isMainFlag = false;
        String shopId = ShopContextHolder.getShopId();
        if(StringUtils.isNotEmpty(shopId)){
            ShopsPartner shopsPartner = remoteShopsService.getByShopId(Long.valueOf(shopId));
            if(shopsPartner!=null&&shopsPartner.getMainFlag()==1){
                isMainFlag = true;
            }
        }

        if(isMainFlag){
            ShopContextHolder.setShopId(CommonConstants.DEFAULT_SHOP_ID);
        }
        AccountInfo accountInfo = this.getById(CurUserUtil.getPcRqeustAccountInfo().getUserId());
        if(accountInfo.getAccountType()!=null&&accountInfo.getAccountType()==1){
            paramDto.setAccountType(accountInfo.getAccountType());
        }
        IPage<UserInfoVo>page = this.baseMapper.searchUserInfoVo(new Page<UserInfoVo>(paramDto.getCurrent(),paramDto.getSize()),paramDto);
        if(isMainFlag){
            ShopContextHolder.setShopId(shopId);
        }
        return new PageUtils(page);
    }


    /**
     * 重置平台用户的密码
     * @param accountInfoDto
     * @return
     */
    @Override
    public AccountInfo resetUserPwd(PlatformAccountInfoDto accountInfoDto){

        //是否为主店铺
        Boolean isMainFlag = false;
        String shopId = ShopContextHolder.getShopId();
        if(StringUtils.isNotEmpty(shopId)){
            ShopsPartner shopsPartner = remoteShopsService.getByShopId(Long.valueOf(shopId));
            if(shopsPartner!=null&&shopsPartner.getMainFlag()==1){
                isMainFlag = true;
            }
        }

        if(isMainFlag){
            ShopContextHolder.setShopId(CommonConstants.DEFAULT_SHOP_ID);
        }

        //判断用户是否存在
        AccountInfo accountInfo = this.getById(accountInfoDto.getId());
        if (accountInfo == null) {
            throw new ServiceException("用户不存在");
        }
        if(StrUtil.isBlank(accountInfoDto.getPasswd())){
            throw new ServiceException("用户密码不能为空");
        }
        removeAccountLogin(accountInfo);
        String salt = RandomUtil.randomString(6);
        accountInfo.setSalt(salt);
        accountInfo.setPasswd(SecureUtil.md5(accountInfoDto.getPasswd().concat(salt)));
        this.baseMapper.updateById(accountInfo);
        return accountInfo;
    }

    @Override
    public void deleteAccountInfo(PlatformAccountInfoDto accountInfoDto) {
        Long id = accountInfoDto.getId();
        if(id==null){
            throw new ServiceException("用户id不能为空");
        }
        LambdaQueryWrapper<AuthUserRole>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AuthUserRole::getUserId,id);
        List<AuthUserRole> list = authUserRoleService.list(wrapper);
        if(list!=null&&list.size()>0){
            throw new ServiceException("存在用户角色关系，不能删除！");
        }
        this.removeById(id);
    }

    @Override
    public List<AccountInfo> getAccountRegistrationId(String mobileType) {
        LambdaQueryWrapper<AccountInfo>wrapper = new LambdaQueryWrapper<>();
        wrapper.isNotNull(AccountInfo::getRegistrationId);
        wrapper.eq(AccountInfo::getMobileType,mobileType);
        List<AccountInfo> list = this.list(wrapper);
        return list;
    }

    @Override
    public StoreFrontVo getStoreFrontByAccountId(String id) {
        StoreFrontVo storeFrontVo = null;
        if(StringUtils.isEmpty(id)){
            throw new ServiceException("用户id不能为空！");
        }
        AccountInfo accountInfo = this.baseMapper.selectById(id);
        if(null == accountInfo){
            throw new ServiceException("用户不存在！");
        }

        if(accountInfo.getId()!=null){
            List<StoreFrontVo> list = storeFrontMapper.getStoreFrontVoByAccountId(accountInfo.getId()+"");
            if(list!=null&&list.size()>0){
                storeFrontVo = list.get(0);
            }
        }
        return storeFrontVo;
    }

    @Override
    @Transactional
    public void bindMiniAccount(BindMiniAccountDto dto) {
        String shopId = ShopContextHolder.getShopId();
        ShopContextHolder.setShopId(CommonConstants.DEFAULT_SHOP_ID);

        //用户id
        String accountId = dto.getAccountId();
        //小程序客户id
        String userId = dto.getUserId();
        //小程序客户名称
        String nikeName = dto.getNikeName();
        AccountInfo accountInfo = this.baseMapper.selectById(accountId);
        if(accountInfo!=null){
            accountInfo.setMiniAccountId(userId);
            accountInfo.setMiniAccountName(nikeName);
            this.baseMapper.updateById(accountInfo);
            //需要更新小程序会员表用户id字段，方便查询过滤已绑定小程序用户的客户
            Boolean result = remoteMiniAccountService.updateMiniAccount(userId,accountId);
            ShopContextHolder.setShopId(shopId);
            if(!result){
                throw new ServiceException("更新小程序会员失败！");
            }
        }else{
            ShopContextHolder.setShopId(shopId);
            throw new ServiceException("用户不存在！");
        }

    }

    @Override
    public void accountBindStoreFront(AccountBindStoreFrontDto dto) {
        String shopId = ShopContextHolder.getShopId();
        ShopContextHolder.setShopId(CommonConstants.DEFAULT_SHOP_ID);
        //用户id
        String accountId = dto.getAccountId();
        //门店标识
        String storeFrontCode = dto.getStoreFrontCode();
        //门店名称
        String storeFrontName = dto.getStoreFrontName();
        AccountInfo accountInfo = this.baseMapper.selectById(accountId);
        if(accountInfo!=null){
            accountInfo.setStoreFrontCode(storeFrontCode);
            accountInfo.setStoreFrontName(storeFrontName);
            this.baseMapper.updateById(accountInfo);
            ShopContextHolder.setShopId(shopId);
        }else{
            ShopContextHolder.setShopId(shopId);
            throw new ServiceException("用户不存在！");
        }
    }

    @Override
    public void accountBindEmployee(AccountBindEmployeeDto dto) {
        String shopId = ShopContextHolder.getShopId();
        ShopContextHolder.setShopId(CommonConstants.DEFAULT_SHOP_ID);
        //用户id
        String accountId = dto.getAccountId();
        //部门标识
        String departmentCode = dto.getDepartmentCode();
        //部门名称
        String departmentName = dto.getDepartmentName();
        //职员id
        String employeeId = dto.getEmployeeId();
        //职员名称
        String employeeName = dto.getEmployeeName();
        AccountInfo accountInfo = this.baseMapper.selectById(accountId);
        if(accountInfo!=null){
            accountInfo.setDepartmentCode(departmentCode);
            accountInfo.setDepartmentName(departmentName);
            accountInfo.setEmployeeId(employeeId);
            accountInfo.setEmployeeName(employeeName);
            this.baseMapper.updateById(accountInfo);
            ShopContextHolder.setShopId(shopId);
        }else{
            ShopContextHolder.setShopId(shopId);
            throw new ServiceException("用户不存在！");
        }
    }

    @Override
    public void accountBindWarehouse(AccountBindWarehouseDto dto) {
        String shopId = ShopContextHolder.getShopId();
        ShopContextHolder.setShopId(CommonConstants.DEFAULT_SHOP_ID);
        //用户id
        String accountId = dto.getAccountId();
        //仓库标识
        String stockCode = dto.getStockCode();
        //仓库名称
        String stockName = dto.getStockName();
        AccountInfo accountInfo = this.baseMapper.selectById(accountId);
        if(accountInfo!=null){
            accountInfo.setStockCode(stockCode);
            accountInfo.setStockName(stockName);
            this.baseMapper.updateById(accountInfo);
            ShopContextHolder.setShopId(shopId);
        }else{
            ShopContextHolder.setShopId(shopId);
            throw new ServiceException("用户不存在！");
        }
    }

    @Override
    @Transactional
    public void settingDefaultDepartment(String accountId) {
        String shopId = ShopContextHolder.getShopId();
        ShopContextHolder.setShopId(CommonConstants.DEFAULT_SHOP_ID);
        if(StringUtils.isNotEmpty(accountId)){
            AccountInfo accountInfo = this.baseMapper.selectById(accountId);
            if(accountInfo!=null){
                String departmentCode = accountInfo.getDepartmentCode();
                String storeFrontCode = accountInfo.getStoreFrontCode();
                if(StringUtils.isEmpty(departmentCode)){
                    ShopContextHolder.setShopId(shopId);
                    throw new ServiceException("用户需要先关联部门");
                }
                if(StringUtils.isEmpty(storeFrontCode)){
                    ShopContextHolder.setShopId(shopId);
                    throw new ServiceException("用户需要先关联门店");
                }
                LambdaQueryWrapper<PlatformDepartment>wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(PlatformDepartment::getClassCode,departmentCode);
                List<PlatformDepartment> list = departmentService.list(wrapper);
                if(list!=null&&list.size()>0){
                    PlatformDepartment platformDepartment = list.get(0);
                    departmentService.setDefault(platformDepartment.getId(),accountInfo.getEmployeeId(),accountInfo.getEmployeeName());
                    ShopContextHolder.setShopId(shopId);
                }else{
                    ShopContextHolder.setShopId(shopId);
                    throw new ServiceException("部门不存在");
                }

            }else{
                ShopContextHolder.setShopId(shopId);
                throw new ServiceException("用户不存在");
            }
        }else{
            ShopContextHolder.setShopId(shopId);
            throw new ServiceException("用户id不能为空");
        }
    }

    @Override
    @Transactional
    public void settingDefaultStoreFront(String accountId) {
        String shopId = ShopContextHolder.getShopId();
        ShopContextHolder.setShopId(CommonConstants.DEFAULT_SHOP_ID);
        if(StringUtils.isNotEmpty(accountId)){
            AccountInfo accountInfo = this.baseMapper.selectById(accountId);
            if(accountInfo!=null){
                String storeFrontCode = accountInfo.getStoreFrontCode();
                if(StringUtils.isEmpty(storeFrontCode)){
                    ShopContextHolder.setShopId(shopId);
                    throw new ServiceException("用户需要先关联门店");
                }
                LambdaQueryWrapper<PlatformStoreFront>wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(PlatformStoreFront::getClassCode,storeFrontCode);
                List<PlatformStoreFront> list = storeFrontService.list(wrapper);
                if(list!=null&&list.size()>0){
                    PlatformStoreFront platformStoreFront = list.get(0);
                    platformStoreFront.setAccountId(accountInfo.getId()+"");
                    platformStoreFront.setAccountName(accountInfo.getNikeName());
                    storeFrontService.updateById(platformStoreFront);
                    ShopContextHolder.setShopId(shopId);
                }else{
                    ShopContextHolder.setShopId(shopId);
                    throw new ServiceException("门店不存在");
                }
            }else{
                ShopContextHolder.setShopId(shopId);
                throw new ServiceException("用户不存在");
            }
        }else{
            ShopContextHolder.setShopId(shopId);
            throw new ServiceException("用户id不能为空");
        }
    }

    @Override
    public RelationInfoVo getRelationInfoByMiniAccountId(String userId) {
        String shopId = ShopContextHolder.getShopId();
        ShopContextHolder.setShopId(CommonConstants.DEFAULT_SHOP_ID);
        RelationInfoVo relationInfoVo = new RelationInfoVo();
        LambdaQueryWrapper<AccountInfo>lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(AccountInfo::getMiniAccountId,userId);
        List<AccountInfo> list = this.list(lambdaQueryWrapper);
        if(list!=null&&list.size()>0){
            AccountInfo accountInfo = list.get(0);
            handleRelationInfo(accountInfo,relationInfoVo);
        }
        ShopContextHolder.setShopId(shopId);
        return relationInfoVo;
    }

    @Override
    public RelationInfoVo getRelationInfoDefaultDepartment() {
        String shopId = ShopContextHolder.getShopId();
        ShopContextHolder.setShopId(CommonConstants.DEFAULT_SHOP_ID);
        RelationInfoVo relationInfoVo = new RelationInfoVo();
        LambdaQueryWrapper<PlatformDepartment>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PlatformDepartment::getDefaultFlag,CommonConstants.NUMBER_ONE);
        List<PlatformDepartment> departmentList = departmentMapper.selectList(wrapper);
        if(departmentList!=null&&departmentList.size()>0){
            PlatformDepartment platformDepartment = departmentList.get(0);
            if(StringUtils.isNotEmpty(platformDepartment.getEmployeeId())){
                LambdaQueryWrapper<AccountInfo>accountInfoLambdaQueryWrapper = new LambdaQueryWrapper<>();
                accountInfoLambdaQueryWrapper.eq(AccountInfo::getEmployeeId,platformDepartment.getEmployeeId());
                accountInfoLambdaQueryWrapper.eq(AccountInfo::getDepartmentCode,platformDepartment.getClassCode());
                List<AccountInfo> accountInfoList = this.list(accountInfoLambdaQueryWrapper);
                if(accountInfoList!=null&&accountInfoList.size()>0){
                    AccountInfo accountInfo = accountInfoList.get(0);
                    handleRelationInfo(accountInfo,relationInfoVo);
                }
            }
        }
        ShopContextHolder.setShopId(shopId);
        return relationInfoVo;
    }

    @Override
    public RelationInfoVo getRelationInfoByStoreFrontId(String storeFrontId) {
        RelationInfoVo relationInfoVo = new RelationInfoVo();
        PlatformStoreFront platformStoreFront = storeFrontMapper.selectById(storeFrontId);
        if(platformStoreFront!=null){
            relationInfoVo.setStoreFrontId(platformStoreFront.getId()+"");
            relationInfoVo.setStoreFrontCode(platformStoreFront.getClassCode());
            relationInfoVo.setStoreFrontName(platformStoreFront.getStoreFullName());
            if(StringUtils.isNotEmpty(platformStoreFront.getAccountId())){
                AccountInfo accountInfo = this.baseMapper.selectById(platformStoreFront.getAccountId());
                handleRelationInfo(accountInfo,relationInfoVo);
            }
        }
        return relationInfoVo;
    }

    @Override
    public RelationInfoVo getRelationInfoByAccountId(String accountId) {
        RelationInfoVo relationInfoVo = new RelationInfoVo();
        AccountInfo accountInfo = this.baseMapper.selectById(accountId);
        handleRelationInfo(accountInfo,relationInfoVo);
        return relationInfoVo;
    }

    /**
     * 处理关联数据
     * @param accountInfo
     * @param relationInfoVo
     */
    private void handleRelationInfo(AccountInfo accountInfo,RelationInfoVo relationInfoVo){
        relationInfoVo.setShopId(accountInfo.getShopId());
        relationInfoVo.setAccountId(accountInfo.getId()+"");//用户id
        relationInfoVo.setAccountName(accountInfo.getNikeName());//用户名称
        relationInfoVo.setMiniAccountId(accountInfo.getMiniAccountId());//小程序用户id
        if(StringUtils.isNotEmpty(accountInfo.getEmployeeId())){//职员
            LambdaQueryWrapper<PlatformEmployee>employeeLambdaQueryWrapper = new LambdaQueryWrapper<>();
            employeeLambdaQueryWrapper.eq(PlatformEmployee::getOutId,accountInfo.getEmployeeId());
            List<PlatformEmployee> employeeList = employeeMapper.selectList(employeeLambdaQueryWrapper);
            if(employeeList!=null&&employeeList.size()>0){
                PlatformEmployee platformEmployee = employeeList.get(0);
                relationInfoVo.setEmployeeId(platformEmployee.getId()+"");//职员id
                relationInfoVo.setEmployeeOutId(platformEmployee.getOutId());//职员标识
                relationInfoVo.setEmployeeName(platformEmployee.getEmpFullName());//职员名称
            }
        }
        if(StringUtils.isNotEmpty(accountInfo.getDepartmentCode())){//部门
            LambdaQueryWrapper<PlatformDepartment>departmentLambdaQueryWrapper = new LambdaQueryWrapper<>();
            departmentLambdaQueryWrapper.eq(PlatformDepartment::getClassCode,accountInfo.getDepartmentCode());
            List<PlatformDepartment> departmentList = departmentMapper.selectList(departmentLambdaQueryWrapper);
            if(departmentList!=null&&departmentList.size()>0){
                PlatformDepartment platformDepartment = departmentList.get(0);
                relationInfoVo.setDepartmentId(platformDepartment.getId()+"");//部门id
                relationInfoVo.setDepartmentCode(platformDepartment.getClassCode());//部门标识
                relationInfoVo.setDepartmentName(platformDepartment.getDeptFullName());//部门名称
            }
        }
        if(StringUtils.isNotEmpty(accountInfo.getStoreFrontCode())){//门店
            LambdaQueryWrapper<PlatformStoreFront>storeFrontLambdaQueryWrapper = new LambdaQueryWrapper<>();
            storeFrontLambdaQueryWrapper.eq(PlatformStoreFront::getClassCode,accountInfo.getStoreFrontCode());
            storeFrontLambdaQueryWrapper.eq(PlatformStoreFront::getShopId,accountInfo.getShopId());
            List<PlatformStoreFront> storeFrontList = storeFrontMapper.selectList(storeFrontLambdaQueryWrapper);
            if(storeFrontList!=null&&storeFrontList.size()>0){
                PlatformStoreFront platformStoreFront = storeFrontList.get(0);
                relationInfoVo.setStoreFrontId(platformStoreFront.getId()+"");//门店id
                relationInfoVo.setStoreFrontCode(platformStoreFront.getClassCode());//门店标识
                relationInfoVo.setStoreFrontName(platformStoreFront.getStoreFullName());//门店名称
            }
        }
        if(StringUtils.isNotEmpty(accountInfo.getStockCode())){//仓库
            List<Warehouse> warehouseList = remoteGoodsService.getWarehouseByClassCode(accountInfo.getStockCode());
            if(warehouseList!=null&&warehouseList.size()>0){
                Warehouse warehouse = warehouseList.get(0);
                relationInfoVo.setStockId(warehouse.getId()+"");//仓库id
                relationInfoVo.setStockCode(warehouse.getClassCode());//仓库标识
                relationInfoVo.setStockName(warehouse.getWarehouseFullName());//仓库名称
            }
        }
    }

    private AccountInfo getMpInfo(AccountInfo accountInfo, WxMpOAuth2AccessToken wxMpOauth2AccessToken, String appId) throws WxErrorException {
        WxMpUser wxMpUser = wxMpService.switchoverTo(appId).oauth2getUserInfo(wxMpOauth2AccessToken, "zh_CN");
        accountInfo.setRefreshToken(wxMpOauth2AccessToken.getRefreshToken());
        accountInfo.setAccessToken(wxMpOauth2AccessToken.getAccessToken());
        accountInfo.setAccessExpiresTime(DateUtils.timestampCoverLocalDateTime(wxMpOauth2AccessToken.getExpiresIn()));
        accountInfo.setOpenId(wxMpOauth2AccessToken.getOpenId());
        accountInfo.setCity(wxMpUser.getCity());
        accountInfo.setLanguage(wxMpUser.getLanguage());
        accountInfo.setNikeName(wxMpUser.getNickname());
        accountInfo.setAvatarUrl(wxMpUser.getHeadImgUrl());
        accountInfo.setGender(wxMpUser.getSex());
        accountInfo.setUnionId(StrUtil.isNotEmpty(wxMpUser.getUnionId()) ? wxMpUser.getUnionId() : null);
        accountInfo.setProvince(wxMpUser.getProvince());
        accountInfo.setCountry(wxMpUser.getCountry());
        accountInfo.setPrivilege(JSON.toJSONString(wxMpUser.getPrivileges()));
        return accountInfo;
    }

    private AccountInfo getByPhone(String username) {
        if (!ReUtil.isMatch(RegexConstants.REGEX_MOBILE_EXACT, username)) {
            throw new ServiceException("手机号错误", SystemCode.DATA_NOT_EXIST.getCode());
        }
        return this.baseMapper.selectOne(new QueryWrapper<AccountInfo>().eq("phone", username));
    }

    private AccountInfo getByPhone(String username, String tenantId) {
        if (!ReUtil.isMatch(RegexConstants.REGEX_MOBILE_EXACT, username)) {
            throw new ServiceException("手机号错误", SystemCode.DATA_NOT_EXIST.getCode());
        }
        return this.baseMapper.selectOne(new QueryWrapper<AccountInfo>().eq("phone", username).eq("tenant_id", tenantId));
    }
}
