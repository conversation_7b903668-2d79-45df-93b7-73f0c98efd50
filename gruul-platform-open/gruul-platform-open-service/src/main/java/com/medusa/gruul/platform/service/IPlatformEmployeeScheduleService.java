package com.medusa.gruul.platform.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.medusa.gruul.common.core.util.PageUtils;
import com.medusa.gruul.platform.api.entity.PlatformEmployeeSchedule;
import com.medusa.gruul.platform.api.model.vo.EmployeeScheduleVo;
import com.medusa.gruul.platform.model.dto.EmployeeScheduleDto;
import com.medusa.gruul.platform.model.param.EmployeeScheduleParam;

import java.time.LocalDate;
import java.util.List;

/**
 * 职员排班Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-25
 */
public interface IPlatformEmployeeScheduleService extends IService<PlatformEmployeeSchedule> {

    /**
     * 分页查询职员排班信息
     * 
     * @param param 查询参数
     * @return 排班信息分页列表
     */
    PageUtils<EmployeeScheduleVo> searchEmployeeSchedule(EmployeeScheduleParam param);

    /**
     * 新增排班记录
     * 
     * @param dto 排班信息
     * @return 是否成功
     */
    boolean addSchedule(EmployeeScheduleDto dto);

    /**
     * 修改排班记录
     * 
     * @param dto 排班信息
     * @return 是否成功
     */
    boolean updateSchedule(EmployeeScheduleDto dto);

    /**
     * 设置排班记录
     *
     * @param dto 排班信息
     * @return 是否成功
     */
    boolean setSchedule(EmployeeScheduleDto dto);
    /**
     * 删除排班记录
     * 
     * @param id 排班记录ID
     * @return 是否成功
     */
    boolean deleteSchedule(Long id);


}
