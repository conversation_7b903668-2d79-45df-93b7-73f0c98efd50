package com.medusa.gruul.account.model.dto;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author: plh
 * @Description: 佣金提现 dto
 * @Date: Created in 10:18 2023/8/31
 */
@Data
public class MiniAccountCommissionCashDto{
    /**
     * 佣金
     */
    @ApiModelProperty(value = "佣金")
    @NotNull(message = "佣金不能为空")
    private BigDecimal amount;

    /**
     * 支付方式：102-微信支付，1001线下支付
     */
    @ApiModelProperty(value = "支付方式：102-微信支付，1001线下支付")
    private Integer payType;

    /**
     * 线下支付类型：300-银行卡，301-微信收款码，302-支付宝收款
     */
    @ApiModelProperty(value = "线下支付类型：300-银行卡，301-微信收款码，302-支付宝收款")
    private Integer offlinePayType;
    /**
     * 用户银行卡id
     */
    @ApiModelProperty(value = "用户银行卡id")
    private Long accountBankId;


    /**
     * 小程序用户id（实际存的是用户信息扩展表的shop_user_id）
     */
    @ApiModelProperty(value = "小程序用户id")
    private String userId;

    /**
     * 后台备注
     */
    @ApiModelProperty(value = "后台备注")
    private String platformRemark;

    /**
     * 数据来源，0-系统，1-后台
     */
    @ApiModelProperty(value = "数据来源，0-系统，1-后台")
    private Integer source;

    /**
     * 平台操作用户id
     */
    @ApiModelProperty(value = "平台操作用户id")
    private String platformUserId;

    /**
     * 平台操作用户名
     */
    @ApiModelProperty(value = "平台操作用户名")
    private String platformUserName;

}
