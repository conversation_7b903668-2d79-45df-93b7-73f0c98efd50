package com.medusa.gruul.platform.model.param;

import com.medusa.gruul.common.core.param.QueryParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;

/**
 * 职员排班查询参数
 * 
 * <AUTHOR>
 * @date 2025-01-25
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "EmployeeScheduleParam对象", description = "职员排班查询参数")
@Data
public class EmployeeScheduleParam extends QueryParam {

    @ApiModelProperty(value = "职员ID")
    private Long employeeId;

    @ApiModelProperty(value = "职员编号")
    private String empNumber;

    @ApiModelProperty(value = "职员姓名")
    private String empFullName;

    @ApiModelProperty(value = "排班开始日期")
    private LocalDate startDate;

    @ApiModelProperty(value = "排班结束日期")
    private LocalDate endDate;

    @ApiModelProperty(value = "工作状态：1-上班，2-休息，3-请假")
    private Integer workStatus;

    @ApiModelProperty(value = "班次：1-上午，2-下午，3-晚上")
    private Integer shiftType;

    @ApiModelProperty(value = "门店ID")
    private String storeFrontId;
}
