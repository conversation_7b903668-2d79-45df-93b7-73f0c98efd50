package com.medusa.gruul.platform.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.medusa.gruul.platform.api.entity.WxMessageTemplate;
import com.medusa.gruul.platform.model.param.WxMessageTemplateParam;
import com.medusa.gruul.platform.api.model.vo.WxMessageTemplateVo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 15:31 2024/11/6
 */
@Repository
public interface WxMessageTemplateMapper extends BaseMapper<WxMessageTemplate> {

    /**
     * 分页获取消息模板信息
     * @param param
     * @return
     */
    IPage<WxMessageTemplateVo> getWxMessageTemplate(Page<WxMessageTemplateVo> page, @Param("param")WxMessageTemplateParam param);
}
