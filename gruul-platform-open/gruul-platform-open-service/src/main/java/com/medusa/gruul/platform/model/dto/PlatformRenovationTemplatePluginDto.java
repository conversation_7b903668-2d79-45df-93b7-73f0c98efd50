package com.medusa.gruul.platform.model.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.medusa.gruul.common.data.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;


/**
 * @Description: 平台装修模板全局控件属性表
 * @Author: jeecg-boot
 * @Date:   2023-09-06
 * @Version: V1.0
 */
@Data
@ApiModel(value="PlatformRenovationTemplatePluginDto对象", description="平台装修模板全局控件属性表")
public class PlatformRenovationTemplatePluginDto {
    
	/**id*/
    @ApiModelProperty(value = "id")
	private Integer id;

	/**平台装修模板ID*/
    @ApiModelProperty(value = "平台装修模板ID")
	private Integer templateId;
	/**控件名称中文*/
    @ApiModelProperty(value = "控件名称中文")
	private String pluginNameCn;
	/**控件名称英文*/
    @ApiModelProperty(value = "控件名称英文")
	private String pluginNameEn;
	/**是否允许取消 0否 1是*/
    @ApiModelProperty(value = "是否允许取消 0否 1是")
	private String isMandatory;
	/**是否选中 0否 1是*/
    @ApiModelProperty(value = "是否选中 0否 1是")
	private String isSelection;
	/**控件*/
    @ApiModelProperty(value = "控件")
	private String pluginProperties;
}
