package com.medusa.gruul.account.model.param;

import com.medusa.gruul.common.core.param.QueryParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @Author: plh
 * @Description: 用户积分查询参数
 * @Date: Created in 18:25 2023/8/22
 */
@Data
@ApiModel(value = "MiniAccountIntegralParam 实体", description = "用户积分查询 param")
public class MiniAccountIntegralParam extends QueryParam {

    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id")
    private String userId;


    /**
     * 积分类型：1.普通积分收入；2.普通积分支出；3.销售积分收入；4.销售积分支出
     */
    @ApiModelProperty(value = "积分类型：1.普通积分收入；2.普通积分支出；3.销售积分收入；4.销售积分支出")
    private Integer type;


}
