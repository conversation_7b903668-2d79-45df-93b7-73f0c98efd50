package com.medusa.gruul.platform.model.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 15:31 2024/10/29
 */
@Data
@ApiModel(value="AccountBindWarehouseDto对象", description="用户绑定仓库dto")
public class AccountBindWarehouseDto {

    @ApiModelProperty(value = "用户id")
    @NotBlank(message = "用户id不能为空")
    private String accountId;

    @ApiModelProperty(value = "仓库标识")
    @NotBlank(message = "仓库标识不能为空")
    private String stockCode;

    @ApiModelProperty(value = "仓库名称")
    @NotBlank(message = "仓库名称不能为空")
    private String stockName;


}
