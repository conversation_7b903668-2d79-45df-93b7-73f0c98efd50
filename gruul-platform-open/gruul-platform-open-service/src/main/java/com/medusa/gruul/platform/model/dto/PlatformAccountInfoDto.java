package com.medusa.gruul.platform.model.dto;


import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 平台用户信息
 */
@Data
public class PlatformAccountInfoDto {

    @ApiModelProperty(value = "用户id")
    private Long id;

    @ApiModelProperty(value = "用户名称")
    @NotBlank(message = "用户名不能为空")
    private String nikeName;

    @NotBlank(message = "手机号不能为空")
    @ApiModelProperty(value = "手机号码")
    private String phone;

    @ApiModelProperty(value = "token")
    private String token;

    /**
     * 账号状态  0-正常  1-禁用
     */
    @ApiModelProperty(value = "账号状态")
    private Integer forbidStatus;

    /**
     * 账号类型  0-商户账号, 1-店铺名下账号
     */
    @ApiModelProperty(value = "账号类型")
    private Integer accountType;

    /**
     * 账号登录密码
     */
    @ApiModelProperty(value = "账号登录密码")
    private String passwd;

    /**
     * 店铺id
     */
    @NotBlank(message = "店铺id不能为空")
    @ApiModelProperty(value = "店铺id")
    private String shopId;
    /**
     * 角色id
     */
    @ApiModelProperty(value = "角色id")
    private List<Long> roleIds;


    @ApiModelProperty(value = "职员id")
    private String employeeId;

}
