package com.medusa.gruul.payment.task;

import com.medusa.gruul.payment.service.IPaymentService;
import com.medusa.gruul.payment.service.IPaymentSharingAccountOrderService;
import com.medusa.gruul.payment.service.IWxTransferV3Service;
import com.medusa.gruul.payment.service.PaymentRefundService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 14:41 2025/4/23
 */
@Component
@Slf4j
public class PaymentXxlJob {

    @Autowired
    private IWxTransferV3Service wxTransferV3Service;
    @Autowired
    private IPaymentService paymentService;
    @Autowired
    private IPaymentSharingAccountOrderService paymentSharingAccountOrderService;


    @XxlJob("updateTransfersStatus")
    public ReturnT<String> updateTransfersStatus(String param) throws Exception {
        log.info("-----------更新微信转零钱订单状态定时任务开启-----------");
        wxTransferV3Service.updateTransfersStatus();
        log.info("-----------更新微信转零钱订单状态定时任务结束-----------");
        return ReturnT.SUCCESS;
    }

    @XxlJob("updatePaymentStatus")
    public ReturnT<String> updatePaymentStatus(String param) throws Exception {
        log.info("-----------更新聚合支付状态定时任务开始-----------");
        paymentService.updatePaymentStatus();
        log.info("-----------更新聚合支付状态定时任务结束-----------");
        return ReturnT.SUCCESS;
    }

    @XxlJob("updateReturnPaymentHandleStatus")
    public ReturnT<String> updateReturnPaymentHandleStatus(String param) throws Exception {
        log.info("-----------更新聚合支付退款状态（退款中）定时任务开始-----------");
        paymentService.updateReturnPaymentHandleStatus();
        log.info("-----------更新聚合支付退款状态（退款中）定时任务结束-----------");
        return ReturnT.SUCCESS;
    }

    @XxlJob("updateReturnPaymentFailStatus")
    public ReturnT<String> updateReturnPaymentFailStatus(String param) throws Exception {
        log.info("-----------更新聚合支付退款状态（失败）定时任务开始-----------");
        paymentService.updateReturnPaymentFailStatus();
        log.info("-----------更新聚合支付退款状态（失败）定时任务结束-----------");
        return ReturnT.SUCCESS;
    }

    @XxlJob("querySharingPayCreateStatus")
    public ReturnT<String> querySharingPayCreateStatus() throws Exception {
        log.info("-----------查询聚合支付分账订单状态定时任务开始-----------");
        paymentSharingAccountOrderService.queryProcessingAccountOrderByApi();
        log.info("-----------查询聚合支付分账订单状态定时任务结束-----------");
        return ReturnT.SUCCESS;
    }

}
