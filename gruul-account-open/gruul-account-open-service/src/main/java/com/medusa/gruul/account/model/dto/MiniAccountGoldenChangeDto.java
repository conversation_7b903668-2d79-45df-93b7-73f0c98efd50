package com.medusa.gruul.account.model.dto;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 15:33 2025/7/21
 */
@Data
@ApiModel(value = "新增或修改用户金豆变更DTO")
public class MiniAccountGoldenChangeDto {


    /**
     * id
     */
    @ApiModelProperty(value = "id")
    private Long id;

    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id")
    @NotNull(message = "用户id不能为空")
    private String userId;

    /**
     * 变更方式：1.增加；2.减少
     */
    @ApiModelProperty(value = "变更方式：1.增加；2.减少")
    @NotNull(message = "变更方式不能为空")
    private Integer type;

    /**
     * 金额
     */
    @ApiModelProperty(value = "金额")
    @NotNull(message = "金额不能为空")
    private BigDecimal amount;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @NotNull(message = "备注不能为空")
    private String remark;
}
