package com.medusa.gruul.platform.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 17:13 2024/12/26
 */
@Data
@ApiModel(value="PlatformStoreFrontDto对象", description="门店dto")
public class PlatformStoreFrontDto {

    @ApiModelProperty(value = "门店id")
    private Long id;

    @ApiModelProperty(value = "上级classCode")
    private String parentCode;

    @ApiModelProperty(value = "门店编号")
    private String storeNumber;

    @ApiModelProperty(value = "门店名称")
    private String storeFullName;

    @ApiModelProperty(value = "状态")
    private String statusId;

}
