package com.medusa.gruul.platform.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 预约提交DTO
 *
 * <AUTHOR>
 * @since 2025-06-10
 */
@Data
@ApiModel(value = "预约提交DTO", description = "预约提交DTO")
public class ReservationSubmitDto implements Serializable {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "shopId")
    private String shopId;

    @ApiModelProperty(value = "用户Id", required = true)
    private Long userId;

    @ApiModelProperty(value = "用户名称", required = true)
    private String userName;

    /**
     * 手机号
     */
    @NotBlank(message = "手机号不能为空")
    @ApiModelProperty(value = "手机号", required = true)
    private String mobile;

    /**
     * 预约人数
     */
    @NotNull(message = "预约人数不能为空")
    @Min(value = 1, message = "预约人数必须大于等于1")
    @ApiModelProperty(value = "预约人数", required = true)
    private Integer reservationNumber;

    /**
     * 预约时间开始
     */
    @NotNull(message = "预约时间开始不能为空")
    @ApiModelProperty(value = "预约时间开始", required = true)
    private LocalDateTime reservationTimeStart;

    /**
     * 预约时间结束
     */
    @NotNull(message = "预约时间结束不能为空")
    @ApiModelProperty(value = "预约时间结束", required = true)
    private LocalDateTime reservationTimeEnd;

    /**
     * 预约项目ID
     */
    @NotNull(message = "预约项目ID不能为空")
    @ApiModelProperty(value = "预约项目ID", required = true)
    private Long serviceId;

    /**
     * 预约职员ID，可为空表示到店分配
     */
    @ApiModelProperty(value = "预约职员ID，可为空表示到店分配")
    private Long employeeId;

    /**
     * 预约内容
     */
    @ApiModelProperty(value = "预约内容")
    private String content;
} 