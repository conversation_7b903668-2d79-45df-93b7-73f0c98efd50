package com.medusa.gruul.platform.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 15:47 2024/11/12
 */
@Data
@ApiModel(value="ApiWxMiniTemplateDto对象", description="订阅消息显示")
public class ApiWxMiniTemplateDto {

    /**
     * 是否显示
     */
    @ApiModelProperty(value = "是否显示")
    private Boolean showFlag;

    /**
     * 模板id
     */
    @ApiModelProperty(value = "模板id")
    private List<String> templateIds;

}
