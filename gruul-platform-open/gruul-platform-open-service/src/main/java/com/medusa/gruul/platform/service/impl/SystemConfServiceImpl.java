package com.medusa.gruul.platform.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.mail.MailAccount;
import cn.hutool.extra.mail.MailUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alipay.api.AlipayApiException;
import com.alipay.api.domain.AlipayTradePrecreateModel;
import com.alipay.api.response.AlipayTradePrecreateResponse;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.binarywang.wxpay.bean.request.BaseWxPayRequest;
import com.github.binarywang.wxpay.bean.request.WxPayUnifiedOrderRequest;
import com.github.binarywang.wxpay.bean.result.WxPayUnifiedOrderResult;
import com.github.binarywang.wxpay.config.WxPayConfig;
import com.github.binarywang.wxpay.constant.WxPayConstants;
import com.github.binarywang.wxpay.exception.WxPayException;
import com.github.binarywang.wxpay.service.WxPayService;
import com.github.binarywang.wxpay.service.impl.WxPayServiceApacheHttpImpl;
import com.ijpay.alipay.AliPayApi;
import com.ijpay.alipay.AliPayApiConfig;
import com.ijpay.alipay.AliPayApiConfigKit;
import com.medusa.gruul.common.core.constant.CommonConstants;
import com.medusa.gruul.common.core.constant.enums.CloudServiceEnum;
import com.medusa.gruul.common.core.exception.ServiceException;
import com.medusa.gruul.common.core.util.Result;
import com.medusa.gruul.common.core.util.SystemCode;
import com.medusa.gruul.platform.api.entity.SystemConf;
import com.medusa.gruul.platform.api.model.dto.OssConfigDto;
import com.medusa.gruul.platform.api.model.dto.YDERPConfigDto;
import com.medusa.gruul.platform.conf.*;
import com.medusa.gruul.platform.mapper.SystemConfMapper;
import com.medusa.gruul.platform.model.dto.*;
import com.medusa.gruul.platform.model.vo.CodeEmailConfVo;
import com.medusa.gruul.platform.model.vo.KfmsgVo;
import com.medusa.gruul.platform.model.vo.PayConfigVo;
import com.medusa.gruul.platform.model.vo.SystemConfigVo;
import com.medusa.gruul.platform.service.ISystemConfService;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.api.WxMpTemplateMsgService;
import me.chanjar.weixin.mp.bean.template.WxMpTemplateData;
import me.chanjar.weixin.mp.bean.template.WxMpTemplateMessage;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;


/**
 * <p>
 * 系统配置 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-09-20
 */
@Slf4j
@Service
public class SystemConfServiceImpl extends ServiceImpl<SystemConfMapper, SystemConf> implements ISystemConfService {

    @Autowired
    @Lazy
    private WxMpService wxMpService;

    @Override
    public void saveValue(SaveConfigDto saveConfigDto) {

        Integer type = saveConfigDto.getType();
        SystemConf systemConf = new SystemConf();
        String paramKey = "";
        //0-系统配置 1：七牛  2：阿里云  3：腾讯云  4:本地存储
        switch (type) {
            case 0:
                systemConf.setParamValue(JSONObject.toJSONString(saveConfigDto.getSystemConfig()));
                paramKey = SystemConstant.SERVER_CONFIG;
                break;
            case 1:
                systemConf.setParamValue(JSONObject.toJSONString(saveConfigDto.getQiniouStorageConfig()));
                paramKey = SystemConstant.STORAGE_QINIOUYUN;
                break;
            case 2:
                systemConf.setParamValue(JSONObject.toJSONString(saveConfigDto.getAliyunStorageConfig()));
                paramKey = SystemConstant.STORAGE_ALIYUN;
                break;
            case 3:
                systemConf.setParamValue(JSONObject.toJSONString(saveConfigDto.getTencentStorageConfig()));
                paramKey = SystemConstant.STORAGE_TENCENT_CLOUD;
                break;
            case 4:
                systemConf.setParamValue(JSONObject.toJSONString(saveConfigDto.getLocalStorageConfig()));
                paramKey = SystemConstant.STORAGE_LOCAL_CLOUD;
                break;
            default:
                throw new ServiceException("数据修改异常", SystemCode.DATA_UPDATE_FAILED.getCode());
        }
        SystemConf ossConf = this.getByParamKey(SystemConstant.CURRENT_OSS_TYPE);
        if (this.baseMapper.update(systemConf, new QueryWrapper<SystemConf>().eq(MeConstant.PARAM_KEY, paramKey)) == 0) {
            systemConf.setParamKey(paramKey);
            this.baseMapper.insert(systemConf);
        }
        if (ossConf == null) {
            ossConf = new SystemConf();
            ossConf.setParamKey(SystemConstant.CURRENT_OSS_TYPE);
        }
        ossConf.setParamValue(type.toString());
        this.saveOrUpdate(ossConf);
    }

    @Override
    public SystemConfigVo getTypeInfo(Integer type) {
        String paramKey = "";
        if (type.equals(0)) {
            paramKey = SystemConstant.SERVER_CONFIG;
        } else if (type.equals(CloudServiceEnum.QINIU.getType())) {
            paramKey = SystemConstant.STORAGE_QINIOUYUN;
        } else if (type.equals(CloudServiceEnum.ALIYUN.getType())) {
            paramKey = SystemConstant.STORAGE_ALIYUN;
        } else if (type.equals(CloudServiceEnum.QCLOUD.getType())) {
            paramKey = SystemConstant.STORAGE_TENCENT_CLOUD;
        } else if (type.equals(CloudServiceEnum.LOCAL.getType())) {
            paramKey = SystemConstant.STORAGE_LOCAL_CLOUD;
        } else {
            throw new ServiceException("数据获取异常", SystemCode.DATA_NOT_EXIST.getCode());
        }
        SystemConfigVo systemConfigVo = new SystemConfigVo();
        SystemConf systemConf = this.getByParamKey(paramKey);
        if (systemConf == null) {
            return systemConfigVo;
        }
        if (type == 0) {
            SystemConfig systemConfig = JSONObject.parseObject(systemConf.getParamValue(), SystemConfig.class);
            systemConfigVo.setSystemConfig(systemConfig);
        } else {
            if (type.equals(CloudServiceEnum.QINIU.getType())) {
                QiniouStorageConfig qiniouStorageConfig = JSONObject.parseObject(systemConf.getParamValue(), QiniouStorageConfig.class);
                systemConfigVo.setQiniouStorageConfig(qiniouStorageConfig);
            } else if (type.equals(CloudServiceEnum.ALIYUN.getType())) {
                AliyunStorageConfig aliyunStorageConfig = JSONObject.parseObject(systemConf.getParamValue(), AliyunStorageConfig.class);
                systemConfigVo.setAliyunStorageConfig(aliyunStorageConfig);
            } else if (type.equals(CloudServiceEnum.QCLOUD.getType())) {
                TencentStorageConfig tencentStorageConfig = JSONObject.parseObject(systemConf.getParamValue(), TencentStorageConfig.class);
                systemConfigVo.setTencentStorageConfig(tencentStorageConfig);
            } else if (type.equals(CloudServiceEnum.LOCAL.getType())) {
                LocalStorageConfig localStorageConfig = JSONObject.parseObject(systemConf.getParamValue(), LocalStorageConfig.class);
                systemConfigVo.setLocalStorageConfig(localStorageConfig);
            }
        }
        return systemConfigVo;
    }

    @Override
    public Result<OssConfigDto> currentOssConfig() {
        SystemConf ossConf = this.getByParamKey(SystemConstant.CURRENT_OSS_TYPE);
        Integer type = CommonConstants.NUMBER_ONE;
        if (ossConf != null) {
            type = Integer.valueOf(ossConf.getParamValue());
        }
        return ossConfig(type);
    }

    private SystemConf getByParamKey(String paramKey) {
        return this.getBaseMapper().selectOne(new QueryWrapper<SystemConf>().eq("param_key", paramKey));
    }

    @Override
    public Result<OssConfigDto> ossConfig(Integer type) {
        if (type == 0) {
            SystemConf ossConf = this.getByParamKey(SystemConstant.CURRENT_OSS_TYPE);
            if (ossConf == null) {
                type = CommonConstants.NUMBER_ONE;
            } else {
                type = Integer.valueOf(ossConf.getParamValue());
            }
        }
        SystemConfigVo typeInfo = getTypeInfo(type);
        if (typeInfo == null) {
            return Result.failed("当前oss配置为空无法使用");
        }
        OssConfigDto dto = new OssConfigDto();
        dto.setType(type);
        if (type.equals(CloudServiceEnum.QINIU.getType()) && typeInfo.getQiniouStorageConfig() != null) {
            BeanUtils.copyProperties(typeInfo.getQiniouStorageConfig(), dto);
        } else if (type.equals(CloudServiceEnum.ALIYUN.getType()) && typeInfo.getAliyunStorageConfig() != null) {
            BeanUtils.copyProperties(typeInfo.getAliyunStorageConfig(), dto);
        } else if (type.equals(CloudServiceEnum.QCLOUD.getType()) && typeInfo.getTencentStorageConfig() != null) {
            BeanUtils.copyProperties(typeInfo.getTencentStorageConfig(), dto);
        }else if (type.equals(CloudServiceEnum.LOCAL.getType()) && typeInfo.getLocalStorageConfig() != null) {
            BeanUtils.copyProperties(typeInfo.getLocalStorageConfig(), dto);
        }
        return Result.ok(dto);
    }

    @Override
    public void savePayConfig(SavePayConfigDto savePayConfigDto) {
        SystemConf systemConf = this.getByParamKey(SystemConstant.PLATFORM_PAY_CONFIG);
        if (ObjectUtil.isNotNull(systemConf)) {
            systemConf.setParamValue(JSONObject.toJSONString(savePayConfigDto));
            this.baseMapper.updateById(systemConf);
        } else {
            systemConf = new SystemConf();
            systemConf.setParamKey(SystemConstant.PLATFORM_PAY_CONFIG);
            systemConf.setParamValue(JSONObject.toJSONString(savePayConfigDto));
            this.baseMapper.insert(systemConf);
        }
        if (StrUtil.isNotEmpty(savePayConfigDto.getAlipayAppId()) &&
                StrUtil.isNotEmpty(savePayConfigDto.getAlipayPrivateKey()) &&
                StrUtil.isNotEmpty(savePayConfigDto.getAlipayPublicKey())) {
            //获取支付配置
            PayConfigVo payConfig = getPayConfig();
            //更新设置支付宝相关配置
            setAliConfigKit(payConfig);
        }
    }

    private void setAliConfigKit(PayConfigVo payConfig) {
        AliPayApiConfig aliPayApiConfig = AliPayApiConfig.builder()
                .setAppId(payConfig.getAlipayAppId())
                .setCharset("UTF-8")
                .setPrivateKey(payConfig.getAlipayPrivateKey())
                .setAliPayPublicKey(payConfig.getAlipayPublicKey())
                .setSignType("RSA2")
                .setServiceUrl("")
                // 普通公钥方式
                .build();
        AliPayApiConfigKit.setThreadLocalAliPayApiConfig(aliPayApiConfig);
    }

    @Override
    public PayConfigVo getPayConfig() {
        SystemConf systemConf = this.getByParamKey(SystemConstant.PLATFORM_PAY_CONFIG);
        if (ObjectUtil.isNull(systemConf)) {
            return new PayConfigVo();
        }
        return JSONObject.parseObject(systemConf.getParamValue(), PayConfigVo.class);
    }

    @Override
    public KfmsgVo getKfmsg() {
        SystemConf systemConf = this.getByParamKey(SystemConstant.KF_MSG_NOTIFY);
        if (ObjectUtil.isNull(systemConf)) {
            return new KfmsgVo();
        }
        return JSONObject.parseObject(systemConf.getParamValue(), KfmsgVo.class);
    }

    @Override
    public void saveKfmsg(KfAddDto dto) {
        SystemConf systemConf = this.getByParamKey(SystemConstant.KF_MSG_NOTIFY);
        if (ObjectUtil.isNotNull(systemConf)) {
            systemConf.setParamValue(JSONObject.toJSONString(dto));
            this.baseMapper.updateById(systemConf);
        } else {
            systemConf = new SystemConf();
            systemConf.setParamKey(SystemConstant.KF_MSG_NOTIFY);
            systemConf.setParamValue(JSONObject.toJSONString(dto));
            this.baseMapper.insert(systemConf);
        }
    }

    @Override
    public void sendKfmsg(Integer sendType, JSONObject sendCentent) {
        KfmsgVo kfmsg = getKfmsg();
        String templateId = "";
        String oepnIdListStr = null;
        //1-客服模板消息 2-开票模板消息
        log.info("sendType : {}", sendType);
        if (sendType.equals(CommonConstants.NUMBER_ONE)) {
            templateId = kfmsg.getKfmsgTemplateId();
            oepnIdListStr = kfmsg.getKfOpenIds();
        } else if (sendType.equals(CommonConstants.NUMBER_TWO)) {
            templateId = kfmsg.getInvoiceTemplateId();
            oepnIdListStr = kfmsg.getInvoiceOpenIds();
        } else {
            throw new ServiceException("错误发送类型");
        }
        if (StrUtil.isEmpty(templateId)) {
            log.info("不存在有效的模板id");
            return;
        }
        if (StrUtil.isEmpty(oepnIdListStr)) {
            log.info("不存在配置openId");
            return;
        }
        //使用笔歌公众号发送模板消息通知客服
        WxMpService wxMpService = this.wxMpService.switchoverTo(MeConstant.BGN_APPID);
        WxMpTemplateMsgService templateMsgService = wxMpService.getTemplateMsgService();
        try {
            for (String oepnId : oepnIdListStr.split(MeConstant.DOUHAO)) {
                WxMpTemplateMessage message = new WxMpTemplateMessage();
                message.setToUser(oepnId);
                message.setTemplateId(templateId);
                message.addData(new WxMpTemplateData("first", sendCentent.getString("first")));
                JSONArray keyword = sendCentent.getJSONArray("keyword");
                for (int i = 0; i < keyword.size(); i++) {
                    message.addData(new WxMpTemplateData("keyword" + (i + 1), keyword.getString(i)));
                }
                message.addData(new WxMpTemplateData("remark", sendCentent.getString("remark")));
                templateMsgService.sendTemplateMsg(message);
            }
        } catch (WxErrorException wxErrorException) {
            wxErrorException.printStackTrace();
            log.info("错误：{}", wxErrorException.getMessage());
        }
    }

    @Override
    public void saveCodeEmailConf(CodeEmailConfDto emailConfDto) {
        SystemConf systemConf = this.getByParamKey(SystemConstant.CODE_EMAIL_CONF);
        if (ObjectUtil.isNotNull(systemConf)) {
            systemConf.setParamValue(JSONObject.toJSONString(emailConfDto));
            this.baseMapper.updateById(systemConf);
        } else {
            systemConf = new SystemConf();
            systemConf.setParamKey(SystemConstant.CODE_EMAIL_CONF);
            systemConf.setParamValue(JSONObject.toJSONString(emailConfDto));
            this.baseMapper.insert(systemConf);
        }
    }

    @Override
    public CodeEmailConfVo getCodeEmailConf() {
        SystemConf systemConf = this.getByParamKey(SystemConstant.CODE_EMAIL_CONF);
        if (ObjectUtil.isNull(systemConf)) {
            return new CodeEmailConfVo();
        }
        return JSONObject.parseObject(systemConf.getParamValue(), CodeEmailConfVo.class);
    }

    /**
     * 获取易达接口配置信息
     * @return
     */
    @Override
    public Result<YDERPConfigDto> ydERPConfig() {
        SystemConf ossConf = this.getByParamKey(SystemConstant.CURRENT_YDERP_TYPE);
        YDERPConfigDto dto = new YDERPConfigDto();
        if (ossConf == null) {
            dto.setType(CommonConstants.NUMBER_ZERO);
        } else {
            dto.setType(Integer.valueOf(ossConf.getParamValue()));
        }

        return Result.ok(dto);
    }

    /**
     * 保存易达接口信息配置
     * @param saveConfigDto
     */
    @Override
    public void ydERPSave(YDERPConfigDto saveConfigDto) {

        Integer type = saveConfigDto.getType();
        SystemConf ydERPConf = this.getByParamKey(SystemConstant.CURRENT_YDERP_TYPE);
        if (ydERPConf == null) {
            ydERPConf = new SystemConf();
            ydERPConf.setParamKey(SystemConstant.CURRENT_YDERP_TYPE);
        }
        ydERPConf.setParamValue(type.toString());
        this.saveOrUpdate(ydERPConf);
    }

    /**
     * 商城是否开启了易达接口
     * @return
     */
    @Override
    public Boolean openYDERP(){
        boolean isOpen = false;
        SystemConf ydERPConf = this.getByParamKey(SystemConstant.CURRENT_YDERP_TYPE);
        if (ydERPConf != null) {
            isOpen = MeConstant.STRING_ONE.equals(ydERPConf.getParamValue()) ? true : false;
        }
        return isOpen;
    }

    /**
     * 获取所有oss配置
     * @return
     */
    @Override
    public Result<List<OssConfigDto>> allOssConfig() {
        List<SystemConf> ossConfList = this.getListByParamKey(SystemConstant.CURRENT_OSS_TYPE);
        List<OssConfigDto> ossConfigs = new ArrayList<>();
        Result<List<OssConfigDto>> listResult = Result.ok(ossConfigs);
        if (ossConfList != null && !ossConfList.isEmpty()) {
            ossConfList.forEach(e -> {
                Integer type = CommonConstants.NUMBER_ONE;
                type = Integer.valueOf(e.getParamValue());
                Result<OssConfigDto> result = ossConfigWithTenantId(e);
                if(result.getCode() == CommonConstants.SUCCESS){
                    ossConfigs.add(result.getData());
                }
            });

        }
        return listResult;
    }

    private List<SystemConf> getListByParamKey(String paramKey) {
        return this.getBaseMapper().selectList(new QueryWrapper<SystemConf>().eq("param_key", paramKey));
    }

    /**
     * 获取指定租户指定类型的存储配置
     * @param systemConf
     * @param tenantId
     * @return
     */
    @Override
    public Result<OssConfigDto> ossConfigWithTenantId(SystemConf systemConf){
        Integer type = Integer.valueOf(systemConf.getParamValue());
        SystemConfigVo typeInfo = getTypeInfoByTenantId(type, systemConf.getTenantId());
        if (typeInfo == null) {
            return Result.failed("当前oss配置为空无法使用");
        }
        OssConfigDto dto = new OssConfigDto();
        dto.setType(type);
        dto.setTenantId(systemConf.getTenantId());
        if (type.equals(CloudServiceEnum.QINIU.getType()) && typeInfo.getQiniouStorageConfig() != null) {
            BeanUtils.copyProperties(typeInfo.getQiniouStorageConfig(), dto);
        } else if (type.equals(CloudServiceEnum.ALIYUN.getType()) && typeInfo.getAliyunStorageConfig() != null) {
            BeanUtils.copyProperties(typeInfo.getAliyunStorageConfig(), dto);
        } else if (type.equals(CloudServiceEnum.QCLOUD.getType()) && typeInfo.getTencentStorageConfig() != null) {
            BeanUtils.copyProperties(typeInfo.getTencentStorageConfig(), dto);
        }else if (type.equals(CloudServiceEnum.LOCAL.getType()) && typeInfo.getLocalStorageConfig() != null) {
            BeanUtils.copyProperties(typeInfo.getLocalStorageConfig(), dto);
        }
        return Result.ok(dto);
    }

    private SystemConf getByParamKeyTenantId(String paramKey, String tenantId) {
        return this.getBaseMapper().selectOne(new QueryWrapper<SystemConf>().eq("param_key", paramKey).eq("tenant_id", tenantId));
    }

    /**
     * 获取指定租户指定类型的系统配置
     * @param type 类型
     * @param tenantId
     * @return
     */
    @Override
    public SystemConfigVo getTypeInfoByTenantId(Integer type, String tenantId) {
        String paramKey = "";
        if (type.equals(0)) {
            paramKey = SystemConstant.SERVER_CONFIG;
        } else if (type.equals(CloudServiceEnum.QINIU.getType())) {
            paramKey = SystemConstant.STORAGE_QINIOUYUN;
        } else if (type.equals(CloudServiceEnum.ALIYUN.getType())) {
            paramKey = SystemConstant.STORAGE_ALIYUN;
        } else if (type.equals(CloudServiceEnum.QCLOUD.getType())) {
            paramKey = SystemConstant.STORAGE_TENCENT_CLOUD;
        } else if (type.equals(CloudServiceEnum.LOCAL.getType())) {
            paramKey = SystemConstant.STORAGE_LOCAL_CLOUD;
        } else {
            throw new ServiceException("数据获取异常", SystemCode.DATA_NOT_EXIST.getCode());
        }
        SystemConfigVo systemConfigVo = new SystemConfigVo();
        SystemConf systemConf = this.getByParamKeyTenantId(paramKey, tenantId);
        if (systemConf == null) {
            return systemConfigVo;
        }
        if (type == 0) {
            SystemConfig systemConfig = JSONObject.parseObject(systemConf.getParamValue(), SystemConfig.class);
            systemConfigVo.setSystemConfig(systemConfig);
        } else {
            if (type.equals(CloudServiceEnum.QINIU.getType())) {
                QiniouStorageConfig qiniouStorageConfig = JSONObject.parseObject(systemConf.getParamValue(), QiniouStorageConfig.class);
                systemConfigVo.setQiniouStorageConfig(qiniouStorageConfig);
            } else if (type.equals(CloudServiceEnum.ALIYUN.getType())) {
                AliyunStorageConfig aliyunStorageConfig = JSONObject.parseObject(systemConf.getParamValue(), AliyunStorageConfig.class);
                systemConfigVo.setAliyunStorageConfig(aliyunStorageConfig);
            } else if (type.equals(CloudServiceEnum.QCLOUD.getType())) {
                TencentStorageConfig tencentStorageConfig = JSONObject.parseObject(systemConf.getParamValue(), TencentStorageConfig.class);
                systemConfigVo.setTencentStorageConfig(tencentStorageConfig);
            } else if (type.equals(CloudServiceEnum.LOCAL.getType())) {
                LocalStorageConfig localStorageConfig = JSONObject.parseObject(systemConf.getParamValue(), LocalStorageConfig.class);
                systemConfigVo.setLocalStorageConfig(localStorageConfig);
            }
        }
        return systemConfigVo;
    }

}
