package com.medusa.gruul.account.model.param;

import com.medusa.gruul.common.core.param.QueryParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 18:23 2024/9/24
 */
@Data
@ApiModel(value = "ApiPackageGoodsCodeParam 实体", description = "小程序用户核销记录param")
public class ApiPackageGoodsCodeParam extends QueryParam {


    /**
     * 小程序用户Id
     */
    @ApiModelProperty(value = "小程序用户Id")
    private String userId;

}
