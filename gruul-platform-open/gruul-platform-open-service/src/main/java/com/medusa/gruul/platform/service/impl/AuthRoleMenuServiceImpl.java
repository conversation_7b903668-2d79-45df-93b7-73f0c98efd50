package com.medusa.gruul.platform.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.medusa.gruul.common.core.exception.ServiceException;
import com.medusa.gruul.common.core.util.CurUserUtil;
import com.medusa.gruul.common.dto.CurPcUserInfoDto;
import com.medusa.gruul.platform.api.entity.*;
import com.medusa.gruul.platform.api.model.dto.AuthRoleMenuDto;
import com.medusa.gruul.platform.api.model.dto.AuthUserMenuDto;
import com.medusa.gruul.platform.api.model.dto.ButtonDto;
import com.medusa.gruul.platform.api.model.dto.MenuDto;
import com.medusa.gruul.platform.mapper.AuthMenuUserMapper;
import com.medusa.gruul.platform.mapper.AuthRoleMenuMapper;
import com.medusa.gruul.platform.model.dto.AuthRoleMenuParamDto;
import com.medusa.gruul.platform.service.IAuthMenuInfoService;
import com.medusa.gruul.platform.service.IAuthRoleInfoService;
import com.medusa.gruul.platform.service.IAuthRoleMenuService;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: plh
 * @Description: 角色菜单服务实现类
 * @Date: Created in 21:04 2023/8/16
 */
@Service
@Log4j2
public class AuthRoleMenuServiceImpl extends ServiceImpl<AuthRoleMenuMapper, AuthRoleMenu> implements IAuthRoleMenuService {

    @Autowired
    private IAuthRoleInfoService authRoleInfoService;

    @Autowired
    private AuthMenuUserMapper authMenuUserMapper;



    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addAuthRoleMenu(AuthRoleMenuDto authRoleMenuDto) {
        Long roleId = authRoleMenuDto.getRoleId();
        this.deleteAuthRoleMenu(roleId);
        //父级菜单
        List<MenuDto> menuParent = authRoleMenuDto.getMenuParent();
        if(menuParent!=null&&menuParent.size()>0){
            for (MenuDto menuDto : menuParent) {
                Long id = menuDto.getId();
                AuthRoleMenu authRoleMenu = new AuthRoleMenu();
                authRoleMenu.setRoleId(roleId);
                authRoleMenu.setParentId(0l);
                authRoleMenu.setMenuId(id);
                authRoleMenu.setType(0);
                CurPcUserInfoDto pcUserInfoDto = CurUserUtil.getPcRqeustAccountInfo();
                authRoleMenu.setCreateUserId(Long.valueOf(pcUserInfoDto.getUserId()));
                this.baseMapper.insert(authRoleMenu);
            }
        }

        //子级菜单
        List<MenuDto> menu = authRoleMenuDto.getMenu();
        if(menu!=null&&menu.size()>0){
            for (MenuDto menuDto : menu) {
                Long id = menuDto.getId();
                AuthRoleMenu authRoleMenu = new AuthRoleMenu();
                authRoleMenu.setRoleId(roleId);
                authRoleMenu.setMenuId(id);
                authRoleMenu.setType(0);
                Long parentId = menuDto.getParentId();
                LambdaQueryWrapper<AuthRoleMenu>wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(AuthRoleMenu::getRoleId,roleId);
                wrapper.eq(AuthRoleMenu::getMenuId,parentId);
                AuthRoleMenu searchAuthRoleMenu= this.baseMapper.selectOne(wrapper);
                if(searchAuthRoleMenu!=null){
                    authRoleMenu.setParentId(searchAuthRoleMenu.getId());
                }else{
                    authRoleMenu.setParentId(parentId);
                }
                CurPcUserInfoDto pcUserInfoDto = CurUserUtil.getPcRqeustAccountInfo();
                authRoleMenu.setCreateUserId(Long.valueOf(pcUserInfoDto.getUserId()));
                this.baseMapper.insert(authRoleMenu);
            }
        }
        //按钮
        List<ButtonDto> button = authRoleMenuDto.getButton();
        if(button!=null&&button.size()>0){
            for (ButtonDto buttonDto : button) {
                Long id = buttonDto.getId();
                AuthRoleMenu authRoleMenu = new AuthRoleMenu();
                authRoleMenu.setRoleId(roleId);
                authRoleMenu.setMenuId(id);
                authRoleMenu.setType(1);
                Long parentId = buttonDto.getParentId();
                LambdaQueryWrapper<AuthRoleMenu>wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(AuthRoleMenu::getRoleId,roleId);
                wrapper.eq(AuthRoleMenu::getMenuId,parentId);
                AuthRoleMenu searchAuthRoleMenu= this.baseMapper.selectOne(wrapper);
                if(searchAuthRoleMenu!=null){
                    authRoleMenu.setParentId(searchAuthRoleMenu.getId());
                }else{
                    authRoleMenu.setParentId(parentId);
                }
                CurPcUserInfoDto pcUserInfoDto = CurUserUtil.getPcRqeustAccountInfo();
                authRoleMenu.setCreateUserId(Long.valueOf(pcUserInfoDto.getUserId()));
                this.baseMapper.insert(authRoleMenu);
            }
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteAuthRoleMenu(Long roleId) {
        AuthRoleInfo authRoleInfo = authRoleInfoService.getById(roleId);
        if(authRoleInfo==null){
            throw new ServiceException("角色不存在！");
        }
        LambdaQueryWrapper<AuthRoleMenu> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AuthRoleMenu::getRoleId,roleId);
        this.baseMapper.delete(wrapper);
    }

    @Override
    public List<Long> getMenuIds(Long roleId) {
        LambdaQueryWrapper<AuthRoleMenu>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AuthRoleMenu::getRoleId,roleId);
        List<AuthRoleMenu> authRoleMenus = this.baseMapper.selectList(wrapper);
        List<Long> ids = authRoleMenus.stream().map(AuthRoleMenu::getMenuId).collect(Collectors.toList());
        return ids;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addAuthUserMenu(AuthUserMenuDto authUserMenuDto) {
        this.deleteUserMenu();
        //父级菜单
        List<MenuDto> menuParent = authUserMenuDto.getMenuParent();
        if(menuParent!=null&&menuParent.size()>0){
            for (MenuDto menuDto : menuParent) {
                Long id = menuDto.getId();
                AuthMenuUser authMenuUser = new AuthMenuUser();
                authMenuUser.setParentId(0l);
                authMenuUser.setMenuId(id);
                authMenuUser.setType(0);
                CurPcUserInfoDto pcUserInfoDto = CurUserUtil.getPcRqeustAccountInfo();
                authMenuUser.setCreateUserId(Long.valueOf(pcUserInfoDto.getUserId()));
                authMenuUserMapper.insert(authMenuUser);
            }
        }
        //子级菜单
        List<MenuDto> menu = authUserMenuDto.getMenu();
        if(menu!=null&&menu.size()>0){
            for (MenuDto menuDto : menu) {
                Long id = menuDto.getId();
                AuthMenuUser authMenuUser = new AuthMenuUser();
                authMenuUser.setMenuId(id);
                authMenuUser.setType(0);
                Long parentId = menuDto.getParentId();
                LambdaQueryWrapper<AuthMenuUser>wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(AuthMenuUser::getMenuId,parentId);
                AuthMenuUser searchAuthMenuUser= authMenuUserMapper.selectOne(wrapper);
                if(searchAuthMenuUser!=null){
                    authMenuUser.setParentId(searchAuthMenuUser.getId());
                }else{
                    authMenuUser.setParentId(parentId);
                }
                CurPcUserInfoDto pcUserInfoDto = CurUserUtil.getPcRqeustAccountInfo();
                authMenuUser.setCreateUserId(Long.valueOf(pcUserInfoDto.getUserId()));
                authMenuUserMapper.insert(authMenuUser);
            }
        }
        //按钮
        List<ButtonDto> button = authUserMenuDto.getButton();
        if(button!=null&&button.size()>0){
            for (ButtonDto buttonDto : button) {
                Long id = buttonDto.getId();
                AuthMenuUser authMenuUser = new AuthMenuUser();
                authMenuUser.setMenuId(id);
                authMenuUser.setType(1);
                Long parentId = buttonDto.getParentId();
                LambdaQueryWrapper<AuthMenuUser>wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(AuthMenuUser::getMenuId,parentId);
                AuthMenuUser searchAuthAuthMenuUser= authMenuUserMapper.selectOne(wrapper);
                if(searchAuthAuthMenuUser!=null){
                    authMenuUser.setParentId(searchAuthAuthMenuUser.getId());
                }else{
                    authMenuUser.setParentId(parentId);
                }
                CurPcUserInfoDto pcUserInfoDto = CurUserUtil.getPcRqeustAccountInfo();
                authMenuUser.setCreateUserId(Long.valueOf(pcUserInfoDto.getUserId()));
                authMenuUserMapper.insert(authMenuUser);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteUserMenu() {
        LambdaQueryWrapper<AuthMenuUser> wrapper = new LambdaQueryWrapper<>();
        authMenuUserMapper.delete(wrapper);
    }

    @Override
    public List<Long> getUserMenuIds() {
        LambdaQueryWrapper<AuthMenuUser>wrapper = new LambdaQueryWrapper<>();
        List<AuthMenuUser> authMenuUsers = authMenuUserMapper.selectList(wrapper);
        List<Long> ids = authMenuUsers.stream().map(AuthMenuUser::getMenuId).collect(Collectors.toList());
        return ids;
    }

}
