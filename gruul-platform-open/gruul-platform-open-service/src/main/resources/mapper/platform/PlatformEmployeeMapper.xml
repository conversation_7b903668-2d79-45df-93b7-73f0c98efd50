<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.medusa.gruul.platform.mapper.PlatformEmployeeMapper">
    <!--平台职员信息结果集 -->
    <resultMap id="EmployeeVoMap" type="com.medusa.gruul.platform.api.model.vo.EmployeeVo">
        <id column="id" property="id"/>
        <result column="emp_number" property="empNumber"/>
        <result column="department_name" property="departmentName"/>
        <result column="emp_full_name" property="empFullName"/>
        <result column="warehouse_id" property="warehouseId"/>
        <result column="stock_code" property="stockCode"/>
        <result column="warehouse_full_name" property="warehouseFullName"/>
        <result column="out_id" property="outId"/>
        <result column="store_front_name" property="storeFrontName"/>
        <result column="store_front_code" property="storeFrontCode"/>
        <result column="dept_code" property="deptCode"/>
        <result column="department_code" property="departmentCode"/>

        <result column="employee_image_url" property="employeeImageUrl"/>
        <result column="high_number" property="highNumber"/>
        <result column="service_number" property="serviceNumber"/>
        <result column="work_status" property="workStatus"/>
    </resultMap>

    <select id="searchEmployee" resultMap="EmployeeVoMap">
        select
        t1.id,t1.emp_number,t2.dept_full_name as department_name,t1.emp_full_name,t1.warehouse_id,t1.warehouse_full_name,
        t1.out_id,t1.store_front_name,t1.store_front_code,t1.stock_code,t1.employee_image_url,t1.high_number,t1.service_number,
        IFNULL( t3.work_status,2) as work_status
        from
        t_platform_lyd_employee t1
        left join t_platform_lyd_department t2 on t1.department_code = t2.class_code
        left JOIN t_platform_employee_schedule t3 on t1.id = t3.employee_id and DATE(t3.schedule_date) = DATE(#{params.scheduleDate})
        where t1.is_deleted = 0
        <if test="params.empNumber!=null and params.empNumber!=''" >
            and  t1.emp_number LIKE CONCAT('%',#{params.empNumber},'%')
        </if>
        <if test="params.departmentName!=null and params.departmentName!=''">
            and  t2.dept_full_name LIKE CONCAT('%',#{params.departmentName},'%')
        </if>
        <if test="params.empFullName!=null and params.empFullName!=''">
            and  t1.emp_full_name LIKE CONCAT('%',#{params.empFullName},'%')
        </if>
        <if test="params.warehouseFullName!=null and params.warehouseFullName!=''">
            and  t1.warehouse_full_name LIKE CONCAT('%',#{params.warehouseFullName},'%')
        </if>
        <if test="params.departmentCode!=null and params.departmentCode!=''">
            and  t1.department_code = #{params.departmentCode}
        </if>
        <if test="params.workStatus!=null">
            and  t3.work_status = #{params.workStatus}
        </if>
    </select>

    <select id="searchBindEmployee" resultMap="EmployeeVoMap">
        select
        t1.id,t1.emp_number,t2.dept_full_name as department_name,t1.emp_full_name,t1.warehouse_id,t1.warehouse_full_name,
        t1.out_id,t1.store_front_name,t1.store_front_code,t2.class_code as department_code,t2.dept_code
        from
        t_platform_lyd_employee t1
        left join t_platform_lyd_department t2 on t1.department_code = t2.class_code
        left join t_platform_account_info t3 on t3.employee_id = t1.out_id
        where t1.is_deleted = 0
        and (ifnull(t3.id,'') = '' or ifnull(t3.id,'') = #{params.userId})
        <if test="params.empNumber!=null and params.empNumber!=''" >
            and  t1.emp_number LIKE CONCAT('%',#{params.empNumber},'%')
        </if>
        <if test="params.departmentName!=null and params.departmentName!=''">
            and  t2.dept_full_name LIKE CONCAT('%',#{params.departmentName},'%')
        </if>
        <if test="params.empFullName!=null and params.empFullName!=''">
            and  t1.emp_full_name LIKE CONCAT('%',#{params.empFullName},'%')
        </if>
        <if test="params.warehouseFullName!=null and params.warehouseFullName!=''">
            and  t1.warehouse_full_name LIKE CONCAT('%',#{params.warehouseFullName},'%')
        </if>
        <if test="params.departmentCode!=null and params.departmentCode!=''">
            and  t1.department_code = #{params.departmentCode}
        </if>
    </select>
    <select id="getLastEmpNumber" resultType="java.lang.String">
        SELECT
            max(
                    RIGHT ( concat( '                ', t1.emp_number ), 20 ))
        FROM
            t_platform_lyd_employee t1
        WHERE
            t1.emp_number REGEXP '[0-9]'
	AND (
		t1.class_code IS NULL
		OR length ( t1.class_code )= 0
	OR length ( t1.class_code )= 5
	)
    </select>
</mapper>
