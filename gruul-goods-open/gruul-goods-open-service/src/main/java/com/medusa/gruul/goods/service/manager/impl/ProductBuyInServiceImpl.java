package com.medusa.gruul.goods.service.manager.impl;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.medusa.gruul.common.core.util.CurUserUtil;
import com.medusa.gruul.common.core.util.HuToolExcelUtils;
import com.medusa.gruul.common.core.util.IDUtil;
import com.medusa.gruul.common.dto.CurUserDto;
import com.medusa.gruul.goods.api.entity.ProductBuyIn;
import com.medusa.gruul.goods.api.entity.ProductBuyInItem;
import com.medusa.gruul.goods.api.entity.ProductStock;
import com.medusa.gruul.goods.api.entity.SkuStock;
import com.medusa.gruul.goods.api.model.dto.manager.ProductBuyInDto;
import com.medusa.gruul.goods.api.model.dto.manager.ProductBuyInItemDto;
import com.medusa.gruul.goods.api.model.vo.manager.ProductBuyInExcelVo;
import com.medusa.gruul.goods.api.model.vo.manager.ProductBuyInVo;
import com.medusa.gruul.goods.api.model.vo.manager.ProductSecUnitVo;
import com.medusa.gruul.goods.mapper.manager.ProductBuyInMapper;
import com.medusa.gruul.goods.service.manager.IProductBuyInItemService;
import com.medusa.gruul.goods.service.manager.IProductBuyInService;
import com.medusa.gruul.goods.service.manager.IProductStockService;
import com.medusa.gruul.goods.service.manager.ISkuStockService;
import com.medusa.gruul.shops.api.entity.DictItem;
import com.medusa.gruul.shops.api.feign.RemoteShopsService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 购货入库
 * @Author: qsx
 * @Date:   2022-03-08
 * @Version: V1.0
 */
@Service
public class ProductBuyInServiceImpl extends ServiceImpl<ProductBuyInMapper, ProductBuyIn> implements IProductBuyInService {
    @Autowired
    private IProductStockService productStockService;
    @Autowired
    private IProductBuyInService productBuyInService;
    @Autowired
    private IProductBuyInItemService productBuyInItemService;
    @Autowired
    private ISkuStockService skuStockService;
    @Autowired
    private ProductSecUnitServiceImpl productSecUnitService;
    @Autowired
    private RemoteShopsService remoteShopsService;

    @Override
    public String getReceiptNo(String time) {
        return this.baseMapper.getReceiptNo(time);
    }

    @Override
    public IPage<ProductBuyInVo> selectList(ProductBuyInDto productBuyInDto) {
        IPage<ProductBuyInVo> page = new Page<>(productBuyInDto.getCurrent(), productBuyInDto.getSize());
        IPage<ProductBuyInVo> iPage =this.baseMapper.selectList((new Page<>(productBuyInDto.getCurrent(), productBuyInDto.getSize())),productBuyInDto);
        List<ProductBuyInVo> records = iPage.getRecords();
        List<DictItem> dictItemList=remoteShopsService.getBuyType();
        Map<String, String> dictItemMap = dictItemList.stream().collect(Collectors.toMap(DictItem::getItemValue, e -> e.getItemText()));
        records.stream().forEach(e -> {
            e.setBuyTypeName(dictItemMap.get(e.getBuyType()));
        });
        page.setTotal(iPage.getTotal());
        return page.setRecords(records);
    }

    @Override
    public ProductBuyInVo getById(Long id) {
        return this.baseMapper.getById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void add(ProductBuyInDto productBuyInDto) {
        // 判断是草稿还是已提交状态
        Integer status = productBuyInDto.getStatus();
        if (status == null) {
            status = 100; // 默认为已提交状态
        }
        if (status == 0) {
            // 草稿 不修改库存
            saveDraft(productBuyInDto);
            return;
        }
        ProductBuyIn productBuyIn=new ProductBuyIn();
        BeanUtils.copyProperties(productBuyInDto, productBuyIn);
        //主表id
        Long id= IDUtil.getId();
        productBuyIn.setId(id);
        List<ProductBuyInItemDto> productBuyInItemDtoList = productBuyInDto.getProductBuyInItemList();
        List<ProductBuyInItem> productBuyInItemList = new ArrayList<ProductBuyInItem>();
        //将明细商品对应的辅助单位全部查出来
        List<Long> productIdList = productBuyInItemDtoList.stream().map(ProductBuyInItemDto::getProductId).collect(Collectors.toList());
        Map paramMap = new HashMap(1);
        paramMap.put("productIdList", productIdList);
        List<ProductSecUnitVo> productSecUnitList = this.productSecUnitService.queryProductSecUnitByProductIds(paramMap);
        //辅助单位的对应关系
        Map<String, ProductSecUnitVo> unitMap = new HashMap<>();
        if(CollectionUtil.isNotEmpty(productSecUnitList)) {
            //key:商品id_辅助单位id
            unitMap = productSecUnitList.stream().collect(Collectors.toMap(e -> e.getProductId() + "_" + e.getSecUnitId(), e -> e));
        }
        //查询商品，
        //单据总金额、总数量
        BigDecimal totalAmount = new BigDecimal("0");
        BigDecimal totalQty = new BigDecimal("0");
        //将ProductBuyInItemDto转成ProductBuyInItem
        for(ProductBuyInItemDto productBuyInItem:productBuyInItemDtoList){

            //对金额进行计算
            BigDecimal inAmount = productBuyInItem.getInstoreQty().multiply(productBuyInItem.getInstorePrice()).setScale(2, BigDecimal.ROUND_HALF_UP);
            productBuyInItem.setInstoreAmount(inAmount);
            totalAmount = totalAmount.add(inAmount);
            //将下单选择的单位转换为基本单位，同时要将明细的数量、单价和单据的总数量都要进行对应的转换
            String unitKey = productBuyInItem.getProductId() + "_" + (productBuyInItem.getInUnitId() == null ? "" : productBuyInItem.getInUnitId());
            if(unitMap.containsKey(unitKey)){
                ProductSecUnitVo secUnitVo = unitMap.get(unitKey);
                //是辅助单位，则要进行转换
                BigDecimal inQty = productBuyInItem.getInstoreQty().multiply(new BigDecimal(secUnitVo.getUnitd())).divide(new BigDecimal(secUnitVo.getSecUnitd()), 2, BigDecimal.ROUND_HALF_UP);
                BigDecimal inPrice = inAmount.divide(inQty, 2, BigDecimal.ROUND_HALF_UP);
                productBuyInItem.setInstorePrice(inPrice);
                productBuyInItem.setInstoreQty(inQty);
                totalQty = totalQty.add(inQty);
            }else{
                totalQty = totalQty.add(productBuyInItem.getInstoreQty());
            }
            productBuyInItem.setProductBuyInId(id);

            ProductBuyInItem productBuyInItemWto=new ProductBuyInItem();
            BeanUtils.copyProperties(productBuyInItem, productBuyInItemWto);
            productBuyInItemList.add(productBuyInItemWto);
        }
        productBuyIn.setBuyAmount(totalAmount);
        productBuyIn.setBuyQuantity(totalQty);
        //保存主表信息
        productBuyInService.save(productBuyIn);
        //获取入库单的id
        //productBuyInItemList = productBuyInItemList.stream().map(item -> item.setProductBuyInId(id)).collect(Collectors.toList());
        //添加商品库存明细
        productBuyInItemService.saveBatch(productBuyInItemList);
        //修改库存数量
        updateProductStock(productBuyIn,productBuyInItemDtoList);

    }
    //规格库存添加入库数量
    public void addBuyInStock(List<ProductBuyInItemDto> productBuyInItemDtoList) {
        //获取规格Id
        List<Long> skuIdList = productBuyInItemDtoList.stream().map(ProductBuyInItemDto::getSkuId).collect(Collectors.toList());
        //获取规格对应的规格库存记录
        List<SkuStock> skuStockList=skuStockService.listByIds(skuIdList);
        Map<Long, SkuStock> skuStockMap=new HashMap<>();
        if(CollectionUtil.isNotEmpty(skuStockList)){
            skuStockMap = skuStockList.stream().collect(Collectors.toMap(SkuStock::getId, v -> v));
        }
        // 获取规格id对应的被关联的规格库存记录
        List<SkuStock> linkedSkuStockList = skuStockService.list(new LambdaQueryWrapper<SkuStock>().in(SkuStock::getLinkSkuId,skuIdList));
        //获取关联规格Id
        List<Long> linkedSkuIdList = linkedSkuStockList.stream().map(SkuStock::getId).collect(Collectors.toList());
        // 将关联商品的库存信息组装成规格id-库存对象的map记录
        Map<Long, SkuStock> linkedSkuStockMap=new HashMap<>();
        // 将商品规格记录组装成被关联规格id-规格id的map记录
        Map<Long, List<Long>> linkedSkuIdMap=new HashMap<>();
        if(CollectionUtil.isNotEmpty(linkedSkuStockList)){
            linkedSkuStockMap = linkedSkuStockList.stream().collect(Collectors.toMap(SkuStock::getId, v -> v));
            linkedSkuIdMap = linkedSkuStockList.stream()
                    .collect(Collectors.groupingBy(SkuStock::getLinkSkuId, Collectors.mapping(SkuStock::getId, Collectors.toList())));
        }

        List<SkuStock> updateSkuStockList=new ArrayList<>();
        //将规格库存里有的商品数量与入库的商品数量相加
        for(ProductBuyInItemDto productBuyInItem : productBuyInItemDtoList){
            SkuStock skuStock=skuStockMap.get(productBuyInItem.getSkuId());
            //规格库存的数量
            BigDecimal skuStockStock=skuStock.getStock();
            //获取入库的数量
            BigDecimal productBuyInItemQty=productBuyInItem.getInstoreQty();
            //规格库存的数量+入库的数量
            skuStockStock=skuStockStock.add(productBuyInItemQty);
            skuStock.setStock(skuStockStock);
            updateSkuStockList.add(skuStock);

            //修改关联了此规格的商品规格库存记录的库存
            if(linkedSkuIdMap.containsKey(productBuyInItem.getSkuId())){
                List<Long> linkedSkuIdList2=linkedSkuIdMap.get(productBuyInItem.getSkuId());
                for(Long linkedSkuId:linkedSkuIdList2) {
                    SkuStock linkedSkuStock=linkedSkuStockMap.get(linkedSkuId);
                    //规格库存的数量
                    BigDecimal linkedSkuStockStock=linkedSkuStock.getStock();
                    //规格库存的数量+入库的数量
                    linkedSkuStockStock = linkedSkuStockStock.add(productBuyInItemQty);
                    linkedSkuStock.setStock(linkedSkuStockStock);
                    updateSkuStockList.add(linkedSkuStock);
                }

            }
        }
        //批量修改规格库存数量
        skuStockService.saveOrUpdateBatch(updateSkuStockList);
    }
    //规格库存减去入库数量
    public void skuReduceBuyInStock(List<ProductBuyInItem> productBuyInItemDtoList) {
        //获取规格Id
        List<Long> skuIdList = productBuyInItemDtoList.stream().map(ProductBuyInItem::getSkuId).collect(Collectors.toList());
        List<SkuStock> skuStockList=skuStockService.listByIds(skuIdList);
        Map<Long, SkuStock> skuStockMap=new HashMap<>();
        if(CollectionUtil.isNotEmpty(skuStockList)){
            skuStockMap = skuStockList.stream().collect(Collectors.toMap(SkuStock::getId, v -> v));
        }
        List<SkuStock> updateSkuStockList=new ArrayList<>();
        //将规格库存里有的商品数量减去入库的商品数量
        for(ProductBuyInItem productBuyInItem : productBuyInItemDtoList){
            SkuStock skuStock=skuStockMap.get(productBuyInItem.getSkuId());
            //规格库存的数量
            BigDecimal skuStockStock = skuStock.getStock();
            //获取入库的数量
            BigDecimal productBuyInItemQty = productBuyInItem.getInstoreQty();
            //规格库存的数量减去入库的数量
            skuStockStock = skuStockStock.subtract(productBuyInItemQty) ;
            skuStock.setStock(skuStockStock);
            updateSkuStockList.add(skuStock);
        }
        //批量修改规格库存数量
        skuStockService.saveOrUpdateBatch(updateSkuStockList);
    }


    //修改商品库存
    public void updateProductStock(ProductBuyIn productBuyIn, List<ProductBuyInItemDto> productBuyInItemDtoList) {
        //添加仓库库存
        //用于修改的商品库存
        List<ProductStock> updateProductStockList=new ArrayList<>();
        //用于添加的商品库存
        List<ProductStock> addProductStockList=new ArrayList<>();
        //获取仓库Id
        Long warehouseId=productBuyIn.getWarehouseId();
        //获取规格Id
        List<Long> skuIdList = productBuyInItemDtoList.stream().map(ProductBuyInItemDto::getSkuId).collect(Collectors.toList());
        //根据仓库id、规格id查询商品的库存信息
        List<ProductStock> productStockList= productStockService.list(new LambdaQueryWrapper<ProductStock>().eq(ProductStock::getWarehouseId,warehouseId).in(ProductStock::getSkuId,skuIdList));

        //获取库存中有的规格Id
        List<Long> productStockSkuIdList = productStockList.stream().map(ProductStock::getSkuId).collect(Collectors.toList());
        Map<Long, ProductStock> productStockMap=new HashMap<>();
        if(CollectionUtil.isNotEmpty(productStockList)){
            productStockMap = productStockList.stream().collect(Collectors.toMap(ProductStock::getSkuId, v -> v));
        }

        //通过规格id查询关联的商品规格记录
        List<SkuStock> linkSkuStockList = skuStockService.list(new LambdaQueryWrapper<SkuStock>().in(SkuStock::getLinkSkuId,skuIdList));
        // 将商品规格记录组装成关联规格id-规格id的map记录
        /*Map<Long, List<Long>> linkSkuIdMap=new HashMap<>();
        // 将商品规格记录组装成关联规格id-商品规格的map记录
        Map<Long, SkuStock> linkSkuStockMap=new HashMap<>();// 将关联商品的库存信息组装成规格id-库存对象的map记录
        Map<Long, ProductStock> linkProductStockMap=new HashMap<>();*/
        if(CollectionUtil.isNotEmpty(linkSkuStockList)){
            //获取关联规格Id
            List<Long> linkSkuIdList = linkSkuStockList.stream().map(SkuStock::getId).collect(Collectors.toList());
            //根据仓库id、规格id查询关联商品的库存信息
            List<ProductStock> linkProductStockList= productStockService.list(new LambdaQueryWrapper<ProductStock>().eq(ProductStock::getWarehouseId,warehouseId).in(ProductStock::getSkuId,linkSkuIdList));

            /*if(CollectionUtil.isNotEmpty(linkProductStockList)){
                linkProductStockMap = linkProductStockList.stream().collect(Collectors.toMap(ProductStock::getSkuId, v -> v));
            }
            if(CollectionUtil.isNotEmpty(linkSkuStockList)){
                linkSkuIdMap = linkSkuStockList.stream()
                        .collect(Collectors.groupingBy(SkuStock::getLinkSkuId, Collectors.mapping(SkuStock::getId, Collectors.toList())));
                linkSkuStockMap = linkSkuStockList.stream().collect(Collectors.toMap(SkuStock::getId, v -> v));
            }*/
        }

        //用于修改商品库存的商品规格信息
        List<ProductBuyInItemDto> updateProductBuyInItemDtoList = productBuyInItemDtoList.stream().filter(user -> productStockSkuIdList.contains(user.getSkuId())).collect(Collectors.toList());
        //将库存里有的商品数量与入库的商品数量相加
        for(ProductBuyInItemDto productBuyInItem : updateProductBuyInItemDtoList){
            ProductStock productStock=productStockMap.get(productBuyInItem.getSkuId());
            //商品库存的数量
            BigDecimal productStockStock=productStock.getStock();
            //获取入库的数量
            BigDecimal productBuyInItemQty=productBuyInItem.getInstoreQty();
            //商品库存的数量+入库的数量
            productStockStock=productStockStock.add(productBuyInItemQty);
            productStock.setStock(productStockStock);
            updateProductStockList.add(productStock);

            // 修改关联商品的库存
            /*List<Long> linkSkuIdList2 = linkSkuIdMap.get(productBuyInItem.getSkuId());
            if(CollectionUtil.isNotEmpty(linkSkuIdList2)){
                for(Long linkSkuId : linkSkuIdList2){
                    if(linkProductStockMap.containsKey(linkSkuId)) {
                        ProductStock linkProductStock = linkProductStockMap.get(linkSkuId);
                        //关联商品库存的数量
                        BigDecimal linkProductStockStock = linkProductStock.getStock();
                        //关联商品库存的数量+入库的数量
                        linkProductStock.setStock(linkProductStockStock.add(productBuyInItemQty));
                        updateProductStockList.add(linkProductStock);
                    }
                }
            }*/

        }
        //批量修改商品库存
        productStockService.saveOrUpdateBatch(updateProductStockList);

        //用于新增的商品规格信息
        List<ProductBuyInItemDto> newProductBuyInItemDtoList = productBuyInItemDtoList.stream().filter(user -> !productStockSkuIdList.contains(user.getSkuId())).collect(Collectors.toList());
        //将库存里有的商品数量与入库的商品数量相加
        for(ProductBuyInItemDto productBuyInItem : newProductBuyInItemDtoList){
            ProductStock productStock=new ProductStock();
            //入库的数量
            productStock.setStock(productBuyInItem.getInstoreQty());
            //仓库Id
            productStock.setWarehouseId(warehouseId);
            //规格Id
            productStock.setSkuId(productBuyInItem.getSkuId());
            //商品Id
            productStock.setProductId(productBuyInItem.getProductId());
            //下限
            productStock.setLowerLimit(productBuyInItem.getLowerLimit());
            //上限
            productStock.setUpperLimit(productBuyInItem.getUpperLimit());
            addProductStockList.add(productStock);

            // 关联商品库存--这里也要判断规格库存记录是否已存在，否则会造成相同规格、相同仓库的记录有两条。
            // 现在取消掉更新被关联规格的库存也修改的逻辑，所以此判断没有加上，如果不取消，则要更改代码加上判断是否存在的逻辑。
            // 为什么取消掉此逻辑呢？因为如果一个商品的多个规格都关联了此商品，那么此商品入库入的库存，实际上是不知道入的是哪个规格的商品,
            // 因此放开组合商品不能入库的限制，由组合商品自己入库自己的规格库存
            /*List<Long> linkSkuIdList3 = linkSkuIdMap.get(productBuyInItem.getSkuId());
            if(CollectionUtil.isNotEmpty(linkSkuIdList3)){
                for (Long linkSkuId : linkSkuIdList3){
                    ProductStock linkProductStock=new ProductStock();
                    //入库的数量
                    linkProductStock.setStock(productBuyInItem.getInstoreQty());
                    //仓库Id
                    linkProductStock.setWarehouseId(warehouseId);
                    //规格Id
                    linkProductStock.setSkuId(linkSkuId);
                    //商品Id
                    linkProductStock.setProductId(linkSkuStockMap.get(linkSkuId).getProductId());
                    //下限
                    linkProductStock.setLowerLimit(productBuyInItem.getLowerLimit());
                    //上限
                    linkProductStock.setUpperLimit(productBuyInItem.getUpperLimit());
                    addProductStockList.add(linkProductStock);
                }
            }*/

        }
        //批量添加商品库存
        productStockService.saveBatch(addProductStockList);
        //规格库存添加入库数量
        addBuyInStock(productBuyInItemDtoList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(ProductBuyInDto productBuyInDto) {
        // 判断是草稿还是已提交状态
        Integer status = productBuyInDto.getStatus();
        if (status == null) {
            status = 100; // 默认为已提交状态
        }

        // 根据状态调用不同的方法
        if (status == 0) {
            // 草稿 不修改库存
            updateDraft(productBuyInDto);
            return;
        }
        //修改后的入库单
        ProductBuyIn productBuyIn=new ProductBuyIn();
        BeanUtils.copyProperties(productBuyInDto, productBuyIn);
        //获取入库单的id
        Long id=productBuyIn.getId();
        //修改前的入库单
        ProductBuyInVo originalProductBuyInVo=productBuyInService.getById(id);
        //修改商品入库单
        productBuyInService.updateById(productBuyIn);

        //删除被修改后不存在的明细
        LambdaQueryWrapper<ProductBuyInItem> lambdaQueryWrapper=new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ProductBuyInItem::getProductBuyInId,id);
        //获取修改后的明细
        List<ProductBuyInItemDto> productBuyInItemDtoList = productBuyInDto.getProductBuyInItemList();

        List<ProductBuyInItem> productBuyInItemList = new ArrayList<ProductBuyInItem>();
        //将ProductBuyInItemDto转成ProductBuyInItem
        for(ProductBuyInItemDto productBuyInItem:productBuyInItemDtoList){
            ProductBuyInItem productBuyInItemWto=new ProductBuyInItem();
            BeanUtils.copyProperties(productBuyInItem, productBuyInItemWto);
            productBuyInItemList.add(productBuyInItemWto);
        }
        //获取修改后的明细Id
        List<Long> productBuyInItemIdList = productBuyInItemDtoList.stream().filter(p-> p.getId()!=null).map(ProductBuyInItemDto::getId).collect(Collectors.toList());
        //获取修改前的明细
        List<ProductBuyInItem> originalProductBuyInItemList=productBuyInItemService.list(lambdaQueryWrapper);
        //获取修改前的的明细Id
        List<Long> originalProductBuyInItemIdList = originalProductBuyInItemList.stream().map(ProductBuyInItem::getId).collect(Collectors.toList());
        //获取修改后不存在的明细id
        List<Long> idList = originalProductBuyInItemIdList.stream().filter(t -> !productBuyInItemIdList.contains(t)).collect(Collectors.toList());
        //删除被修改后不存在的明细
        productBuyInItemService.removeByIds(idList);
        //添加新增的入库明细信息
        List<ProductBuyInItem> newProductBuyInItem=productBuyInItemList.stream().filter(p -> p.getId()==null).collect(Collectors.toList());
        productBuyInItemService.saveBatch(newProductBuyInItem);
        //修改原来的入库明细信息
        List<ProductBuyInItem> originalProductBuyInItem=productBuyInItemList.stream().filter(p -> p.getId()!=null).collect(Collectors.toList());
        productBuyInItemService.updateBatchById(originalProductBuyInItem);

        //删除之前添加的库存
        //获取修改前仓库Id
        Long originalWarehouseId=originalProductBuyInVo.getWarehouseId();
        //获取修改前的规格Id
        List<Long> originalSkuIdList = originalProductBuyInItemList.stream().map(ProductBuyInItem::getSkuId).collect(Collectors.toList());
        //根据仓库id、规格id查询修改前商品的库存信息
        List<ProductStock> originalProductStockList= productStockService.list(new LambdaQueryWrapper<ProductStock>().eq(ProductStock::getWarehouseId,originalWarehouseId).in(ProductStock::getSkuId,originalSkuIdList));
        //获取库存中有的规格Id
        List<Long> originalProductStockSkuIdList = originalProductStockList.stream().map(ProductStock::getSkuId).collect(Collectors.toList());
        Map<Long, ProductStock> originalProductStockMap=new HashMap<>();
        if(CollectionUtil.isNotEmpty(originalProductStockSkuIdList)){
            originalProductStockMap = originalProductStockList.stream().collect(Collectors.toMap(ProductStock::getSkuId, v -> v));
        }
        //用于修改商品库存的商品规格信息
        List<ProductBuyInItem> updateProductBuyInItemList = originalProductBuyInItemList.stream().filter(user -> originalProductStockSkuIdList.contains(user.getSkuId())).collect(Collectors.toList());
        //用于减去原来的商品库存
        List<ProductStock> subtractOriginalProductStockList=new ArrayList<>();
        //将库存里有的商品数量减去入库的商品数量
        for(ProductBuyInItem productBuyInItem : updateProductBuyInItemList){
            ProductStock productStock=originalProductStockMap.get(productBuyInItem.getSkuId());
            //商品库存的数量
            BigDecimal productStockStock=productStock.getStock();
            //获取入库的数量
            BigDecimal productBuyInItemQty=productBuyInItem.getInstoreQty();
            productStockStock=productStockStock.subtract(productBuyInItemQty);
            productStock.setStock(productStockStock);
            subtractOriginalProductStockList.add(productStock);
        }
        //批量修改商品库存（减去原来的商品库存）
        productStockService.saveOrUpdateBatch(subtractOriginalProductStockList);
        //规格库存减去入库数量
        skuReduceBuyInStock(updateProductBuyInItemList);
        //添加商品库存
        updateProductStock(productBuyIn, productBuyInItemDtoList);
    }

    @Override
    public void delete(ProductBuyInDto productBuyInDto) {
        //获取入库单的id
        Long id=productBuyInDto.getId();

        //入库单信息
        ProductBuyInVo productBuyInVo=productBuyInService.getById(id);
        LambdaQueryWrapper<ProductBuyInItem> lambdaQueryWrapper=new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ProductBuyInItem::getProductBuyInId,id);
        //获取入库的明细
        List<ProductBuyInItem> productBuyInItemList=productBuyInItemService.list(lambdaQueryWrapper);
        //获取入库明细Id
        List<Long> productBuyInItemIdList = productBuyInItemList.stream().map(ProductBuyInItem::getId).collect(Collectors.toList());
        //删除入库单
        productBuyInService.removeById(id);
        //删除入库明细
        productBuyInItemService.removeByIds(productBuyInItemIdList);

        //获取仓库Id
        Long warehouseId=productBuyInVo.getWarehouseId();
        //获取规格Id
        List<Long> skuIdList = productBuyInItemList.stream().map(ProductBuyInItem::getSkuId).collect(Collectors.toList());
        //根据仓库id、规格id查询商品的库存信息
        List<ProductStock> productStockList= productStockService.list(new LambdaQueryWrapper<ProductStock>().eq(ProductStock::getWarehouseId,warehouseId).in(ProductStock::getSkuId,skuIdList));
        //获取库存中有的规格Id
        List<Long> productStockSkuIdList = productStockList.stream().map(ProductStock::getSkuId).collect(Collectors.toList());
        Map<Long, ProductStock> productStockMap=new HashMap<>();
        if(CollectionUtil.isNotEmpty(productStockSkuIdList)){
            productStockMap = productStockList.stream().collect(Collectors.toMap(ProductStock::getSkuId, v -> v));
        }
        //用于修改商品库存的商品规格信息
        List<ProductBuyInItem> updateProductBuyInItemList = productBuyInItemList.stream().filter(product -> productStockSkuIdList.contains(product.getSkuId())).collect(Collectors.toList());
        //用于减去商品库存
        List<ProductStock> subtractProductStockList=new ArrayList<>();
        //将库存里有的商品数量减去入库的商品数量
        for(ProductBuyInItem productBuyInItemVo : updateProductBuyInItemList){
            ProductStock productStock=productStockMap.get(productBuyInItemVo.getSkuId());
            //商品库存的数量
            BigDecimal productStockStock=productStock.getStock();
            //获取入库的数量
            BigDecimal productBuyInItemQty=productBuyInItemVo.getInstoreQty();
            productStockStock=productStockStock.subtract(productBuyInItemQty);
            productStock.setStock(productStockStock);
            subtractProductStockList.add(productStock);
        }
        //批量修改商品库存（减去原来的商品库存）
        productStockService.saveOrUpdateBatch(subtractProductStockList);
        //规格库存减去入库数量
        skuReduceBuyInStock(updateProductBuyInItemList);
    }

    @Override
    public Map<String, Object> getReceiptNoAndUser() {
        //获取用户信息
        CurUserDto curUserDto = CurUserUtil.getHttpCurUser();
        Map<String,Object> map=new HashMap<String,Object>(3);
        SimpleDateFormat bartDateFormat = new SimpleDateFormat("yyyyMMdd");
        Date date = new Date();
        String time="IN-"+bartDateFormat.format(date);
        String maxBuyNo = productBuyInService.getReceiptNo(time);
        String buyNo="";
        if(StrUtil.isNotEmpty(maxBuyNo)){
            String code=maxBuyNo.substring(12);
            Long number=Long.parseLong(code);
            number=number+1;
            String str=number.toString();
            int num=str.length();
            for (int i=0;i<(5-num);i++){
                str="0"+str;
            }
            buyNo=time+"-"+(str);
        }else{
            buyNo=time+"-"+"00001";
        }
        map.put("preparerId",curUserDto.getUserId());
        map.put("preparerName",curUserDto.getNikeName());
        map.put("buyNo",buyNo);
        return map;
    }

    @Override
    public void exportProductBuyIn(ProductBuyInDto productBuyInDto) {
        // 设置导出最大数量
        HuToolExcelUtils.exportParamToMax(productBuyInDto);
        // 直接调用现有的查询方法
        IPage<ProductBuyInVo> voIPage = this.selectList(productBuyInDto);
        List<ProductBuyInVo> buyInList = voIPage.getRecords();

        HuToolExcelUtils.exportData(buyInList, "商品入库列表",
                item->{
                    ProductBuyInExcelVo excelVo = new ProductBuyInExcelVo();
                    BeanUtils.copyProperties(item, excelVo);

                    // 格式化入库日期
                    if (item.getBuyDate() != null) {
                        excelVo.setBuyDate(DateUtil.format(item.getBuyDate(), "yyyy-MM-dd HH:mm:ss"));
                    }

                    return excelVo;
                });
    }

    /**
     * 保存为草稿
     *
     * @param productBuyInDto 需要保存的草稿信息
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveDraft(ProductBuyInDto productBuyInDto) {
        ProductBuyIn productBuyIn = new ProductBuyIn();
        BeanUtils.copyProperties(productBuyInDto, productBuyIn);

        // 主表id
        Long id = IDUtil.getId();
        productBuyIn.setId(id);


        BigDecimal totalAmount = new BigDecimal("0");
        BigDecimal totalQty = new BigDecimal("0");
        // 如果有明细数据，保存明细（草稿状态允许明细为空或不完整）
        List<ProductBuyInItemDto> productBuyInItemDtoList = productBuyInDto.getProductBuyInItemList();
        productBuyInItemDtoList = productBuyInItemDtoList.stream().filter(data -> data != null && data.getId() != null).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(productBuyInItemDtoList)) {
            List<ProductBuyInItem> productBuyInItemList = new ArrayList<>();

            for (ProductBuyInItemDto productBuyInItem : productBuyInItemDtoList) {
                productBuyInItem.setProductBuyInId(id);
                ProductBuyInItem productBuyInItemWto = new ProductBuyInItem();
                BeanUtils.copyProperties(productBuyInItem, productBuyInItemWto);
                productBuyInItemList.add(productBuyInItemWto);
            }

            // 保存明细信息
            productBuyInItemService.saveBatch(productBuyInItemList);
        }
        productBuyIn.setBuyAmount(totalAmount);
        productBuyIn.setBuyQuantity(totalQty);
        // 保存主表信息
        productBuyInService.save(productBuyIn);
    }

    /**
     * 更新草稿
     *
     * @param productBuyInDto 需要更新的草稿信息
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateDraft(ProductBuyInDto productBuyInDto) {
        // 修改后的入库单
        ProductBuyIn productBuyIn = new ProductBuyIn();
        BeanUtils.copyProperties(productBuyInDto, productBuyIn);

        // 获取入库单的id
        Long id = productBuyIn.getId();

        List<ProductBuyInItemDto> productBuyInItemDtoList = productBuyInDto.getProductBuyInItemList();

        // 计算totalAmount和totalQty逻辑
        if (CollectionUtil.isNotEmpty(productBuyInItemDtoList)) {
            //获取商品辅助单位信息
            List<Long> productIdList = productBuyInItemDtoList.stream().map(ProductBuyInItemDto::getProductId).collect(Collectors.toList());
            List<ProductSecUnitVo> productSecUnitList = productSecUnitService.getByProductIds(productIdList);
            Map<String, ProductSecUnitVo> unitMap = new HashMap<>();
            if (CollectionUtil.isNotEmpty(productSecUnitList)) {
                //key:商品id_辅助单位id
                unitMap = productSecUnitList.stream().collect(Collectors.toMap(e -> e.getProductId() + "_" + e.getSecUnitId(), e -> e));
            }

            //单据总金额、总数量
            BigDecimal totalAmount = new BigDecimal("0");
            BigDecimal totalQty = new BigDecimal("0");

            //计算总金额和总数量
            for(ProductBuyInItemDto productBuyInItem : productBuyInItemDtoList){
                if (productBuyInItem.getInstoreQty() != null && productBuyInItem.getInstorePrice() != null) {
                    //对金额进行计算
                    BigDecimal inAmount = productBuyInItem.getInstoreQty().multiply(productBuyInItem.getInstorePrice()).setScale(2, BigDecimal.ROUND_HALF_UP);
                    productBuyInItem.setInstoreAmount(inAmount);
                    totalAmount = totalAmount.add(inAmount);

                    //将下单选择的单位转换为基本单位，同时要将明细的数量、单价和单据的总数量都要进行对应的转换
                    String unitKey = productBuyInItem.getProductId() + "_" + (productBuyInItem.getInUnitId() == null ? "" : productBuyInItem.getInUnitId());
                    if(unitMap.containsKey(unitKey)){
                        ProductSecUnitVo secUnitVo = unitMap.get(unitKey);
                        //是辅助单位，则要进行转换
                        BigDecimal inQty = productBuyInItem.getInstoreQty().multiply(new BigDecimal(secUnitVo.getUnitd())).divide(new BigDecimal(secUnitVo.getSecUnitd()), 2, BigDecimal.ROUND_HALF_UP);
                        BigDecimal inPrice = inAmount.divide(inQty, 2, BigDecimal.ROUND_HALF_UP);
                        productBuyInItem.setInstorePrice(inPrice);
                        productBuyInItem.setInstoreQty(inQty);
                        totalQty = totalQty.add(inQty);
                    }else{
                        totalQty = totalQty.add(productBuyInItem.getInstoreQty());
                    }
                }
            }
            productBuyIn.setBuyAmount(totalAmount);
            productBuyIn.setBuyQuantity(totalQty);
        }

        // 修改商品入库单
        productBuyInService.updateById(productBuyIn);

        // 删除原有明细
        LambdaQueryWrapper<ProductBuyInItem> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ProductBuyInItem::getProductBuyInId, id);
        productBuyInItemService.remove(lambdaQueryWrapper);

        // 如果有新的明细数据，保存新明细
        if (CollectionUtil.isNotEmpty(productBuyInItemDtoList)) {
            List<ProductBuyInItem> productBuyInItemList = new ArrayList<>();
            for (ProductBuyInItemDto productBuyInItem : productBuyInItemDtoList) {
                productBuyInItem.setProductBuyInId(id);
                productBuyInItem.setId(null);
                ProductBuyInItem productBuyInItemWto = new ProductBuyInItem();
                BeanUtils.copyProperties(productBuyInItem, productBuyInItemWto);
                productBuyInItemList.add(productBuyInItemWto);
            }

            // 保存新的明细信息
            productBuyInItemService.saveBatch(productBuyInItemList);
        }
    }

}
