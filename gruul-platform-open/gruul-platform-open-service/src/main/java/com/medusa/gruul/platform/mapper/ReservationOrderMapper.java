package com.medusa.gruul.platform.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.medusa.gruul.platform.api.entity.ReservationOrder;
import com.medusa.gruul.platform.model.dto.ReservationPageQueryDto;

import com.medusa.gruul.platform.model.vo.ReservationOrderVo;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 预约订单Mapper接口
 *
 * <AUTHOR>
 * @since 2025-06-10
 */
@Repository
public interface ReservationOrderMapper extends BaseMapper<ReservationOrder> {
    
    /**
     * 统计职员好评数（满意评价的数量）
     *
     * @param employeeId 职员ID
     * @return 好评数
     */
    int countEmployeeGoodComments(@Param("employeeId") Long employeeId);

    /**
     * 统计职员服务人数（待评价和已完成状态的预约单数量）
     *
     * @param employeeId 职员ID
     * @return 服务人数
     */
    int countEmployeeServiceNumber(@Param("employeeId") Long employeeId);

    /**
     * 统计用户在指定时间段内的预约数量
     *
     * @param userId 用户ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 预约数量
     */
    int countUserReservationInTimeRange(@Param("userId") Long userId, 
                                       @Param("startTime") LocalDateTime startTime, 
                                       @Param("endTime") LocalDateTime endTime);
    
    /**
     * 统计职员在指定时间段内的预约数量
     *
     * @param employeeId 职员ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 预约数量
     */
    int countEmployeeReservationInTimeRange(@Param("employeeId") Long employeeId, 
                                          @Param("startTime") LocalDateTime startTime, 
                                          @Param("endTime") LocalDateTime endTime);
    
    /**
     * 查询指定时间段内已被预约的职员ID列表
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 职员ID列表
     */
    List<Long> listBookedEmployeeIds(@Param("startTime") LocalDateTime startTime, 
                                   @Param("endTime") LocalDateTime endTime);

    /**
     * 分页查询预约订单
     *
     * @param page 分页参数
     * @param queryDto 查询条件
     * @return 分页结果
     */
    IPage<ReservationOrderVo> listPage(Page<ReservationOrder> page, @Param("params") ReservationPageQueryDto queryDto);
    
    /**
     * 查询用户的预约列表 TODO 优化 4join
     * @param params
     * @return 预约订单列表
     */
    List<ReservationOrderVo> getUserReservations(@Param("params") ReservationPageQueryDto params);
    
    /**
     * 查询预约详情
     *
     * @param id 预约单ID
     * @return 预约详情
     */
    ReservationOrderVo getReservationDetail(@Param("id") Long id);
} 