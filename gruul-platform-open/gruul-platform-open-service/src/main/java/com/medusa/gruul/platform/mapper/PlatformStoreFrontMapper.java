package com.medusa.gruul.platform.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.medusa.gruul.platform.api.entity.PlatformDepartment;
import com.medusa.gruul.platform.api.entity.PlatformStoreFront;
import com.medusa.gruul.platform.api.model.vo.DepartmentVo;
import com.medusa.gruul.platform.api.model.vo.StoreFrontVo;
import com.medusa.gruul.platform.model.param.StoreFrontParam;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 17:25 2024/10/8
 */
@Repository
public interface PlatformStoreFrontMapper extends BaseMapper<PlatformStoreFront> {

    /**
     * 分页查询门店信息
     * @param departmentVoPage
     * @param storeFrontParam
     * @return
     */
    IPage<StoreFrontVo> searchStoreFront(Page<DepartmentVo> departmentVoPage, @Param("params")StoreFrontParam storeFrontParam);


    /**
     * 通过用户id获取门店信息
     * @param accountId
     * @return
     */
    List<StoreFrontVo> getStoreFrontVoByAccountId(@Param("accountId")String accountId);

    /**
     * 获取最大classCode
     * @param parentCode
     * @return
     */
    String getMaxCode(@Param("parentCode")String parentCode);

    /**
     * 获取下个门店编号-父级
     * @return
     */
    String getLastStoreNumber();
    /**
     * 获取下个门店编号-非父级
     * @param parentCode
     * @return
     */
    String getLastStoreNumberByParentCode(@Param("parentCode")String parentCode);

    /**
     * 获取下级门店个数
     * @param parentCode
     * @return
     */
    Integer getNextCount(@Param("parentCode")String parentCode);
}
