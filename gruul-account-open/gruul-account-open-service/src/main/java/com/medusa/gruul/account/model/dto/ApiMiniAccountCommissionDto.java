package com.medusa.gruul.account.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 14:54 2025/6/9
 */
@Data
@ApiModel(value = "小程序转赠佣金dto")
public class ApiMiniAccountCommissionDto {

    @ApiModelProperty(value = "小程序用户id")
    private String userId;
    @ApiModelProperty(value = "手机号")
    private String phone;
    @ApiModelProperty(value = "校验类型,1001-短信登录校验," +
            "1002-账号注册验证手机号," +
            "1003-用户手机号换绑校验," +
            "1004-用户修改密码," +
            "1005-用户信息修改," +
            "1006-用户删除自身店铺," +
            "1007-用户忘记密码找回," +
            "1008-代理申请," +
            "1009-代理银行卡手机号校验," +
            "1010-代理银行卡手机号换绑," +
            "1011-代理账号换绑," +
            "1012-代理修改密码," +
            "1013-修改支付密码," +
            "1014-验证代用户下单," +
            "1015-支付短信验证码," +
            "")
    private Integer type;
    /**
     * 验证码
     */
    @ApiModelProperty(value = "验证码")
    private String smsCode;
    /**
     * 支付密码
     */
    @ApiModelProperty(value = "支付密码")
    private String payPwd;
    /**
     * 转赠余额
     */
    @ApiModelProperty(value = "转赠余额")
    private BigDecimal amount;

}
