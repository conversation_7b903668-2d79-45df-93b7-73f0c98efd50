package com.medusa.gruul.platform.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.medusa.gruul.account.api.entity.MiniAccount;
import com.medusa.gruul.account.api.feign.RemoteMiniAccountService;
import com.medusa.gruul.account.api.model.AccountInfoDto;
import com.medusa.gruul.common.core.constant.enums.MessageTypeEnum;
import com.medusa.gruul.common.core.constant.enums.UseTypeEnum;
import com.medusa.gruul.common.core.exception.ServiceException;
import com.medusa.gruul.common.core.util.CurUserUtil;
import com.medusa.gruul.common.core.util.Result;
import com.medusa.gruul.common.core.util.StringUtil;
import com.medusa.gruul.common.core.util.SystemCode;
import com.medusa.gruul.common.data.tenant.ShopContextHolder;
import com.medusa.gruul.common.data.tenant.TenantContextHolder;
import com.medusa.gruul.common.dto.CurUserDto;
import com.medusa.gruul.platform.api.entity.*;
import com.medusa.gruul.platform.api.enums.PlatformPositionEnums;
import com.medusa.gruul.platform.api.enums.ReservationEnums;
import com.medusa.gruul.platform.api.model.dto.ReservationOrderCancelMessage;
import com.medusa.gruul.platform.api.model.vo.*;
import com.medusa.gruul.platform.mapper.*;
import com.medusa.gruul.platform.model.dto.*;

import com.medusa.gruul.platform.model.param.EmployeeParam;
import com.medusa.gruul.platform.model.param.EmployeeScheduleParam;
import com.medusa.gruul.platform.model.vo.*;
import com.medusa.gruul.platform.mq.Sender;
import com.medusa.gruul.platform.service.*;
import com.medusa.gruul.shops.api.entity.ShopsPartner;
import com.medusa.gruul.shops.api.feign.RemoteShopsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 预约服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-10
 */
@Service
@Slf4j
public class ReservationServiceImpl extends ServiceImpl<ReservationOrderMapper, ReservationOrder> implements IReservationOrderService {

    @Resource
    private ServiceContentMapper serviceContentMapper;

    @Resource
    private PlatformEmployeeMapper platformEmployeeMapper;

    @Resource
    private PlatformPositionMapper platformPositionMapper;

    @Resource
    private PlatformEmployeePositionMapper platformEmployeePositionMapper;
    @Resource
    private PlatformShopMessageMapper platformShopMessageMapper;

    @Autowired
    private RemoteShopsService remoteShopsService;

    @Autowired
    private IReservationOrderEvaluateService evaluateService;

    @Autowired
    private Sender sender;
    @Override
    public List<ServiceContentVo> listEnabledServices() {
        // 查询启用状态的服务项目
        LambdaQueryWrapper<ServiceContent> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ServiceContent::getStatus, PlatformPositionEnums.StatusEnum.ENABLED.getCode()) ;// 1-启用状态

        List<ServiceContent> serviceContentList = serviceContentMapper.selectList(wrapper);

        // 转换为VO
        List<ServiceContentVo> voList = new ArrayList<>();
        for (ServiceContent content : serviceContentList) {
            ServiceContentVo vo = new ServiceContentVo();
            BeanUtils.copyProperties(content, vo);
            voList.add(vo);
        }

        return voList;
    }

    @Override
    public List<ReservationEmployeeVo> listEmployeesWithPosition(EmployeeScheduleDto params) {

        EmployeeParam employeeParam = new EmployeeParam();
        // 根据状态查询
        if (params.getWorkStatus() != null){
            employeeParam.setWorkStatus(params.getWorkStatus());
        }
        // 排班日期
        if (params.getScheduleDate() != null){
            employeeParam.setScheduleDate(params.getScheduleDate());
        }else {
            employeeParam.setScheduleDate(LocalDate.now());
        }

        IPage<EmployeeVo> employeeVoIPage = platformEmployeeMapper.searchEmployee(new Page<>(1, Integer.MAX_VALUE), employeeParam);
        List<EmployeeVo> employeeList = employeeVoIPage.getRecords();
        if (employeeList.isEmpty()) {
            return new ArrayList<>();
        }

        // 获取职员ID列表
        List<Long> employeeIds = employeeList.stream().map(EmployeeVo::getId).collect(Collectors.toList());

        // 查询职员关联的职位
        LambdaQueryWrapper<PlatformEmployeePosition> epWrapper = new LambdaQueryWrapper<>();
        epWrapper.in(PlatformEmployeePosition::getEmployeeId, employeeIds);
        List<PlatformEmployeePosition> employeePositions = platformEmployeePositionMapper.selectList(epWrapper);
        // 分组
        Map<Long, List<PlatformEmployeePosition>> employeePositionMap = employeePositions.stream()
                .collect(Collectors.groupingBy(PlatformEmployeePosition::getEmployeeId));


        // 查询所有相关职位
        Set<Long> positionIds = employeePositions.stream()
                .map(PlatformEmployeePosition::getPositionId)
                .collect(Collectors.toSet());

        Map<Long, PlatformPosition> positionMap = new HashMap<>();
        if (!positionIds.isEmpty()) {
            LambdaQueryWrapper<PlatformPosition> positionWrapper = new LambdaQueryWrapper<>();
            positionWrapper.in(PlatformPosition::getId, positionIds).eq(PlatformPosition::getStatus, PlatformPositionEnums.StatusEnum.ENABLED.getCode());

            List<PlatformPosition> positions = platformPositionMapper.selectList(positionWrapper);
            positionMap = positions.stream().collect(Collectors.toMap(PlatformPosition::getId, position -> position));
        }
        // 转换为VO
        List<ReservationEmployeeVo> voList = new ArrayList<>();
        for (EmployeeVo employee : employeeList) {
            ReservationEmployeeVo vo = new ReservationEmployeeVo();
            BeanUtils.copyProperties(employee, vo);

            // 设置职位信息
            List<PlatformPositionVo> positionVoList = new ArrayList<>();
            List<PlatformEmployeePosition> positions = employeePositionMap.getOrDefault(employee.getId(), new ArrayList<>(0));

            for (PlatformEmployeePosition ep : positions) {
                PlatformPosition position = positionMap.get(ep.getPositionId());
                if (position != null) {
                    PlatformPositionVo positionVo = new PlatformPositionVo();
                    BeanUtils.copyProperties(position, positionVo);
                    positionVoList.add(positionVo);
                }
            }

            vo.setPositionList(positionVoList);
            // 总好评数 = 初始好评数 + 满意评价数
            Integer goodComments = this.baseMapper.countEmployeeGoodComments(employee.getId());
            vo.setTotalHighNumber((employee.getHighNumber() + goodComments));
            // 总服务人数 = 初始服务人数 + 待评价和已完成状态的预约单数量
            Integer serviceNumber = this.baseMapper.countEmployeeServiceNumber(employee.getId());
            vo.setTotalServiceNumber((employee.getServiceNumber() + serviceNumber));

            voList.add(vo);
        }

        return voList;
    }
    /**
     * 获取可预约的时间段
     * @param queryDto 查询参数
     * @return 预约时间段结果
     */
    public ReservationTimeSlotVo getTimeSlots(TimeSlotQueryDto queryDto) {
        if (null== queryDto.getShopId()){
            throw new ServiceException("缺少shop信息",SystemCode.PARAM_MISS.getCode());
        }

        // 获取商铺信息
        ShopsPartner shopsPartner = remoteShopsService.getByShopId(Long.valueOf(queryDto.getShopId()));

        // 默认店铺的营业时间和营业日（实际应从数据库获取）
   /*     LocalTime businessBeginTime = LocalTime.of(9, 0); // 默认9:00开始营业
        LocalTime businessEndTime = LocalTime.of(18, 0);  // 默认18:00结束营业
        String businessDays = "星期一,星期二,星期三,星期四,星期五"; // 默认工作日营业*/

        if (shopsPartner ==  null){
            throw new ServiceException("获取不到该店铺信息",SystemCode.PARAM_MISS.getCode());
        }else if (StrUtil.isBlank(shopsPartner.getTradeDate())){
            throw new ServiceException("该店铺未设置营业日",SystemCode.PARAM_MISS.getCode());
        }else if (shopsPartner.getBusinessBeginHours() == null || shopsPartner.getBusinessEndHours() == null){
            throw new ServiceException("该店铺未设置营业时间",SystemCode.PARAM_MISS.getCode());
        }

        // 如果商铺信息中有营业时间配置，则使用商铺配置
        LocalTime businessBeginTime = shopsPartner.getBusinessBeginHours().toInstant().atZone(ZoneId.of("Asia/Shanghai")).toLocalTime();
        LocalTime businessEndTime = shopsPartner.getBusinessEndHours().toInstant().atZone(ZoneId.of("Asia/Shanghai")).toLocalTime();
        String businessDays = shopsPartner.getTradeDate();
        LocalDateTime startTime = queryDto.getStartTime();
        LocalDateTime now = LocalDateTime.now();
        if (startTime==null){
            startTime = now;
        }

        // 可预约日期列表
        List<DateSlotVo> availableDates;

        // 可预约时间段列表
        List<TimeSlotVo> availableTimeSlots;

        if (startTime.toLocalDate().isBefore(LocalDate.now())){
            availableDates = new ArrayList<>();
            availableTimeSlots = new ArrayList<>();
        }else {
            availableDates = getAvailableDates(businessDays,now);
            availableTimeSlots = getAvailableTimeSlots(businessBeginTime, businessEndTime,availableDates.get(0),startTime);
        }




        ReservationTimeSlotVo result = new ReservationTimeSlotVo()
                .setAvailableTimeSlots(availableTimeSlots).setAvailableDates(availableDates)
                .setBusinessStartTime(businessBeginTime.toString())
                .setBusinessEndTime(businessEndTime.toString())
                .setBusinessDays(businessDays);

        return result;
    }

    /**
     * 可预约日期列表（7天内的营业日）
     * @param businessDays 营业日字符串
     * @return 可预约日期列表
     */
    private List<DateSlotVo> getAvailableDates(String businessDays,LocalDateTime startTime) {
        List<DateSlotVo> availableDates = new ArrayList<>();
        LocalDate today = startTime.toLocalDate();

        // 解析营业日
        List<String> businessDayList = Arrays.asList(businessDays.split(","));
        // 生成本周的7天日期 计算当前日期对应的本周的开始日期
        /*        LocalDate weekStart = today.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));*/
        for (int i = 0; i < 7; i++) {
            LocalDate date = today.plusDays(i);
            String dayOfWeekChinese = getDayOfWeekChinese(date.getDayOfWeek());
            // 检查是否是营业日
            if (businessDayList.contains(dayOfWeekChinese)) {
                DateSlotVo dateSlot = new DateSlotVo();
                dateSlot.setDate(date);
                dateSlot.setDayOfWeek(dayOfWeekChinese);
                dateSlot.setDateStr(date.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                dateSlot.setIsToday(date.equals(today));
                availableDates.add(dateSlot);
            }
        }

        return availableDates;
    }

    /**
     * 可预约时间段列表
     * @param businessBeginTime 营业开始时间
     * @param businessEndTime 营业结束时间
     * @param queryStartTime 查询的开始时间
     * @return 可预约时间段列表
     */
    private List<TimeSlotVo> getAvailableTimeSlots(LocalTime businessBeginTime, LocalTime businessEndTime,DateSlotVo firstDate, LocalDateTime queryStartTime) {
        List<TimeSlotVo> timeSlots = new ArrayList<>();
        LocalDateTime now = LocalDateTime.now();

        // 如果查询时间在营业开始时间之前，则设置为营业开始时间
        if (firstDate.getDate().isAfter(queryStartTime.toLocalDate())){
            queryStartTime = firstDate.getDate().atTime(businessBeginTime);
        }
        // 判断查询日期是否是今天
        boolean isToday = queryStartTime.toLocalDate().equals(now.toLocalDate());

        // 如果是今天，需要延后一个小时
        LocalTime startTime = businessBeginTime;
        if (isToday) {
            // 处理前端只传入日期
            queryStartTime = LocalDateTime.of(queryStartTime.toLocalDate(), now.toLocalTime());

            LocalTime delayedTime = queryStartTime.toLocalTime().plusHours(1);
            startTime = delayedTime.isAfter(businessBeginTime) ? delayedTime : businessBeginTime;
            // 调整到最近的半小时整点
            int minute = startTime.getMinute();
            if (minute > 0 && minute <= 30) {
                startTime = startTime.withMinute(30).withSecond(0).withNano(0);
            } else if (minute > 30) {
                startTime = startTime.plusHours(1).withMinute(0).withSecond(0).withNano(0);
            }
        }

        // 生成半小时间隔的时间段
        LocalTime currentSlotTime = startTime;
        while (currentSlotTime.isBefore(businessEndTime)) {
            LocalTime endSlotTime = currentSlotTime.plusMinutes(30);

            // 确保结束时间不超过营业结束时间
            if (endSlotTime.isAfter(businessEndTime)) {
                break;
            }
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm");
            TimeSlotVo timeSlot = new TimeSlotVo();
            timeSlot.setStartTime(currentSlotTime);
            timeSlot.setEndTime(endSlotTime);
            timeSlot.setDisplay( currentSlotTime.format(formatter) + "-" + endSlotTime.format(formatter));

            timeSlots.add(timeSlot);
            currentSlotTime = endSlotTime;
        }

        return timeSlots;
    }

/*    private String getDayOfWeekChinese(DayOfWeek dayOfWeek) {
        switch (dayOfWeek) {
            case MONDAY: return "星期一";
            case TUESDAY: return "星期二";
            case WEDNESDAY: return "星期三";
            case THURSDAY: return "星期四";
            case FRIDAY: return "星期五";
            case SATURDAY: return "星期六";
            case SUNDAY: return "星期日";
            default: return "";
        }
    }*/
    /**
     * 获取中文星期
     * @param dayOfWeek 星期枚举
     * @return 中文星期
     */
    private String getDayOfWeekChinese(DayOfWeek dayOfWeek) {
        switch (dayOfWeek) {
            case MONDAY: return "周一";
            case TUESDAY: return "周二";
            case WEDNESDAY: return "周三";
            case THURSDAY: return "周四";
            case FRIDAY: return "周五";
            case SATURDAY: return "周六";
            case SUNDAY: return "周日";
            default: return "";
        }
    }
    @Override
    public List<ReservationEmployeeVo>  listAvailableEmployees(TimeSlotQueryDto queryDto) {
        LocalDateTime startDataTime = queryDto.getStartTime();
        LocalDateTime endDataTime =  queryDto.getEndTime();
        // 计算 时间范围
        if (null == queryDto.getStartTime()){
            ReservationTimeSlotVo timeSlots = getTimeSlots(queryDto);
            startDataTime = LocalDateTime.of(timeSlots.getAvailableDates().get(0).getDate(),timeSlots.getAvailableTimeSlots().get(0).getStartTime());
        }
        if (null == queryDto.getEndTime() || queryDto.getEndTime().isBefore(queryDto.getStartTime())){
            endDataTime = startDataTime.plusMinutes(30);
        }
        // 获取所有上班状态的职员
        EmployeeScheduleDto params = new EmployeeScheduleDto();
        params.setWorkStatus(ReservationEnums.WorkStatus.WORKING.getCode());
        params.setScheduleDate(startDataTime.toLocalDate());
        List<ReservationEmployeeVo> workingEmployees = listEmployeesWithPosition(params);

        List<Long> workingEmployeeIds = workingEmployees.stream()
                .map(ReservationEmployeeVo::getId)
                .collect(Collectors.toList());

        // 指定时间的 已被预约的记录
        List<ReservationOrder> normalOrders = new ArrayList<>();
        if (!workingEmployeeIds.isEmpty()) {
            LambdaQueryWrapper<ReservationOrder> wrapper = new LambdaQueryWrapper<>();
            wrapper.in(ReservationOrder::getEmployeeId, workingEmployeeIds)
                    .ge(ReservationOrder::getReservationTimeStart, startDataTime)
                    .le(ReservationOrder::getReservationTimeEnd, endDataTime)
                    .eq(ReservationOrder::getStatus, ReservationEnums.OrderStatus.NORMAL.getCode());
            normalOrders = this.baseMapper.selectList(wrapper);
        }
        Map<Long, ReservationOrder> normalOrderMap = normalOrders.stream().collect(Collectors.toMap(ReservationOrder::getEmployeeId, item -> item));

        // 是否可被预约
        ArrayList<ReservationEmployeeVo> canEmployee = new ArrayList<>();
        ArrayList<ReservationEmployeeVo> noEmployee = new ArrayList<>();
        // 循环职员 处理已被预约。
        for (ReservationEmployeeVo employee : workingEmployees) {
            Long employeeId = employee.getId();
            //不存在 可预约
            if (normalOrderMap.get(employeeId)==null){
                employee.setAvailable(1);
                canEmployee.add(employee);
            }else {
                employee.setAvailable(0);
                noEmployee.add(employee);
            }
        }

        // 创建"到店分配"选项
        ReservationEmployeeVo defaultVo = new ReservationEmployeeVo();
        defaultVo.setId(0L)
                .setEmpFullName("到店分配")
                .setEmpNumber("0")
                .setPositionList(new ArrayList<>())
                .setTotalHighNumber(0)
                .setTotalServiceNumber(0)
                .setAvailable(1);
        ArrayList<ReservationEmployeeVo> list = new ArrayList<>(1+canEmployee.size() + noEmployee.size() );
        list.add(defaultVo);
        list.addAll(canEmployee);
        list.addAll(noEmployee);
        return list;
    }

    private List<ReservationOrder> getReservationOrders(List<Long> employeeIds, LocalDateTime dayStart, LocalDateTime dayEnd) {
        LambdaQueryWrapper<ReservationOrder> wrapper = new LambdaQueryWrapper<>();
        if (employeeIds == null || employeeIds.isEmpty()){
            return new ArrayList<>();
        }
        if (employeeIds.size()>1){
            wrapper.in(ReservationOrder::getEmployeeId, employeeIds);
        }else {
            wrapper.eq(ReservationOrder::getEmployeeId, employeeIds.get(0));
        }

        wrapper.ge(ReservationOrder::getReservationTimeStart, dayStart)
                .le(ReservationOrder::getReservationTimeEnd, dayEnd)
                .in(ReservationOrder::getStatus, ReservationEnums.OrderStatus.NORMAL.getCode()); // 正常、 todo 待评价是否算未完成

        List<ReservationOrder> employeeOrders = this.baseMapper.selectList(wrapper);
        return employeeOrders;
    }
    private CurUserDto getCurUser(){
        CurUserDto curUser = CurUserUtil.getHttpCurUser();
        if (curUser == null){
            throw new ServiceException("用户未登录");
        }
        return curUser;
    }

    @Override
    public Result<String> submitReservation(ReservationSubmitDto submitDto) {
        try {
            // 1. 校验
            if (submitDto.getMobile() == null || submitDto.getMobile().isEmpty()) {
                return Result.failed("手机号不能为空");
            }else if (submitDto.getReservationNumber() == null || submitDto.getReservationNumber() < 1) {
                return Result.failed("预约人数必须大于等于1");
            }else if (submitDto.getReservationTimeStart() == null || submitDto.getReservationTimeEnd() == null) {
                return Result.failed("预约时间段不能为空");
            }else if (submitDto.getServiceId() == null) {
                return Result.failed("预约项目不能为空");
            }else if (submitDto.getReservationTimeStart().isBefore(LocalDateTime.now()) ){
                return Result.failed("预约时间应在当前时间之后");
            }else if (StringUtil.isBlank(submitDto.getShopId())){
                return Result.failed("缺少店铺信息");
            }
            CurUserDto curUser = getCurUser();
            if (ObjectUtil.isNotNull(curUser)){
                submitDto.setUserId(Long.valueOf(curUser.getUserId()));
            }
            // 2. 检查用户在该时间段是否已有预约
            int userReservationCount = this.baseMapper.countUserReservationInTimeRange(
                    submitDto.getUserId(),
                    submitDto.getReservationTimeStart(),
                    submitDto.getReservationTimeEnd());
            if (submitDto.getEmployeeId()==null){
                // 设置为到店
                submitDto.setEmployeeId(0L);
            }
            if (userReservationCount > 0) {
                return Result.failed("您已预约，不能重复预约");
            }

            // 3. 如果选择了职员，检查该职员在所选时间段是否已被预约
            if (submitDto.getEmployeeId() != null && submitDto.getEmployeeId() != 0L) {
                int employeeReservationCount = this.baseMapper.countEmployeeReservationInTimeRange(
                        submitDto.getEmployeeId(),
                        submitDto.getReservationTimeStart(),
                        submitDto.getReservationTimeEnd());

                if (employeeReservationCount > 0) {
                    return Result.failed("该时间段已被约满，请选择其他时间段或者其他服务人员");
                }
            } else {
                // 4. 如果选择"到店分配"，检查所有上班职员在该时间段是否全部被预约
                // 获取所有上班状态的职员
                LambdaQueryWrapper<PlatformEmployee> empWrapper = new LambdaQueryWrapper<>();
                empWrapper.eq(PlatformEmployee::getWorkStatus, 1); // 1-上班状态

                List<PlatformEmployee> workingEmployees = platformEmployeeMapper.selectList(empWrapper);
                if (workingEmployees.isEmpty()) {
                    return Result.failed("当前没有上班的职员，无法预约");
                }

                List<Long> workingEmployeeIds = workingEmployees.stream()
                        .map(PlatformEmployee::getId)
                        .collect(Collectors.toList());

                // 查询该时间段已被预约的职员
                List<Long> bookedEmployeeIds = this.baseMapper.listBookedEmployeeIds(
                        submitDto.getReservationTimeStart(),
                        submitDto.getReservationTimeEnd());

                // 如果所有上班职员都已被预约，则不允许预约
                if (bookedEmployeeIds.size() >= workingEmployeeIds.size()) {
                    return Result.failed("该时间段已被约满，请选择其他时间段");
                }
            }

            // 5. 插入预约记录
            ReservationOrder reservationOrder = new ReservationOrder();
            BeanUtils.copyProperties(submitDto, reservationOrder);
            reservationOrder.setStatus(ReservationEnums.OrderStatus.NORMAL.getCode()); // 101-> 正常
            reservationOrder.setCreateTime(LocalDateTime.now());
            reservationOrder.setCreateUserId(submitDto.getUserId());
            reservationOrder.setCreateUserName(submitDto.getUserName());

            boolean saveResult = this.save(reservationOrder);
            if (!saveResult) {
                return Result.failed("预约失败，请稍后重试");
            }
            ServiceContent serviceContent = serviceContentMapper.selectById(submitDto.getServiceId());
            // 6. 插入消息记录
            PlatformShopMessage message = new PlatformShopMessage();
            message.setOrderId(reservationOrder.getId()+"");
            message.setUseType(UseTypeEnum.SHOP.getStatus()); // 2-商家通知
            message.setTitle(MessageTypeEnum.RESERVATION_MESSAGE.getDesc());
            message.setMessageType(MessageTypeEnum.RESERVATION_MESSAGE.getStatus()); // 7-预约单消息
            message.setStatus(0); // 0-未读
            message.setContent("您有一条新的预约单（编号："+reservationOrder.getId()+"），预约项目："+serviceContent.getServiceName()+
                    " 预约时间：" + submitDto.getReservationTimeStart().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm")) + "，" +
                    "预约人：" + curUser.getNikeName() + "，手机号：" + submitDto.getMobile());
            platformShopMessageMapper.insert(message);
            //  预约订单自动取消
            ReservationOrderCancelMessage cancelMessage = new ReservationOrderCancelMessage();
            cancelMessage.setOrderId(reservationOrder.getId());
            cancelMessage.setShopId(reservationOrder.getShopId());
            cancelMessage.setTenantId(TenantContextHolder.getTenantId());
            LocalDateTime now = LocalDateTime.now();
            Duration duration = Duration.between(now, reservationOrder.getReservationTimeEnd());
            long millis = duration.toMillis();
            sender.sendAutoCancelReservationOrderMessage(cancelMessage,millis);
            // 7. 返回结果
            return Result.ok("预约成功");
        } catch (Exception e) {
            log.error("提交预约失败", e);
            return Result.failed("预约失败，系统异常");
        }
    }



    @Override
    public List<ReservationOrderVo> getUserReservations(ReservationPageQueryDto queryDto) {
        CurUserDto curUser = getCurUser();
        if (ObjectUtil.isNotNull(curUser)){
            queryDto.setUserId(Long.valueOf(curUser.getUserId()));
        }

        //查询用户的预约列表
        List<ReservationOrderVo> voList = this.baseMapper.getUserReservations(queryDto);

        if (voList== null && voList.isEmpty()){
            return new ArrayList<>();
        }

      /*  Set<Long> shopIds = voList.stream().map(ReservationOrderVo::getShopId).collect(Collectors.toSet());
        Map<Long, ShopsPartner> shopMap = new HashMap<>();
        for (Long shopId : shopIds) {
            ShopsPartner shopsPartner = remoteShopsService.getByShopId(shopId);
            shopMap.put(shopId, shopsPartner);
        }*/

        // 设置操作权限
        LocalDateTime now = LocalDateTime.now();
        for (ReservationOrderVo vo : voList) {
        /*    if (shopMap.containsKey(vo.getShopId())){
               vo.setShopName(shopMap.get(vo.getShopId()).getName());
            }*/

            // 1. 未超时且正常状态的预约单可以取消
            boolean canCancel =  ReservationEnums.OrderStatus.NORMAL.getCode().equals(vo.getStatus()) &&
                    vo.getReservationTimeStart().isAfter(now);
            vo.setCanCancel(canCancel);

            // 2. 正常状态的预约单可以完成
            boolean canComplete = ReservationEnums.OrderStatus.NORMAL.getCode().equals(vo.getStatus());
            vo.setCanComplete(canComplete);

            // 3. 待评价状态的预约单可以评价
            boolean canEvaluate = ReservationEnums.OrderStatus.WAIT_EVALUATE.getCode().equals(vo.getStatus());
            vo.setCanEvaluate(canEvaluate);
        }

        return voList;
    }

    @Override
    public  Result<String>  cancelReservation(Long id,boolean isAutoCancel) {
        // 查询预约单
        ReservationOrder order = this.getById(id);
        // 检查预约单
        if (order == null ) {
            return Result.failed("找不到订单");
        }else if (!Objects.equals(order.getStatus(), ReservationEnums.OrderStatus.NORMAL.getCode())) {
            return Result.failed("订单当前状态不可以取消");
        }

        if (isAutoCancel){
            order.setStatus(ReservationEnums.OrderStatus.TIMEOUT_CANCEL.getCode()); //  超时取消
        }else {
            order.setStatus(ReservationEnums.OrderStatus.USER_CANCEL.getCode()); //  用户取消
        }

        order.setUpdateTime(LocalDateTime.now());

        boolean updateResult = this.updateById(order);
        if (!updateResult) {
            return Result.failed("取消失败");
        }
        ServiceContent serviceContent = serviceContentMapper.selectById(order.getServiceId());
        // 插入取消消息记录
        PlatformShopMessage message = new PlatformShopMessage();
        message.setOrderId(id.toString());
        message.setUseType(UseTypeEnum.SHOP.getStatus());
        message.setTitle(MessageTypeEnum.RESERVATION_MESSAGE.getDesc());
        message.setMessageType(MessageTypeEnum.RESERVATION_MESSAGE.getStatus()); // 7-预约单消息
        message.setStatus(0); // 0-未读
        message.setContent("预约单（编号："+order.getId()+"）已被用户取消，预约项目："+serviceContent.getServiceName()+
                " 预约时间：" + order.getReservationTimeStart().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm")) + "，" +
                "预约人：" + order.getUserName() + "，手机号：" + order.getMobile());

        platformShopMessageMapper.insert(message);

        return Result.ok("取消成功");
    }

    @Override
    public Result<String> completeReservation(Long id) {
        // 查询预约单
        ReservationOrder order = this.getById(id);
        // 检查预约单
        if (order == null ) {
            return Result.failed("找不到订单");
        }else if (!Objects.equals(order.getStatus(), ReservationEnums.OrderStatus.NORMAL.getCode())) {
            return Result.failed("订单当前状态不可以取消");
        }

        // 更新预约单状态为待评价
        order.setStatus(ReservationEnums.OrderStatus.WAIT_EVALUATE.getCode()); // 待评价
        order.setUpdateTime(LocalDateTime.now());

        return this.updateById(order) ? Result.ok("完成订单成功") : Result.failed("完成订单失败");
    }

    @Override
    public IPage<ReservationOrderVo> pageReservations(ReservationPageQueryDto queryDto) {
        return this.getBaseMapper().listPage(new Page<>(queryDto.getCurrent(), queryDto.getSize()), queryDto);
    }

    @Override
    public ReservationOrderVo getReservationDetail(Long id) {

        // 获取预约详情
        ReservationOrderVo vo = this.baseMapper.getReservationDetail(id);
        
        if (vo != null) {
            // 设置操作权限
            LocalDateTime now = LocalDateTime.now();


            // 1. 未超时且正常状态的预约单可以取消
            boolean canCancel =  ReservationEnums.OrderStatus.NORMAL.getCode().equals(vo.getStatus()) &&
                    vo.getReservationTimeStart().isAfter(now);
            vo.setCanCancel(canCancel);
            
            // 2. 正常状态的预约单可以完成
            boolean canComplete =  ReservationEnums.OrderStatus.NORMAL.getCode().equals(vo.getStatus());
            vo.setCanComplete(canComplete);
            
            // 3. 待评价状态的预约单可以评价
            boolean canEvaluate =  ReservationEnums.OrderStatus.WAIT_EVALUATE.getCode().equals(vo.getStatus());
            vo.setCanEvaluate(canEvaluate);
            if (vo.getShopId()!=null){
                ShopsPartner shopsPartner = remoteShopsService.getByShopId(vo.getShopId());
                vo.setShopName(shopsPartner.getName());
            }
            // 已完成 有评论信息
            if (ReservationEnums.OrderStatus.COMPLETED.getCode().equals(vo.getStatus())){
                ReservationOrderEvaluateVo evaluateVo = evaluateService.getByOrderId(id);
                vo.setEvaluateInfo(evaluateVo);
            }
        }
        
        return vo;
    }
} 