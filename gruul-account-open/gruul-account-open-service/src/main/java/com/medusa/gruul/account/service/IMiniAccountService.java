package com.medusa.gruul.account.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.medusa.gruul.account.api.entity.MiniAccount;
import com.medusa.gruul.account.api.entity.MiniAccountOauths;
import com.medusa.gruul.account.api.model.AccountInfoDto;
import com.medusa.gruul.account.api.model.MiniAccountBalanceDto;
import com.medusa.gruul.account.api.model.MiniAccountExtDto;
import com.medusa.gruul.account.api.model.message.AccountReturnBalanceMessage;
import com.medusa.gruul.account.api.model.message.UpgradeMemberLevelMessage;
import com.medusa.gruul.account.api.model.param.ChooseAccountParam;
import com.medusa.gruul.account.api.model.param.MemberSalesReportParam;
import com.medusa.gruul.account.api.model.vo.ChooseAccountVo;
import com.medusa.gruul.account.model.dto.*;
import com.medusa.gruul.account.model.param.*;
import com.medusa.gruul.account.model.vo.*;
import com.medusa.gruul.common.core.util.PageUtils;
import com.medusa.gruul.order.api.model.OrderVo;
import com.medusa.gruul.order.api.model.RevertMiniAccountBalanceMessage;
import com.medusa.gruul.account.model.vo.MemberSalesReportVo;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 小程序用户表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-11-18
 */
public interface IMiniAccountService extends IService<MiniAccount> {

    /**
     * 小程序用户登录
     *
     * @param code code值
     * @param inviteCode 邀请码
     * @return com.medusa.gruul.account.model.vo.LoginBaseInfoVo
     */
    LoginBaseInfoVo login(String code, String inviteCode);

    /**
     * 获取微信用户绑定的手机号
     *
     * @param decodePhoneInfo dto
     * @return java.lang.String
     */
    String decodePhoneInfo(DecodePhoneInfo decodePhoneInfo);

    /**
     * 更新用户基础信息
     *
     * @param updateUserBaseInfoDto dto
     */
    void updateUserBaseInfo(UpdateUserBaseInfoDto updateUserBaseInfoDto);

    /**
     * 代注册，帮别人注册
     * @param updateUserBaseInfoDto
     */
    void helpOtherRegister(UpdateUserBaseInfoDto updateUserBaseInfoDto);

    /**
     * 用户登录认证
     * @param dto
     */
    void userLoginAndAuth(UserLoginDto dto);

    /**
     * 更新用户会员等级
     *
     * @param memberId
     * @param memberLevelId
     */
    void updateMemberLeve(Long memberId, String memberLevelId);

    /**
     * 获取用户信息
     *
     * @param infoLevel 数据级别 1-基础信息 2-用户扩展数据
     * @return com.medusa.gruul.account.model.vo.UserInfoVo
     */
    UserInfoVo getUserInfo(Integer infoLevel);


    /**
     * 根据用户id获取用户基础数据
     *
     * @param userId 用户id
     * @return com.medusa.gruul.account.api.entity.MiniAccount
     */
    MiniAccount getByUserId(String userId);


    /**
     * 获取pc端用户列表
     *
     * @param nikeName              微信昵称
     * @param tagId                 标签id
     * @param orderSuccessStartTime 成交时间 2019-11-11 11:23:23
     * @param orderSuccessEndTime   成交时间 2019-11-11 11:23:23
     * @param page                  页数
     * @param size                  条数
     * @param sortType
     * @param id
     * @param superiorAndSubordinate
     * @return com.medusa.gruul.account.model.vo.UserListVo
     */
    PageUtils<List<UserListVo>> userList(String nikeName, String phone, Long tagId,
                                         String orderSuccessStartTime, String orderSuccessEndTime,  Integer page, Integer size, Integer sortType,Integer id,
                                         Integer superiorAndSubordinate,Long parentId,Long aboveParentId, String shopIds,Integer expireDays);

    /**
     * 获取小程序用户详情信息
     * @param userId
     * @return
     */
    MiniAccountDetailVo getMiniAccountDetail(String userId);


    /**
     * 外部系统获取用户列表
     *
     * @param page                  页数
     * @param size                  条数
     * @return com.medusa.gruul.account.model.vo.UserListVo
     */
    PageUtils<List<UserListVo>> externalUserList(Integer page, Integer size);

    /**
     *
     * @param page
     * @param size
     * @return
     */
    PageUtils<List<UserListVo>> getUserList(Integer page, Integer size);

    /**
     * 批量修改用户发送状态
     *
     * @param accountIds the order ids
     * @param    sendStatus
     * @return void
     * <AUTHOR>
     * @date 2022 /05/24 09:12
     */
    void updateSendStatus(List<String> accountIds,String sendStatus);


    /**
     * 获取用户信息接口
     *
     * @param userId 用户id
     * @param infos  [1,2,3]  1,基本信息,2,扩展信息,3-地址信息,4-授权信息
     * @return com.medusa.gruul.account.api.model.AccountInfoDto
     */
    AccountInfoDto accountInfo(String userId, List<Integer> infos);

    /**
     * 获取用户MiniAccountOauths
     * @param userId
     * @return
     */
    MiniAccountOauths getMiniAccountOauths(String userId);


    /**
     * 分页查询黑名单用户
     *
     * @param page
     * @param size
     * @param permission 权限 0-全部 1-下单 2-评论
     * @param fuzzy      手机号或微信昵称模糊搜索
     * @return com.medusa.gruul.account.model.vo.BlacklistUserVo
     */
    PageUtils<List<BlacklistUserVo>> blacklist(Integer page, Integer size, Integer permission, String fuzzy);



    /**
     * 批量获取指定用户id(用户店铺id)的用户基本信息
     *
     * @param shopUserId 用户id数组
     * @return com.medusa.gruul.account.api.entity.MiniAccount
     */
    List<MiniAccountExtDto> accountsInfoList(List<String> shopUserId);


    /**
     * 获取当前用户二维码
     *
     * @return base64二维码
     */
    String qrCode();



    /**
     * 根据店铺用户id查询用户所在店铺唯一id
     *
     * @param shopUserId 店铺用户id
     * @return com.medusa.gruul.account.api.entity.MiniAccount
     */
    MiniAccount getByShopUserId(String shopUserId);

    /**
     * 获取会员权益
     *
     *
     * @return java.lang.Integer
     */
    List<MemberLevelRightsVo> getPowerAll();

    /**
     * 保存外部系统对接的用户信息
     * @param userDto
     * @return
     */
    MiniAccount outSave(UserListDto userDto);

    /**
     * 根据用户id获取用户积分信息
     * @return
     */
    UserIntegralVo getUserInfoIntegral();

    /**
     * 根据用户id获取用户积分信息
     * @return
     */
    UserIntegralVo getUserInfoIntegralByUserId(String userId);

    /**
     * 获取我的团队信息
     * @param myTeamMiniAccountParam
     * @return
     */
    PageUtils<MyTeamMiniAccountVo>getMyTeamMiniAccountVo(MyTeamMiniAccountParam myTeamMiniAccountParam);

    /**
     * 获取团员数量
     * @param type
     * @return
     */
    Integer getTeamNumCount(Integer type);

    /**
     * app登录
     * @param tenementLoginDto
     * @param resetTokenFlag
     * @return
     */
    LoginBaseInfoVo appLogin(AppLoginDto tenementLoginDto, boolean resetTokenFlag);

    /**
     * app注册
     * @param appRegisterDto
     * @return
     */
    boolean appRegister(AppRegisterDto appRegisterDto);

    /**
     * 上传用户头像单独接口
     *
     * @param updateUserBaseInfoDto dto
     */
    void uploadAvatar(UpdateUserBaseInfoDto updateUserBaseInfoDto);

    /**
     * 获取用户上下级设置
     * @param userId
     * @return
     */
    MiniAccountParentVo getMiniAccountParentVo(String userId);

    /**
     * 获取可添加用户上级列表
     * @return
     */
    PageUtils<MiniAccountChildVo> getMiniAccountParentList(MiniAccountChildParam param);

    /**
     * 获取可添加用户下级列表
     * @param param
     * @return
     */
    PageUtils<MiniAccountChildVo> getMiniAccountChildList(MiniAccountChildParam param);

    /**
     * 保存用户上下级关系
     * @param param
     */
    void saveMiniAccountParentChild(MiniAccountParentChildParam param) throws Exception;

    /**
     * 获取新增客户数
     * @param startDate
     * @param endDate
     * @return
     */
    Integer getAddCustom(String startDate,String endDate);

    /**
     * 更新小程序用户基本信息
     * @param dto
     */
    void updateAccountData(ApiUpdateAccountDto dto);

    /**
     * 分页获取可添加的小程序用户
     * @param param
     * @return
     */
    PageUtils<BindMiniAccountVo> getBindMiniAccount(BindMiniAccountParam param);

    /**
     * pc端修改小程序客户信息
     * @param dto
     */
    void updateMiniAccountDetail(MiniAccountDetailDto dto);

    /**
     * 更新小程序会员绑定的用户关系
     * @param userId
     * @param accountId
     * @return
     */
    Boolean updateMiniAccount(String userId, String accountId);

    /**
     * 更新小程序会员等级
     */
    void updateMiniAccountLevel();

    /**
     * 发送生日祝福消息
     */
    void sendBirthDayWxMessage();

    /**
     * 发送微信订阅活动消息
     * @param dto
     */
    void sendActivityWxMessage(SendActivityWxMessageDto dto);

    /**
     * 同步会员公众号id
     * @param dto
     */
    void updateMpOpenId(UpdateMpOpenIdDto dto);

    /**
     * 分页获取客户明细
     * @param param
     * @return
     */
    IPage<MiniAccountDetailVo>searchMiniAccountDetail(MiniAccountDetailParam param);

    /**
     * 分页获取提成汇总
     * @param param
     * @return
     */
    IPage<MiniAccountRoyaltyVo>searchMiniAccountRoyalty(MiniAccountRoyaltyParam param);

    /**
     * 获取小程序用户权限
     * @return
     */
    Boolean getMiniAccountAgainPermission();

    /**
     * 根据会员等级获取小程序用户权限
     * @param memberId
     * @return
     */
    Boolean getMiniAccountAgainPermissionByMemberId(String memberId);

    /**
     * 分页获取可选择客户
     * @param param
     * @return
     */
    IPage<ChooseAccountVo>getChooseAccount(ChooseAccountParam param);

    /**
     * 根据userId获取可选择客户详情
     * @param userId
     * @return
     */
    ChooseAccountVo getChooseAccountByUserId(String userId);

    /**
     * 获取验证码
     * @param sendCodeDto
     * @return
     */
    String sendCode(SendCodeDto sendCodeDto);

    /**
     * 查询用户是否已经设置支付密码
     * @param dto
     */
    Integer checkAccountPayPwd(CheckAccountPayPwdDto dto);

    /**
     * 修改支付密码
     * @param dto
     */
    void updateAccountPayPwd(UpdateAccountPayPwdDto dto);

    /**
     * 上传微信收款码，支付宝收款码
     * @param dto
     */
    void updateAccountExtends(UpdateAccountExtendsDto dto);

    /**
     * 获取小程序用户认证信息
     * @return
     */
    ApiMiniAccountCardVo getApiMiniAccountCard();

    /**
     * 小程序认证实名
     * @param dto
     */
    void authCard(ApiMiniAccountCardDto dto);

    /**
     * 后台审核小程序用户身份证
     * @param dto
     * @return
     */
    String auditCard(AuditMiniAccountCardDto dto);

    /**
     * 驳回用户身份证认证
     * @param dto
     * @return
     */
    String rejectCard(AuditMiniAccountCardDto dto);

    /**
     * 更新小程序用户扩展信息表冻结状态
     * @return
     */
    Map updateMiniAccountExtendsStatus();

    /**
     * 获取激活商品信息
     * @return
     */
    List<ActiveProductVo> getActiveProduct();

    /**
     * 验证代下单人信息
     * @param dto
     * @return
     */
    MiniAccount vailUserMessage(VailUserMessageDto dto);

    /**
     * 小程序根据手机号码擦好像用户信息
     * @param param
     * @return
     */
    ApiMiniAccountVo getApiMiniAccount(ApiMiniAccountParam param);

    /**
     * 扣减用户余额
     * @param miniAccountBalanceDto
     * @return
     */
    boolean subtractMiniAccountBalance(MiniAccountBalanceDto miniAccountBalanceDto);

    /**
     * 设置业务员标识
     * @param accountIds
     * @param saleFlag
     */
    void updateSaleFlag(List<String> accountIds,Integer saleFlag);

    /**
     * 查询出用户id直推该会员类型下会员等级属于会员体系的会员数量
     * @param memberTypeId
     * @param userId
     * @return
     */
    Integer getDirectMemberCount(Long memberTypeId, String userId);

    /**
     * 查询出用户id间推该会员类型下会员等级属于会员体系的会员数量
     * @param memberTypeId
     * @param userId
     * @return
     */
    Integer getInDirectMemberCount(Long memberTypeId, String userId);

    /**
     * 查询直推会员属于区域类型会员体系会员数
     * @param memberTypeId
     * @param userId
     * @return
     */
    Integer getRegionMemberCount(String memberTypeId, String userId);

    /**
     * 查询用户直推会员等级数
     * @param directMemberLevelIds
     * @param userId
     * @return
     */
    Integer getDirectMemberCountByMemberTypeIds(String directMemberLevelIds, String userId);

    /**
     * 查询出用户id直推运营中心数量
     * @param memberTypeId
     * @param userId
     * @return
     */
    Integer getDirectOperationCenterCount(Long memberTypeId, String userId);

    /**
     * 查询出用户id间推运营中心数量
     * @param memberTypeId
     * @param userId
     * @return
     */
    Integer getInDirectOperationCenterCount(Long memberTypeId, String userId);


    /**
     * 导出客户列表
     */
    void exportUserList(String nikeName, String phone, Long tagId, String orderSuccessStartTime, String orderSuccessEndTime, Integer sortType, Integer userId, Integer superiorAndSubordinate, Long parentId, Long aboveParentId, String shopIds,Integer expireDays);

    /**
     * 导出客户明细
     * @param param 查询参数
     */
    void exportMiniAccountDetail(MiniAccountDetailParam param);


    /**
     * 验证权益包，通惠证是否使用
     * @param orderId
     * @return
     */
    Integer vailDeleteOrderFlag(Long orderId);

    /**
     * 删除订单
     * @param orderVo
     */
    void deleteOrder(OrderVo orderVo);

    /**
     * 更新会员等级
     *
     * @param miniAccount
     */
    void updateMemberLevelRelation(MiniAccount miniAccount);

    /**
     * 获取销售汇总表数据
     */
    IPage<MemberSalesReportVo> getSalesReport(MemberSalesReportParam param);

    /**
     * 获取会员类型汇总数据
     */
    IPage<MemberTypeReportVo> getMemberTypeReport(MemberReportParam param);

    /**
     * 获取会员等级汇总数据
     */
    IPage<MemberLevelReportVo> getMemberLevelReport(MemberReportParam param);
}
