package com.medusa.gruul.platform.model.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Author: plh
 * @Description: 用户角色关系参数
 * @Date: Created in 19:09 2023/8/16
 */
@Data
public class AuthUserRoleParamDto {

    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id")
    private Long userId;

    /**
     * 角色ids
     */
    @ApiModelProperty(value = "角色ids")
    private List<Long> roleIds;

}
