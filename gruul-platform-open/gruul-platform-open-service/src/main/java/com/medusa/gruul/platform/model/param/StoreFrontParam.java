package com.medusa.gruul.platform.model.param;

import com.baomidou.mybatisplus.annotation.TableField;
import com.medusa.gruul.common.core.param.QueryParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 18:52 2024/10/8
 */
@Data
public class StoreFrontParam extends QueryParam {


    /**
     * 查询一级分类标识：0否1是
     */
    @ApiModelProperty(value = "查询一级分类标识")
    private String firstClassFlag;

    /**
     * 父级分类标识
     */
    @ApiModelProperty(value = "父级分类标识")
    private String parentClassCode;

    /**
     * 门店编号
     */
    @ApiModelProperty(value = "门店编号")
    private String storeNumber;

    /**
     * 门店全称
     */
    @ApiModelProperty(value = "门店全称")
    private String storeFullName;

    /**
     * 所属区域
     */
    @ApiModelProperty(value = "所属区域")
    private String storeArea;

    /**
     * 部门名称
     */
    @ApiModelProperty(value = "部门名称")
    private String departmentName;


    /**
     * 职员名称
     */
    @ApiModelProperty(value = "职员名称")
    private String employeeName;

    /**
     * 仓库名称
     */
    @ApiModelProperty(value = "仓库名称")
    private String stockName;

    /**
     * 是否末级
     */
    @ApiModelProperty(value = "是否末级")
    private String isCatalog;

}
