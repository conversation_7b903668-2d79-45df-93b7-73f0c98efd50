package com.medusa.gruul.platform.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.medusa.gruul.platform.api.entity.AuthMenuButton;
import com.medusa.gruul.platform.api.entity.AuthRoleInfo;
import com.medusa.gruul.platform.api.model.dto.AuthMenuButtonDto;
import com.medusa.gruul.platform.api.model.dto.AuthMenuInfoDto;
import com.medusa.gruul.platform.api.model.vo.AuthMenuButtonVo;
import com.medusa.gruul.platform.model.dto.PlatformAuthRoleInfoDto;

import java.util.List;

/**
 * @Author: plh
 * @Description: 菜单按钮服务层
 * @Date: Created in 9:44 2023/9/21
 */
public interface IAuthMenuButtonService extends IService<AuthMenuButton> {

    /**
     * 添加菜单按钮
     *
     * @param authMenuButtonDto
     */
    void saveAuthMenuButton(AuthMenuButtonDto authMenuButtonDto);

    /**
     * 编辑菜单按钮
     * @param authMenuButtonDto
     * @return
     */
    void editAuthMenuButton(AuthMenuButtonDto authMenuButtonDto);

    /**
     * 删除菜单按钮
     * @param id
     */
    void deleteAuthMenuButton(Long id);

    /**
     * 获取菜单按钮列表
     * @param menuId
     * @return
     */
    List<AuthMenuButtonVo> getAuthMenuButtonVo(Long menuId);

    /**
     * 获取用户角色按钮
     * @param id
     * @return
     */
    List<AuthMenuButtonVo> getAuthMenuButtonVoByUserId(Long id);
}
