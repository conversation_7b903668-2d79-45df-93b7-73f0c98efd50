package com.medusa.gruul.account.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 10:04 2025/5/20
 */
@Data
@ApiModel(value = "会员类型提交数据")
public class AddOrUpdateMemberTypeDto {

    @ApiModelProperty(value = "新增/修改会员类型Dto")
    private List<MemberTypeDto> list;

}
