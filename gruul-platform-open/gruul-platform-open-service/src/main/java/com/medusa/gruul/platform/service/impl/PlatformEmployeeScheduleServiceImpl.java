package com.medusa.gruul.platform.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.medusa.gruul.common.core.constant.CommonConstants;
import com.medusa.gruul.common.core.exception.ServiceException;
import com.medusa.gruul.common.core.util.PageUtils;
import com.medusa.gruul.common.core.util.StringUtil;
import com.medusa.gruul.common.data.tenant.ShopContextHolder;
import com.medusa.gruul.common.dto.CurUserDto;
import com.medusa.gruul.common.core.util.CurUserUtil;
import com.medusa.gruul.platform.api.entity.PlatformEmployeeSchedule;
import com.medusa.gruul.platform.api.model.vo.EmployeeScheduleVo;
import com.medusa.gruul.platform.mapper.PlatformEmployeeScheduleMapper;
import com.medusa.gruul.platform.model.dto.EmployeeScheduleDto;
import com.medusa.gruul.platform.model.param.EmployeeScheduleParam;
import com.medusa.gruul.platform.service.IPlatformEmployeeScheduleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 职员排班Service实现类
 * 
 * <AUTHOR>
 * @date 2025-01-25
 */
@Slf4j
@Service
public class PlatformEmployeeScheduleServiceImpl extends ServiceImpl<PlatformEmployeeScheduleMapper, PlatformEmployeeSchedule> 
        implements IPlatformEmployeeScheduleService {

    @Override
    public PageUtils<EmployeeScheduleVo> searchEmployeeSchedule(EmployeeScheduleParam param) {
        IPage<EmployeeScheduleVo> page = this.baseMapper.searchEmployeeSchedule(
                new Page<>(param.getCurrent(), param.getSize()), param);
        return new PageUtils<>(page);
    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addSchedule(EmployeeScheduleDto dto) {
        // 检查是否已存在相同的排班记录
        LambdaQueryWrapper<PlatformEmployeeSchedule> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PlatformEmployeeSchedule::getEmployeeId, dto.getEmployeeId())
               .eq(PlatformEmployeeSchedule::getScheduleDate, dto.getScheduleDate());
        //wrapper.eq(PlatformEmployeeSchedule::getShiftType, dto.getShiftType()); 暂时不用
        
        if (this.count(wrapper) > 0) {
            throw new ServiceException("该职员在此日期和班次已有排班记录");
        }

        PlatformEmployeeSchedule schedule = new PlatformEmployeeSchedule();
        BeanUtils.copyProperties(dto, schedule);
        
        // 设置创建人信息
        CurUserDto curUser = CurUserUtil.getHttpCurUser();
        if (curUser != null) {
            schedule.setCreateUserId(curUser.getUserId());
            schedule.setCreateUserName(curUser.getNikeName());
        }
        
        return this.save(schedule);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateSchedule(EmployeeScheduleDto dto) {
        if (dto.getId() == null) {
            throw new ServiceException("排班记录ID不能为空");
        }

        PlatformEmployeeSchedule existSchedule = this.getById(dto.getId());
        if (existSchedule == null) {
            throw new ServiceException("排班记录不存在");
        }

        // 检查是否已存在相同的排班记录（排除当前记录）
        LambdaQueryWrapper<PlatformEmployeeSchedule> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PlatformEmployeeSchedule::getEmployeeId, dto.getEmployeeId())
               .eq(PlatformEmployeeSchedule::getScheduleDate, dto.getScheduleDate())
               .ne(PlatformEmployeeSchedule::getId, dto.getId());
        //wrapper.eq(PlatformEmployeeSchedule::getShiftType, dto.getShiftType()); 暂时不用

        if (this.count(wrapper) > 0) {
            throw new ServiceException("该职员在此日期和班次已有排班记录");
        }

        PlatformEmployeeSchedule schedule = new PlatformEmployeeSchedule();
        BeanUtils.copyProperties(dto, schedule);
        
        // 设置修改人信息
        CurUserDto curUser = CurUserUtil.getHttpCurUser();
        if (curUser != null) {
            schedule.setLastModifyUserId(curUser.getUserId());
            schedule.setLastModifyUserName(curUser.getNikeName());
        }
        
        return this.updateById(schedule);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean setSchedule(EmployeeScheduleDto dto) {
        List<Long> employeeIds = dto.getEmployeeIds();
        if (CollectionUtil.isEmpty(employeeIds)) {
            throw new ServiceException("缺少职员ids");
        }
        if (dto.getScheduleDate()==null){
            dto.setScheduleDate(LocalDate.now());
        }

        // 根据 时间 查询
        LambdaQueryWrapper<PlatformEmployeeSchedule> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(PlatformEmployeeSchedule::getId, PlatformEmployeeSchedule::getEmployeeId,
                PlatformEmployeeSchedule::getShiftType,
                PlatformEmployeeSchedule::getWorkStatus);
        wrapper.eq(PlatformEmployeeSchedule::getScheduleDate, dto.getScheduleDate());
        //wrapper.eq(PlatformEmployeeSchedule::getShiftType, dto.getShiftType()); 暂时不用
        List<PlatformEmployeeSchedule> sourceList = this.list(wrapper);

        // 如果shopId不为空 小程序
        if (StringUtil.isNotBlank(dto.getShopId())){
            ShopContextHolder.setShopId(dto.getShopId());
            //多出的Id 将未选择的职员设置为休息
            Set<PlatformEmployeeSchedule> extraIds = sourceList.stream()
                    .filter(data -> !dto.getEmployeeIds().contains(data.getEmployeeId()))
                    .collect(Collectors.toSet());
            //多出的其他Id设置为休息
            for (PlatformEmployeeSchedule data : extraIds) {
                if (data.getWorkStatus() != 2){
                    PlatformEmployeeSchedule employeeSchedule = new PlatformEmployeeSchedule();
                    employeeSchedule.setWorkStatus(2);
                    employeeSchedule.setId(data.getId());
                    this.updateById(employeeSchedule);
                }
            }
        }


        ArrayList<PlatformEmployeeSchedule> list = new ArrayList<>();
        for (Long employeeId : employeeIds) {
            PlatformEmployeeSchedule schedule = new PlatformEmployeeSchedule();
            List<PlatformEmployeeSchedule> dataList = sourceList.stream().filter(item -> item.getEmployeeId().equals(employeeId)).collect(Collectors.toList());

            if (CollectionUtil.isNotEmpty( dataList) && dataList.size()>1){
                throw new ServiceException("多条记录");
            }
            if (CollectionUtil.isNotEmpty( dataList)){
                schedule = dataList.get(0);
                schedule.setWorkStatus(dto.getWorkStatus());
            }else {
                BeanUtils.copyProperties(dto, schedule);
                schedule.setEmployeeId(employeeId);
            }
            list.add( schedule);
        }
        return this.saveOrUpdateBatch(list);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteSchedule(Long id) {
        PlatformEmployeeSchedule schedule = this.getById(id);
        if (schedule == null) {
            throw new ServiceException("排班记录不存在");
        }
        
        // 设置修改人信息
        CurUserDto curUser = CurUserUtil.getHttpCurUser();
        if (curUser != null) {
            schedule.setLastModifyUserId(curUser.getUserId());
            schedule.setLastModifyUserName(curUser.getNikeName());
        }
        schedule.setUpdateTime(LocalDateTime.now());
        
        return this.updateById(schedule);
    }


}
