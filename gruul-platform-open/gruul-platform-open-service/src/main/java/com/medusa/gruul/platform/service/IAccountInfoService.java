package com.medusa.gruul.platform.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.medusa.gruul.common.core.util.PageUtils;
import com.medusa.gruul.common.core.util.Result;
import com.medusa.gruul.platform.api.entity.AccountInfo;
import com.medusa.gruul.platform.api.model.dto.OssConfigDto;
import com.medusa.gruul.platform.api.model.vo.RelationInfoVo;
import com.medusa.gruul.platform.api.model.vo.StoreFrontVo;
import com.medusa.gruul.platform.api.model.vo.UserInfoVo;
import com.medusa.gruul.platform.model.dto.*;
import com.medusa.gruul.platform.model.vo.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <p>
 * 平台用户表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-09-07
 */
public interface IAccountInfoService extends IService<AccountInfo> {


    /**
     * 校验是否存在该平台账号
     *
     * @param phone 手机号
     * @param type  type=1 校验账号存在  type=2校验账号不存在 默认使用1
     */
    void checkoutAccount(String phone, Integer type);

    /**
     * 用户校验预授权
     *
     * @param preAccountVerifyDto com.medusa.gruul.platform.model.dto.PreAccountVerifyDto
     * @return java.lang.String
     */
    String preAccountScanCode(PreAccountVerifyDto preAccountVerifyDto);

    /**
     * 用户校验统一回调入口
     *
     * @param code     code
     * @param state    state
     * @param response response
     */
    void accountScanCodeNotify(String code, String state, HttpServletResponse response);

    /**
     * 根据请求token获取当前用户信息
     *
     * @return com.medusa.gruul.platform.model.vo.AccountInfoVo
     */
    LoginAccountInfoDetailVo info();


    /**
     * 账号换绑
     *
     * @param appId  网站应用appId
     * @param code   扫码的code值
     * @param userId 用户id
     * @return 返回结果
     */
    Result changeTie(String appId, String code, Long userId);

    /**
     * 账号手机换绑
     *
     * @param phoneChangeTieDto com.medusa.gruul.platform.model.dto.PhoneChangeTieDto
     */
    void phoneChangeTie(PhoneChangeTieDto phoneChangeTieDto);

    /**
     * 账号修改密码
     *
     * @param passChangeTieDto com.medusa.gruul.platform.model.dto.PassChangeTieDto
     */
    void passChangeTie(PassChangeTieDto passChangeTieDto);




    /**
     * 商家登录入口
     *
     * @param tenementLoginDto com.medusa.gruul.platform.model.dto.LoginDto
     * @return com.medusa.gruul.platform.model.vo.AccountInfoVo
     */
    AccountInfoVo login(LoginDto tenementLoginDto, boolean resetTokenFlag);


    /**
     * 获取state值
     *
     * @param code code
     * @return com.medusa.gruul.common.core.util.Result
     */
    Result verifyStateResult(String code);

    /**
     * 忘记密码
     *
     * @param passwordRetrieveDto com.medusa.gruul.platform.model.dto.PasswordRetrieveDto
     */
    void passwordRetrieve(PasswordRetrieveDto passwordRetrieveDto);

    /**
     * 获取用户登录信息
     *
     * @param accountInfo com.medusa.gruul.platform.api.entity.AccountInfo
     * @return com.medusa.gruul.platform.model.vo.AccountInfoVo
     */
    AccountInfoVo getLoginInfoVo(AccountInfo accountInfo, boolean resetTokenFlag);


    /**
     * 是否商家主账号
     *
     * @param token 用户id
     * @return
     */
    Boolean affirmLessee(String token);


    /**
     * 修改用户电子发票邮箱接口
     *
     * @param emailChangeDto com.medusa.gruul.platform.model.dto.EmailChangeDto
     */
    void emailChange(EmailChangeDto emailChangeDto);




    /**
     * 校验当前用户相关信息是否正确
     *
     * @param verifyDataDto com.medusa.gruul.platform.model.dto.VerifyDataDto
     * @return 正确返回true  不正确返回false
     */
    Boolean verifyData(VerifyDataDto verifyDataDto);

    /**
     * 添加平台用户
     * @param accountInfoDto
     * @return
     */
    AccountInfo addAccountInfo(PlatformAccountInfoDto accountInfoDto);

    /**
     * 编辑平台用户
     * @param accountInfoDto
     * @return
     */
    AccountInfo editAccountInfo(PlatformAccountInfoDto accountInfoDto);

    /**
     * 查询平台用户列表
     * @param paramDto
     * @return
     */
    PageUtils searchAccountInfo(PlatformAccountInfoParamDto paramDto);

    /**
     * 分页查询平台用户列表-新
     * @param paramDto
     * @return
     */
    PageUtils<UserInfoVo>searchUserInfoVo(PlatformAccountInfoParamDto paramDto);

    /**
     * 重置平台用户的密码
     * @param accountInfoDto
     * @return
     */
    AccountInfo resetUserPwd(PlatformAccountInfoDto accountInfoDto);

    /**
     * 删除平台用户
     * @param accountInfoDto
     */
    void deleteAccountInfo(PlatformAccountInfoDto accountInfoDto);


    /**
     * 获取含有设备id的用户
     * @return
     */
    List<AccountInfo> getAccountRegistrationId(String mobileType);

    /**
     * 根据用户id获取门店信息
     * @param id
     * @return
     */
    StoreFrontVo getStoreFrontByAccountId(String id);

    /**
     * 用户信息-绑定小程序客户
     * @param dto
     */
    void bindMiniAccount(BindMiniAccountDto dto);

    /**
     * 用户信息-绑定门店
     * @param dto
     */
    void accountBindStoreFront(AccountBindStoreFrontDto dto);

    /**
     * 用户信息-绑定职员，部门
     * @param dto
     */
    void accountBindEmployee(AccountBindEmployeeDto dto);

    /**
     * 用户信息-绑定仓库"
     * @param dto
     */
    void accountBindWarehouse(AccountBindWarehouseDto dto);

    /**
     * 用户信息-设置默认部门
     * @param accountId
     */
    void settingDefaultDepartment(String accountId);

    /**
     * 用户信息-设置默认门店
     * @param accountId
     */
    void settingDefaultStoreFront(String accountId);

    /**
     * 通过小程序会员id获取关联信息
     * @param userId
     * @return
     */
    RelationInfoVo getRelationInfoByMiniAccountId(String userId);

    /**
     * 获取默认部门关联信息
     * @return
     */
    RelationInfoVo getRelationInfoDefaultDepartment();

    /**
     * 通过门店id获取关联信息
     * @param storeFrontId
     * @return
     */
    RelationInfoVo getRelationInfoByStoreFrontId(String storeFrontId);

    /**
     * 通过用户id获取关联信息
     * @param accountId
     * @return
     */
    RelationInfoVo getRelationInfoByAccountId(String accountId);
}
