package com.medusa.gruul.platform.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Author: plh
 * @Description: 角色菜单关系参数
 * @Date: Created in 21:00 2023/8/16
 */
@Data
public class AuthRoleMenuParamDto {

    /**
     * 用户id
     */
    @ApiModelProperty(value = "角色id")
    @NotNull
    private Long roleId;

    /**
     * 菜单ids
     */
    @ApiModelProperty(value = "菜单ids")
    @NotNull
    private String menuIds;


}
