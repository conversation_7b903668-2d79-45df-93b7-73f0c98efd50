package com.medusa.gruul.platform.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.medusa.gruul.platform.api.entity.PlatformEmployeeSchedule;
import com.medusa.gruul.platform.api.model.vo.EmployeeScheduleVo;
import com.medusa.gruul.platform.model.param.EmployeeScheduleParam;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

/**
 * 职员排班Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-25
 */
@Repository
public interface PlatformEmployeeScheduleMapper extends BaseMapper<PlatformEmployeeSchedule> {

    /**
     * 分页查询职员排班信息
     * 
     * @param page 分页参数
     * @param params 查询参数
     * @return 排班信息列表
     */
    IPage<EmployeeScheduleVo> searchEmployeeSchedule(Page<EmployeeScheduleVo> page, @Param("params") EmployeeScheduleParam params);



}
