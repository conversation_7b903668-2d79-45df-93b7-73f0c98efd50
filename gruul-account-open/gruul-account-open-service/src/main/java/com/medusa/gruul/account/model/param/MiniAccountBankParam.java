package com.medusa.gruul.account.model.param;

import com.medusa.gruul.common.core.param.QueryParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 10:36 2025/5/27
 */
@Data
@ApiModel(value = "MiniAccountBankParam 实体", description = "会员银行卡查询 param")
public class MiniAccountBankParam extends QueryParam {
    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id")
    private String userId;
}
