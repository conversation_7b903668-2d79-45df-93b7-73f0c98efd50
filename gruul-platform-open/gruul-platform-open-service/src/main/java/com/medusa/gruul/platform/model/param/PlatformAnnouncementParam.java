package com.medusa.gruul.platform.model.param;

import com.medusa.gruul.common.core.param.QueryParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @description
 * @date 2025/7/30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "PlatformAnnouncementParam对象", description = "平台公告参数")
public class PlatformAnnouncementParam extends QueryParam {

    private static final long serialVersionUID = 1L;

    private Long id;

    @ApiModelProperty(value = "公告标题")
    private String title;

    @ApiModelProperty(value = "生效开始日期")
    private LocalDate effectiveStartDate;

    @ApiModelProperty(value = "生效结束时间")
    private LocalDate effectiveEndDate;

    @ApiModelProperty(value = "查询当天有效用")
    private LocalDate today;

    //目前不用
    @ApiModelProperty(value = "状态:0->草稿；100->待审核；101->审核通过；200->审核不通过；300->失效；400->停止")
    private Integer status;


}