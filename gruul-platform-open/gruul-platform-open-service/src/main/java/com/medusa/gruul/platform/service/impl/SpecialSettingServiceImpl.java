package com.medusa.gruul.platform.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.medusa.gruul.common.core.constant.CommonConstants;
import com.medusa.gruul.common.data.tenant.ShopContextHolder;
import com.medusa.gruul.platform.api.entity.SpecialSetting;
import com.medusa.gruul.platform.api.model.dto.SpecialSettingDto;
import com.medusa.gruul.platform.api.model.vo.SpecialSettingVo;
import com.medusa.gruul.platform.mapper.SpecialSettingMapper;
import com.medusa.gruul.platform.service.ISpecialSettingService;
import com.medusa.gruul.shops.api.entity.ShopsPartner;
import com.medusa.gruul.shops.api.feign.RemoteShopsService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Author: plh
 * @Description: 特殊配置 服务实现类
 * @Date: Created in 11:05 2024/8/15
 */
@Slf4j
@Service
public class SpecialSettingServiceImpl extends ServiceImpl<SpecialSettingMapper, SpecialSetting> implements ISpecialSettingService {

    @Autowired
    private RemoteShopsService remoteShopsService;

    /**
     * 查询特殊配置
     * @return
     */
    @Override
    public SpecialSettingVo getSpecialSetting() {
        SpecialSetting specialSetting = this.baseMapper.selectOne(null);
        SpecialSettingVo specialSettingVo = new SpecialSettingVo();
        if(specialSetting!=null){
            BeanUtils.copyProperties(specialSetting,specialSettingVo);
        }
        return specialSettingVo;
    }

    /**
     * 保存或更新特殊配置
     * @param specialSettingDto
     */
    @Override
    public void saveSpecialSetting(SpecialSettingDto specialSettingDto) {
        SpecialSetting specialSetting = this.baseMapper.selectOne(null);
        if(specialSetting==null){
            specialSetting = new SpecialSetting();
        }
        BeanUtils.copyProperties(specialSettingDto,specialSetting);
        if(specialSetting.getId()!=null&&!specialSetting.getId().equals("")){//更新
            this.baseMapper.updateById(specialSetting);
        }else{//保存
            this.baseMapper.insert(specialSetting);
        }
    }

    @Override
    public List<String> getSpecialSettingShopIds(String shopId) {
        ShopContextHolder.setShopId(CommonConstants.DEFAULT_SHOP_ID);
        List<String> shopIds = this.baseMapper.getSpecialSettingShopIds(shopId);
        ShopContextHolder.setShopId(shopId);
        return shopIds;
    }

    @Override
    public List<SpecialSetting> getSpecialSettingByShopId(String shopId) {
        //String oldShopId = ShopContextHolder.getShopId();
        //ShopContextHolder.setShopId(CommonConstants.DEFAULT_SHOP_ID);
        LambdaQueryWrapper<SpecialSetting>wrapper = new LambdaQueryWrapper<>();
        //wrapper.eq(SpecialSetting::getShopId,shopId);
        List<SpecialSetting> list = this.baseMapper.selectList(wrapper);
        //ShopContextHolder.setShopId(oldShopId);
        return list;
    }

    @Override
    public SpecialSettingVo getMainSpecialSetting() {
        ShopsPartner shopsPartner = remoteShopsService.getShopsPartnerMain();
        ShopContextHolder.setShopId(shopsPartner.getShopId());
        SpecialSetting specialSetting = this.baseMapper.selectOne(null);
        SpecialSettingVo specialSettingVo = new SpecialSettingVo();
        if(specialSetting!=null){
            BeanUtils.copyProperties(specialSetting,specialSettingVo);
        }
        return specialSettingVo;
    }


}
