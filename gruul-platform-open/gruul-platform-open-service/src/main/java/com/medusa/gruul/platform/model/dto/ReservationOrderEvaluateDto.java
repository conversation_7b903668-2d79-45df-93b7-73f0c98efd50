package com.medusa.gruul.platform.model.dto;


import com.medusa.gruul.platform.api.enums.ReservationEnums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 预约单评价DTO
 *
 * <AUTHOR>
 * @Date: 2025-06-11
 */
@Data
@ApiModel(value = "ReservationOrderEvaluateDto", description = "预约单评价DTO")
public class ReservationOrderEvaluateDto implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "shouId")
    private String shouId;

/*    @ApiModelProperty(value = "employeeId")
    private String employeeId;*/

    /**
     * 预约单ID
     */
    @NotNull(message = "预约单ID不能为空")
    @ApiModelProperty(value = "预约单ID", required = true)
    private Long reservationOrderId;

    /**
     * 满意度(1-不满意，3-良好，5-满意) todo 临时取消null判断
     * {@link ReservationEnums.EvaluateScore}
     */
    @ApiModelProperty(value = "满意度(1-不满意，3-良好，5-满意)", required = true)
    private Integer successScore;


    /**
     * 评分
     */
    @NotNull(message = "评分不能为空")
    @ApiModelProperty(value = "评分", required = true)
    private Integer rate;

    /**
     * 评价
     */
    @ApiModelProperty(value = "评价")
    private String comment;
} 