package com.medusa.gruul.account.model.param;

import com.medusa.gruul.common.core.param.QueryParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 商家查询实体
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "MiniShopsPartnerParam 实体", description = "商家 param")
@Data
public class MiniShopsPartnerParam extends QueryParam {

    @ApiModelProperty(value = "通行票id")
    private Long ticketId;

    @ApiModelProperty(value = "用户-通行票id")
    private Long miniAccountPassTicketId;

    @ApiModelProperty(value = "用户选址纬度")
    private Double lat;

    @ApiModelProperty(value = "用户选址经度")
    private Double lng;

}
