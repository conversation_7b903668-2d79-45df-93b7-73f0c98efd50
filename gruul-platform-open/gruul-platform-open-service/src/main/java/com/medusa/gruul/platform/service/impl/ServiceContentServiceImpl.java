package com.medusa.gruul.platform.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.medusa.gruul.common.core.exception.ServiceException;
import com.medusa.gruul.common.core.util.CurUserUtil;
import com.medusa.gruul.common.core.util.SpringContextHolder;
import com.medusa.gruul.common.core.util.SystemCode;
import com.medusa.gruul.common.dto.CurUserDto;
import com.medusa.gruul.platform.api.entity.ServiceContent;

import com.medusa.gruul.platform.api.entity.ServiceContentTemplate;
import com.medusa.gruul.platform.api.enums.PlatformPositionEnums;
import com.medusa.gruul.platform.api.model.dto.MessageTemplateCopyDto;
import com.medusa.gruul.platform.model.dto.ServiceContentDto;
import com.medusa.gruul.platform.model.param.ServiceContentParam;

import com.medusa.gruul.platform.model.vo.ServiceContentVo;
import com.medusa.gruul.platform.mapper.ServiceContentMapper;
import com.medusa.gruul.platform.service.IServiceContentService;
import com.medusa.gruul.platform.service.IServiceContentTemplateService;
import com.medusa.gruul.shops.api.enums.PartnerModelEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 服务项目Service实现类
 */
@Service
@Slf4j
public class ServiceContentServiceImpl extends ServiceImpl<ServiceContentMapper, ServiceContent> implements IServiceContentService {
    @Autowired
    private IServiceContentTemplateService iServiceContentTemplateService;
    @Override
    public IPage<ServiceContentVo> page(ServiceContentParam queryDto) {
        Page<ServiceContent> page = new Page<>(queryDto.getCurrent(), queryDto.getSize());
        return baseMapper.selectServiceContentList(page, queryDto);
    }

    @Override
    public boolean add(ServiceContentDto dto) {
        // 检查服务项目名称是否重复
        if (checkServiceNameExists(dto.getServiceName(), null)) {
            throw new ServiceException("服务项目名称已存在", SystemCode.PARAM_VALID_ERROR.getCode());
        }
        
        // 检查服务项目名称是否为空
        if (dto.getServiceName() == null || dto.getServiceName().trim().isEmpty()) {
            throw new ServiceException("服务项目名称不能为空", SystemCode.PARAM_MISS.getCode());
        }
        
        // 设置默认状态为启用
        if (dto.getStatus() == null) {
            dto.setStatus(1);
        }
        
        ServiceContent entity = new ServiceContent();
        BeanUtils.copyProperties(dto, entity);

        // 设置创建人信息
        CurUserDto user = CurUserUtil.getHttpCurUser();
        entity.setCreateUserId(Long.valueOf(user.getUserId()));
        entity.setCreateUserName(user.getNikeName());
        
        return save(entity);
    }

    @Override
    public boolean update(ServiceContentDto dto) {
        // 检查ID是否存在
        if (dto.getId() == null) {
            throw new ServiceException("ID不能为空", SystemCode.PARAM_MISS.getCode());
        }
        
        // 检查服务项目是否存在
        ServiceContent existEntity = getById(dto.getId());
        if (existEntity == null) {
            throw new ServiceException("服务项目不存在", SystemCode.DATA_NOT_EXIST.getCode());
        }
        
        // 检查服务项目名称是否重复
        if (checkServiceNameExists(dto.getServiceName(), dto.getId())) {
            throw new ServiceException("服务项目名称已存在", SystemCode.DATA_EXISTED.getCode());
        }
        
        // 检查服务项目名称是否为空
        if (dto.getServiceName() == null || dto.getServiceName().trim().isEmpty()) {
            throw new ServiceException("服务项目名称不能为空", SystemCode.PARAM_MISS.getCode());
        }
        
        ServiceContent entity = new ServiceContent();
        BeanUtils.copyProperties(dto, entity);

        // 设置修改人信息
        CurUserDto user = CurUserUtil.getHttpCurUser();
        entity.setLastModifyUserId(Long.valueOf(user.getUserId()));
        entity.setLastModifyUserName(user.getNikeName());
        
        return updateById(entity);
    }

    @Override
    public boolean delete(Long id) {
        // 检查ID是否存在
        if (id == null) {
            throw new ServiceException("ID不能为空", SystemCode.PARAM_MISS.getCode());
        }
        return removeById(id);
    }

    @Override
    public boolean checkServiceNameExists(String serviceName, Long id) {
        LambdaQueryWrapper<ServiceContent> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ServiceContent::getServiceName, serviceName);
        if (id != null) {
            queryWrapper.ne(ServiceContent::getId, id);
        }
        return count(queryWrapper) > 0;
    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void copyFromTemplate(MessageTemplateCopyDto msg) {
        if (!msg.getPartnerModel().equals(PartnerModelEnum.OPERATION_CENTER.getType())||
                StringUtils.isBlank(msg.getTenantId())||
                StringUtils.isBlank(msg.getShopId())){
            throw new ServiceException("参数错误");
        }
        // 查询模板
        List<ServiceContentTemplate> templateList = iServiceContentTemplateService.list(
                new LambdaQueryWrapper<ServiceContentTemplate>()
                        .eq(ServiceContentTemplate::getPartnerModel, msg.getPartnerModel())
                        .eq(ServiceContentTemplate::getStatus, PlatformPositionEnums.StatusEnum.ENABLED.getCode())
        );

        if (!templateList.isEmpty()) {
            // 获取所有模板中的服务名称
            List<String> allServiceNames = templateList.stream()
                    .map(ServiceContentTemplate::getServiceName)
                    .collect(Collectors.toList());

            // 查询已存在的服务名称，避免重复插入
            List<String> existingServiceNames = new ArrayList<>();

            //  一次性获取所有已存在的服务名称
            if (!allServiceNames.isEmpty()) {
                LambdaQueryWrapper<ServiceContent> serviceContentWrapper = new LambdaQueryWrapper<>();
                serviceContentWrapper.eq(ServiceContent::getShopId, msg.getShopId())
                        .in(ServiceContent::getServiceName, allServiceNames);

                List<ServiceContent> existingServices = super.list(serviceContentWrapper);
                existingServiceNames = existingServices.stream()
                        .map(ServiceContent::getServiceName)
                        .collect(Collectors.toList());
            }

            // 创建需要保存的服务列表
            List<ServiceContent> services = new ArrayList<>(templateList.size() - existingServiceNames.size());
            for (ServiceContentTemplate template : templateList) {
                if (!existingServiceNames.contains(template.getServiceName())) {
                    ServiceContent position = new ServiceContent();
                    BeanUtils.copyProperties(template, position, "id", "partnerModel");
                    position.setShopId(msg.getShopId());
                    position.setTenantId(msg.getTenantId());
                    services.add(position);
                }
            }

            // 批量保存服务
            if (!services.isEmpty()) {
                boolean saveBatch = SpringContextHolder.getBean(IServiceContentService.class).saveBatch(services);
                if (!saveBatch) {
                    throw new ServiceException("服务项目复制失败: 无法保存服务项目到数据库");
                }
            }
        }
    }
} 