package com.medusa.gruul.platform.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.medusa.gruul.platform.api.entity.PlatformAnnouncement;
import com.medusa.gruul.platform.model.param.PlatformAnnouncementParam;
import com.medusa.gruul.platform.model.vo.PlatformAnnouncementVo;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @description 平台公告Mapper
 * @date 2025/7/30
 */
public interface PlatformAnnouncementMapper extends BaseMapper<PlatformAnnouncement> {

    /**
     * 分页查询公告列表
     *
     * @param page 分页参数
     * @param param 查询参数
     * @return 公告分页数据
     */
    IPage<PlatformAnnouncementVo> getAnnouncementPage(Page<PlatformAnnouncement> page, @Param("param") PlatformAnnouncementParam param);



}