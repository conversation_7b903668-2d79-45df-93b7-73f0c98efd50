package com.medusa.gruul.platform.model.dto;

import com.baomidou.mybatisplus.annotation.*;
import com.medusa.gruul.common.data.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;


/**
 * @Description: 平台装修模板DTO
 * @Author: jeecg-boot
 * @Date:   2023-09-06
 * @Version: V1.0
 */
@Data
@ApiModel(value="PlatformRenovationTemplateDto对象", description="平台装修模板表")
public class PlatformRenovationTemplateDto {
    
	/**id*/
    @ApiModelProperty(value = "id")
	private Integer id;

	/**模版名称*/
    @ApiModelProperty(value = "模版名称")
    @NotBlank(message = "模板名称不能为空")
	private String name;
	/**模版编号*/
    @ApiModelProperty(value = "模版编号")
    @NotBlank(message = "模版编号不能为空")
	private String code;
	/**分类类型：1 系统模版 2 定制模版*/
    @ApiModelProperty(value = "分类类型：1 系统模版 2 定制模版")
	private Integer type;
	/**模版应用类型：1商城，2社区拼团，3门店版*/
    @ApiModelProperty(value = "模版应用类型：0自定义，1社区拼团，2商城，3门店版")
	private Integer shopTemplateType;
    /**
     * 模板全局颜色 0红 1绿 2蓝
     */
    @NotNull
    @ApiModelProperty(value = "模板全局颜色 0红 1绿 2蓝")
    private String colour;

    /**
     * 模板是否使用中 0 否, 1 是
     */
    @NotNull
    @ApiModelProperty(value = "模板是否使用中 0 否, 1 是")
    private String onlineStatus;
	/**备注*/
    @ApiModelProperty(value = "备注")
	private String remark;

    /**模板预览url*/
    @ApiModelProperty(value = "模板预览url")
    private String url;
}
