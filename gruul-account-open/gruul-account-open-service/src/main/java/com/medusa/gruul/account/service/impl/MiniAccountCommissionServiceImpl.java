package com.medusa.gruul.account.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.medusa.gruul.account.api.entity.*;
import com.medusa.gruul.account.api.enums.CommissionTypeEnum;
import com.medusa.gruul.account.conf.AccountRedis;
import com.medusa.gruul.account.constant.RedisConstant;
import com.medusa.gruul.account.mapper.MiniAccountCommissionMapper;
import com.medusa.gruul.account.mapper.MiniAccountMapper;
import com.medusa.gruul.account.model.dto.ApiMiniAccountCommissionDto;
import com.medusa.gruul.account.model.dto.MiniAccountCommissionDto;
import com.medusa.gruul.account.model.dto.MiniAccountManualCommissionCashDto;
import com.medusa.gruul.account.model.param.*;
import com.medusa.gruul.account.model.vo.*;
import com.medusa.gruul.account.service.*;
import com.medusa.gruul.common.core.constant.CommonConstants;
import com.medusa.gruul.common.core.constant.enums.PriceTypeEnum;
import com.medusa.gruul.common.core.constant.enums.RewardTypeEnum;
import com.medusa.gruul.common.core.exception.ServiceException;
import com.medusa.gruul.common.core.util.*;
import com.medusa.gruul.common.data.tenant.ShopContextHolder;
import com.medusa.gruul.common.data.tenant.TenantContextHolder;
import com.medusa.gruul.common.dto.CurUserDto;
import com.medusa.gruul.goods.api.feign.RemoteGoodsService;
import com.medusa.gruul.goods.api.model.vo.manager.RewardSchemeDataVo;
import com.medusa.gruul.order.api.entity.Order;
import com.medusa.gruul.order.api.enums.OrderStatusEnum;
import com.medusa.gruul.order.api.enums.OrderTypeEnum;
import com.medusa.gruul.order.api.feign.RemoteOrderService;
import com.medusa.gruul.order.api.model.OrderItemVo;
import com.medusa.gruul.order.api.model.OrderVo;
import com.medusa.gruul.platform.api.entity.SpecialSetting;
import com.medusa.gruul.platform.api.feign.RemoteMiniInfoService;
import com.medusa.gruul.shops.api.entity.ShopCommissionRule;
import com.medusa.gruul.shops.api.enums.CommissionRuleTypeEnum;
import com.medusa.gruul.shops.api.feign.RemoteShopsService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;


/**
 * 会员-佣金服务类
 * <AUTHOR>
 */
@Service(value = "miniAccountCommissionServiceImpl")
@Slf4j
public class MiniAccountCommissionServiceImpl extends ServiceImpl<MiniAccountCommissionMapper, MiniAccountCommission> implements IMiniAccountCommissionService {

    @Autowired
    private RemoteShopsService remoteShopsService;
    @Autowired
    private RemoteMiniInfoService remoteMiniInfoService;
    @Autowired
    private RemoteOrderService remoteOrderService;
    @Autowired
    private RemoteGoodsService remoteGoodsService;
    @Autowired
    private IMiniAccountService miniAccountService;
    @Autowired
    private MiniAccountMapper miniAccountMapper;

    @Autowired
    private IMiniAccountExtendsService miniAccountExtendsService;
    @Autowired
    private IMemberLevelService memberLevelService;
    @Autowired
    private IMemberTypeService memberTypeService;
    @Autowired
    private IMiniAccountCommissionCashService miniAccountCommissionCashService;



    /**
     * 获取个人佣金聚合信息接口
     * @return
     */
    @Override
    public MyCommissionMoreVo getCommissionMore() {
        // 获取个人佣金信息
        // 非shop_user_id，是miniAccount的user_id
        String userId = CurUserUtil.getMiniReqeustAccountInfo().getUserId();
        LambdaQueryWrapper<MiniAccount> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MiniAccount::getUserId, userId);
        MiniAccount account = this.miniAccountService.getOne(wrapper);
        MyCommissionMoreVo vo = new MyCommissionMoreVo();
        BeanUtil.copyProperties(account, vo);
        if(StrUtil.isNotBlank(account.getAboveParentId())){
            wrapper.eq(MiniAccount::getUserId, account.getAboveParentId());
            MiniAccount aboveAccount = this.miniAccountService.getOne(wrapper);
            if(aboveAccount!=null){
                vo.setRecommendName(aboveAccount.getNikeName());
            }
        }
        // 获取佣金规则
        ShopCommissionRule rule = remoteShopsService.getCommissionRule();
        BeanUtil.copyProperties(rule, vo);
        if(null != rule){
            if((null != rule.getMinCashAmount() && rule.getMinCashAmount().compareTo(new BigDecimal("0")) > 0) ||
                    (null != rule.getCashRate() && rule.getCashRate().compareTo(new BigDecimal("0")) > 0)
            ){
                StringBuffer cashTipsBuf = new StringBuffer();
                if(null != rule.getMinCashAmount() && rule.getMinCashAmount().compareTo(new BigDecimal("0")) > 0){
                    cashTipsBuf.append("最低提现金额" + rule.getMinCashAmount().stripTrailingZeros().toPlainString() + "元，");
                }
                if(null != rule.getCashRate() && rule.getCashRate().compareTo(new BigDecimal("0")) > 0){
                    cashTipsBuf.append("收取" + rule.getCashRate().stripTrailingZeros().toPlainString()+ "%手续费");
                }
                vo.setCashTips(cashTipsBuf.toString());
            }
            if((null != rule.getMinCashAmount() && rule.getMinCashAmount().compareTo(new BigDecimal("0")) > 0) ||
                    (null != rule.getMaxCashAmount() && rule.getMaxCashAmount().compareTo(new BigDecimal("0")) > 0)||
                    (null != rule.getCashTimes() && rule.getCashTimes().compareTo(new Integer("0")) > 0)
            ){
                StringBuffer cashRuleBuf = new StringBuffer();
                if(null != rule.getMinCashAmount() && rule.getMinCashAmount().compareTo(new BigDecimal("0")) > 0){
                    cashRuleBuf.append("单笔提现最低" + rule.getMinCashAmount().stripTrailingZeros().toPlainString() + "元，");
                }
                if(null != rule.getMaxCashAmount() && rule.getMaxCashAmount().compareTo(new BigDecimal("0")) > 0){
                    cashRuleBuf.append("最高" + rule.getMaxCashAmount().stripTrailingZeros().toPlainString() + "元；");
                }

                if(null != rule.getCashTimes() && rule.getCashTimes().compareTo(new Integer("0")) > 0){
                    cashRuleBuf.append("每日最多提现" + rule.getCashTimes() + "笔。");
                }
                vo.setCashRule(cashRuleBuf.toString());
            }
        }
        if(vo.getCommission()==null){
            vo.setCommission(BigDecimal.ZERO);
        }
        if(vo.getCurrentCommission()==null){
            vo.setCurrentCommission(BigDecimal.ZERO);
        }
        if(vo.getUsedCommission()==null){
            vo.setUsedCommission(BigDecimal.ZERO);
        }
        return vo;
    }

    /**
     * 增加佣金明细记录
     * @param miniAccountCommissionDto
     * @return
     */
    @Override
    @Transactional
    public MiniAccountCommission add(MiniAccountCommissionDto miniAccountCommissionDto) {


        MiniAccountCommission accountCommission = new MiniAccountCommission();
        BeanUtil.copyProperties(miniAccountCommissionDto, accountCommission);




        boolean success = this.save(accountCommission);
        if(success){
            //修改个人佣金
            String shopUserId = miniAccountCommissionDto.getUserId();
            MiniAccount account = miniAccountService.getByShopUserId(shopUserId);
            BigDecimal commission = account.getCommission();
            if(commission==null){
                commission = BigDecimal.ZERO;
            }
            BigDecimal currentCommission = account.getCurrentCommission()==null?BigDecimal.ZERO:account.getCurrentCommission();
            BigDecimal usedCommission = account.getUsedCommission()==null?BigDecimal.ZERO:account.getUsedCommission();
            BigDecimal rewardCommission = account.getRewardCommission()==null?BigDecimal.ZERO:account.getRewardCommission();
            BigDecimal rewardRoyalty = account.getRewardRoyalty()==null?BigDecimal.ZERO:account.getRewardRoyalty();

            if(CommissionTypeEnum.TEAM.getType() == accountCommission.getCommissionType()){//团队佣金
                //奖励佣金
                account.setRewardCommission(rewardCommission.add(accountCommission.getAmount()));
                //团队佣金-总佣金
                account.setCommission(commission.add(accountCommission.getAmount()));
                account.setCurrentCommission(currentCommission.add(accountCommission.getAmount()));
            }else if(CommissionTypeEnum.REWARD_COMMISSION.getType() == accountCommission.getCommissionType()){//奖励佣金
                //奖励佣金
                account.setRewardCommission(rewardCommission.add(accountCommission.getAmount()));
                //团队佣金-总佣金
                account.setCommission(commission.add(accountCommission.getAmount()));
                account.setCurrentCommission(currentCommission.add(accountCommission.getAmount()));
            }else if(CommissionTypeEnum.REWARD_ROYALTY.getType() == accountCommission.getCommissionType()){//奖励提成
                //奖励提成
                account.setRewardRoyalty(rewardRoyalty.add(accountCommission.getAmount()));
                //团队佣金-总佣金
                account.setCommission(commission.add(accountCommission.getAmount()));
                account.setCurrentCommission(currentCommission.add(accountCommission.getAmount()));

            }else{// 佣金提现
                account.setUsedCommission(usedCommission.add(accountCommission.getAmount()));
                account.setCurrentCommission(currentCommission.subtract(accountCommission.getAmount()));
            }

            LambdaUpdateWrapper<MiniAccount> userUpdateWrapper = new LambdaUpdateWrapper<>();
            userUpdateWrapper.set(MiniAccount::getCommission, account.getCommission());
            userUpdateWrapper.set(MiniAccount::getUsedCommission, account.getUsedCommission());
            userUpdateWrapper.set(MiniAccount::getCurrentCommission, account.getCurrentCommission());

            userUpdateWrapper.set(MiniAccount::getRewardRoyalty, account.getRewardRoyalty());
            userUpdateWrapper.set(MiniAccount::getRewardCommission, account.getRewardCommission());

            userUpdateWrapper.eq(MiniAccount::getId, account.getId());
            this.miniAccountService.update(null, userUpdateWrapper);
            /**
             * 佣金支出
             */
            if(miniAccountCommissionDto.getCommissionType() == CommissionTypeEnum.CASH.getType()
                    &&accountCommission.getAmount().compareTo(BigDecimal.ZERO)>0){
                accountCommission.setAmount(accountCommission.getAmount().negate());
            }
            accountCommission.setLastCommission(currentCommission);
            accountCommission.setTotalCommission(account.getCurrentCommission());
            this.updateById(accountCommission);
        }
        return success ? accountCommission : null;
    }

    @Override
    public void addMiniAccountCommission(MiniAccountCommissionDto miniAccountCommissionDto) {
        MiniAccountCommission accountCommission = new MiniAccountCommission();
        BeanUtil.copyProperties(miniAccountCommissionDto, accountCommission);

        //修改个人佣金
        MiniAccount account = miniAccountService.getByShopUserId(miniAccountCommissionDto.getUserId());

        BigDecimal commission = account.getCommission() == null ? BigDecimal.ZERO : account.getCommission();
        BigDecimal currentCommission = account.getCurrentCommission() ==null ? BigDecimal.ZERO : account.getCurrentCommission();

        //团队佣金-总佣金
        account.setCommission(commission.add(accountCommission.getAmount()));
        account.setCurrentCommission(currentCommission.add(accountCommission.getAmount()));

        accountCommission.setLastCommission(currentCommission);
        accountCommission.setTotalCommission(account.getCurrentCommission());

        this.save(accountCommission);

        LambdaUpdateWrapper<MiniAccount> userUpdateWrapper = new LambdaUpdateWrapper<>();

        userUpdateWrapper.set(MiniAccount::getCommission, account.getCommission());
        userUpdateWrapper.set(MiniAccount::getCurrentCommission, account.getCurrentCommission());
        userUpdateWrapper.eq(MiniAccount::getId, account.getId());
        this.miniAccountService.update(null, userUpdateWrapper);


    }

    /**
     * 查询用户个人的佣金明细记录
     * @param param
     * @return
     */
    @Override
    public IPage<MiniAccountCommissionVo> pageMyCommission(MiniAccountCommissionParam param) {
        String userId = CurUserUtil.getHttpCurUser().getUserId();
        param.setShopUserId(userId);
        if(StringUtils.isNotEmpty(param.getStartTime())){
            param.setStartTime(param.getStartTime()+" 00:00:00");
        }
        if(StringUtils.isNotEmpty(param.getEndTime())){
            param.setEndTime(param.getEndTime()+" 23:59:59");
        }
        IPage<MiniAccountCommissionVo> page = this.baseMapper.pageMyCommission(new Page<>(param.getCurrent(), param.getSize()), param);
        return page;
    }

    @Override
    @Transactional
    public void handleCommission(OrderVo orderVo) {
        TenantContextHolder.setTenantId(orderVo.getTenantId());
        log.info("开始处理订单佣金信息");
        if(orderVo.getType().getCode()!= OrderTypeEnum.INTEGRAL.getCode()){//积分订单不参与佣金
            ShopCommissionRule openCommissionRule = remoteShopsService.getOpenCommissionRule();
            if(openCommissionRule!=null){
                BigDecimal minPayAmount = openCommissionRule.getMinPayAmount();
                Boolean flag = false;
                Integer ruleType = openCommissionRule.getRuleType();

                if(ruleType==CommissionRuleTypeEnum.REGULAR_AMOUNT.getType()){
                    if(minPayAmount!=null){
                        log.info("佣金规则类型为固定金额需要判断是否达到最低消费...");
                        BigDecimal payAmount = orderVo.getPayAmount();
                        int result = payAmount.compareTo(minPayAmount);
                        if(result==-1){
                            log.info("支付金额小于最低消费，不能添加佣金...");
                            flag = false;
                        }else{
                            log.info("支付金额大于等于最低消费，可以添加佣金...");
                            flag = true;
                        }
                    }else{
                        log.info("最低消费金额为空，不能添加佣金...");
                        flag = false;
                    }

                }else{
                    log.info("佣金规则类型为非固定金额类型");
                    flag = true;
                }

                if(openCommissionRule!=null&&flag){
                    String shopUserId = orderVo.getUserId();
                    MiniAccount miniAccount = miniAccountService.getByShopUserId(shopUserId);
                    String parentId = "";//上级userId
                    if(miniAccount!=null){
                        parentId = miniAccount.getParentId();
                    }
                    if(StringUtils.isNotEmpty(parentId)){
                        log.info("开始处理上级佣金...");
                        MiniAccountExtends miniAccountExtends = miniAccountExtendsService.findByUserId(parentId);
                        if(null == miniAccountExtends){
                            log.info("计算上级佣金，上级信息查询不到:{}", parentId);
                            return;
                        }
                        MiniAccountCommissionDto dto = new MiniAccountCommissionDto();
                        dto.setCommissionType(CommissionTypeEnum.TEAM.getType());
                        dto.setSourceShopUserId(shopUserId);
                        if(openCommissionRule.getRuleType()== CommissionRuleTypeEnum.REGULAR_AMOUNT.getType()){//固定金额
                            dto.setAmount(openCommissionRule.getParentReceive());
                        }else if(openCommissionRule.getRuleType()== CommissionRuleTypeEnum.PERCENTAGE.getType()){//百分比
                            dto.setAmount((openCommissionRule.getParentReceive().divide(new BigDecimal(100))).multiply(orderVo.getPayAmount()));
                        }else if(openCommissionRule.getRuleType()== CommissionRuleTypeEnum.GOODS.getType()) {//以单个商品
                            Long id = orderVo.getId();
                            //获取订单商品计算的上级佣金
                            BigDecimal amount = remoteOrderService.getCommissionByOrderId(id,0);
                            dto.setAmount(amount);
                        }else if(openCommissionRule.getRuleType()== CommissionRuleTypeEnum.MEMBER_LEVEL.getType()) {//根据会员等级
                            // 获取上级会员对应的会员等级记录设置的佣金规则
                            MiniAccount parentAccount = miniAccountService.getByUserId(parentId);
                            if(null == parentAccount){
                                log.info("上级会员记录在表MiniAccount不存在[{}]，不能添加佣金...", parentId);
                                return;
                            }
                            MemberLevel memberLevel = this.memberLevelService.getById(parentAccount.getMemberLevelId());
                            if(null == memberLevel){
                                log.info("上级会员等级为空[{}]，不能添加佣金...", parentAccount.getMemberLevelId());
                                return;
                            }
                            if(memberLevel.getRuleType()== CommissionRuleTypeEnum.REGULAR_AMOUNT.getType()){//固定金额
                                if(memberLevel.getMinPayAmount() != null){
                                    log.info("根据会员等级佣金规则类型为固定金额需要判断是否达到最低消费...");
                                    BigDecimal payAmount = orderVo.getPayAmount();
                                    int result = payAmount.compareTo(memberLevel.getMinPayAmount());
                                    if(result == -1){
                                        log.info("根据会员等级支付金额小于最低消费，不能添加佣金...");
                                        return;
                                    }
                                }else{
                                    log.info("根据会员等级最低消费金额为空，不能添加佣金...");
                                    return;
                                }
                                dto.setAmount(memberLevel.getParentReceive());
                            }
                            if(memberLevel.getRuleType()== CommissionRuleTypeEnum.PERCENTAGE.getType()){//百分比
                                dto.setAmount((memberLevel.getParentReceive().divide(new BigDecimal(100))).multiply(orderVo.getPayAmount()));
                            }
                        }


                        dto.setUserId(miniAccountExtends.getShopUserId());
                        dto.setOrderId(orderVo.getId()+"");
                        dto.setRemark("上级添加佣金");
                        if(dto.getAmount().compareTo(BigDecimal.ZERO)>0){
                            add(dto);
                        }else{
                            log.info("上级添加的佣金小于等于0，不添加...");
                        }

                    }else{
                        log.info("计算上级佣金，上级信息不存在...");
                    }
                    String aboveParentId = "";
                    if(miniAccount!=null){
                        aboveParentId = miniAccount.getAboveParentId();//上上级userId
                    }
                    if(StringUtils.isNotEmpty(aboveParentId)){
                        log.info("开始处理上上级佣金...");
                        MiniAccountExtends miniAccountExtends = miniAccountExtendsService.findByUserId(aboveParentId);
                        if(null == miniAccountExtends){
                            log.info("计算上上级佣金，上上级信息查询不到:{}", aboveParentId);
                            return;
                        }
                        MiniAccountCommissionDto dto = new MiniAccountCommissionDto();
                        dto.setCommissionType(CommissionTypeEnum.TEAM.getType());
                        dto.setSourceShopUserId(shopUserId);
                        if(openCommissionRule.getRuleType()== CommissionRuleTypeEnum.REGULAR_AMOUNT.getType()){//固定金额
                            dto.setAmount(openCommissionRule.getAboveParentReceive());
                        }else if(openCommissionRule.getRuleType()== CommissionRuleTypeEnum.PERCENTAGE.getType()){//百分比
                            dto.setAmount((openCommissionRule.getAboveParentReceive().divide(new BigDecimal(100))).multiply(orderVo.getPayAmount()));
                        }else if(openCommissionRule.getRuleType()== CommissionRuleTypeEnum.GOODS.getType()) {//以单个商品
                            Long id = orderVo.getId();
                            //获取订单商品计算的上级佣金
                            BigDecimal amount = remoteOrderService.getCommissionByOrderId(id,1);
                            dto.setAmount(amount);
                        }else if(openCommissionRule.getRuleType()== CommissionRuleTypeEnum.MEMBER_LEVEL.getType()) {//根据会员等级
                            // 获取上级会员对应的会员等级记录设置的佣金规则
                            MiniAccount parentAccount = miniAccountService.getByUserId(aboveParentId);
                            if(null == parentAccount){
                                log.info("上上级会员记录在表MiniAccount不存在[{}]，不能添加佣金...", aboveParentId);
                                return;
                            }
                            MemberLevel memberLevel = this.memberLevelService.getById(parentAccount.getMemberLevelId());
                            if(null == memberLevel){
                                log.info("上上级会员等级为空[{}]，不能添加佣金...", parentAccount.getMemberLevelId());
                                return;
                            }
                            if(memberLevel.getRuleType()== CommissionRuleTypeEnum.REGULAR_AMOUNT.getType()){//固定金额
                                if(memberLevel.getMinPayAmount() != null){
                                    log.info("根据会员等级佣金规则类型为固定金额需要判断是否达到最低消费...");
                                    BigDecimal payAmount = orderVo.getPayAmount();
                                    int result = payAmount.compareTo(memberLevel.getMinPayAmount());
                                    if(result == -1){
                                        log.info("根据会员等级支付金额小于最低消费，不能添加佣金...");
                                        return;
                                    }
                                }else{
                                    log.info("根据会员等级最低消费金额为空，不能添加佣金...");
                                    return;
                                }
                                dto.setAmount(memberLevel.getAboveParentReceive());
                            }
                            if(memberLevel.getRuleType()== CommissionRuleTypeEnum.PERCENTAGE.getType()){//百分比
                                dto.setAmount((memberLevel.getAboveParentReceive().divide(new BigDecimal(100))).multiply(orderVo.getPayAmount()));
                            }
                        }

                        dto.setUserId(miniAccountExtends.getShopUserId());
                        dto.setOrderId(orderVo.getId()+"");
                        dto.setRemark("上上级添加佣金");
                        if(dto.getAmount().compareTo(BigDecimal.ZERO)>0){
                            add(dto);
                        }else{
                            log.info("上上级添加的佣金小于等于0，不添加...");
                        }
                    }else{
                        log.info("计算上上级佣金，上上级信息不存在...");
                    }
                }
            }else{
                log.info("没有佣金规则...");
            }

        }else{
            log.info("积分订单不参与佣金功能...");
        }
    }

    @Override
    @Transactional
    public void handleRewardScheme(OrderVo orderVo) {
        log.info("开始处理奖励方案...");
        if(orderVo.getType().getCode()!= OrderTypeEnum.INTEGRAL.getCode()) {//积分订单不参与奖励方案
            //1.处理奖励提成
            handleRewardSchemeRoyalty(orderVo);
            //2.处理奖励佣金
            handleRewardSchemeCommission(orderVo);
        }else{
            log.info("积分订单不参与奖励方案...");
        }
    }

    /**
     * 过滤奖励方案
     * @param miniAccount
     * @param rewardList
     * @param orderItemVo
     * @return
     */
    private RewardSchemeDataVo handleRewardSchemeRoyaltyData(MiniAccount miniAccount,List<RewardSchemeDataVo>rewardList,OrderItemVo orderItemVo){
        RewardSchemeDataVo rewardSchemeDataVo = null;
        String productId = orderItemVo.getProductId()+"";//商品id
        String skuId = orderItemVo.getProductSkuId()+"";//商品skuId
        Integer priceType = orderItemVo.getPriceType();//商品价格类型
        String memberLevelId = miniAccount.getMemberLevelId();//用户会员等级

        List<RewardSchemeDataVo> filterList = new ArrayList<>();
        if(filterList==null||filterList.size()==0){
            //1.商品id，商品skuId，商品价格类型，用户会员等级都存在
            filterList = rewardList.stream().filter(e ->
                    (e.getProductId().equals(productId)&&
                            e.getSkuId().equals(skuId)&&
                            e.getPriceType() == priceType &&
                            e.getMemberLevelId().equals(memberLevelId))
            ).collect(Collectors.toList());
        }else{
            log.info("过滤商品id，商品skuId，商品价格类型，用户会员都存在时，没有对应关系");
        }

        if(filterList==null||filterList.size()==0){
            //2.商品id不存在，商品skuId存在，商品价格类型存在，用户会员等级存在
            filterList = rewardList.stream().filter(e ->
                    (StringUtils.isEmpty(e.getProductId())&&
                            e.getSkuId().equals(skuId)&&
                            e.getPriceType() == priceType &&
                            e.getMemberLevelId().equals(memberLevelId))
            ).collect(Collectors.toList());
        }else{
            log.info("过滤商品id不存在，商品skuId存在，商品价格类型存在，用户会员等级存在时，没有对应关系");
        }

        if(filterList==null||filterList.size()==0){
            //3.商品id存在，商品skuId不存在，商品价格类型存在，用户会员等级存在
            filterList = rewardList.stream().filter(e ->
                    (e.getProductId().equals(productId)&&
                            StringUtils.isEmpty(e.getSkuId())&&
                            e.getPriceType() == priceType &&
                            e.getMemberLevelId().equals(memberLevelId))
            ).collect(Collectors.toList());
        }else{
            log.info("过滤商品id存在，商品skuId不存在，商品价格类型存在，用户会员等级存在时，没有对应关系");
        }

        if(filterList==null||filterList.size()==0){
            //4.商品id存在，商品skuId存在，商品价格类型不存在，用户会员等级存在
            filterList = rewardList.stream().filter(e ->
                    (e.getProductId().equals(productId)&&
                            e.getSkuId().equals(skuId)&&
                            (e.getPriceType() == null || e.getPriceType().equals("")) &&
                            e.getMemberLevelId().equals(memberLevelId))
            ).collect(Collectors.toList());
        }else{
            log.info("过滤商品id存在，商品skuId存在，商品价格类型不存在，用户会员等级存在关系");
        }


        if(filterList==null||filterList.size()==0){
            //5.商品id存在，商品skuId存在，商品价格类型存在，用户会员等级不存在
            filterList = rewardList.stream().filter(e ->
                    (e.getProductId().equals(productId)&&
                            e.getSkuId().equals(skuId)&&
                            e.getPriceType() == priceType &&
                            StringUtils.isEmpty(e.getMemberLevelId()))
            ).collect(Collectors.toList());
        }else{
            log.info("过滤商品id存在，商品skuId存在，商品价格类型存在，用户会员等级不存在时，没有对应关系");
        }


        if(filterList==null||filterList.size()==0){
            //6.商品id不存在，商品skuId不存在，商品价格类型存在，用户会员等级存在
            filterList = rewardList.stream().filter(e ->
                    (StringUtils.isEmpty(e.getProductId())&&
                            StringUtils.isEmpty(e.getSkuId())&&
                            e.getPriceType() == priceType &&
                            e.getMemberLevelId().equals(memberLevelId))
            ).collect(Collectors.toList());
        }else{
            log.info("过滤商品id不存在，商品skuId不存在，商品价格类型存在，用户会员等级存在时，没有对应关系");
        }

        if(filterList==null||filterList.size()==0){
            //7.商品id不存在，商品skuId存在，商品价格类型不存在，用户会员等级存在
            filterList = rewardList.stream().filter(e ->
                    (StringUtils.isEmpty(e.getProductId())&&
                            e.getSkuId().equals(skuId)&&
                            (e.getPriceType() == null || e.getPriceType().equals("")) &&
                            e.getMemberLevelId().equals(memberLevelId))
            ).collect(Collectors.toList());
        }else{
            log.info("商品id不存在，商品skuId存在，商品价格类型不存在，用户会员等级存在时，没有对应关系");
        }

        if(filterList==null||filterList.size()==0){
            //7.商品id不存在，商品skuId存在，商品价格类型存在，用户会员等级不存在
            filterList = rewardList.stream().filter(e ->
                    (StringUtils.isEmpty(e.getProductId())&&
                            e.getSkuId().equals(skuId)&&
                            e.getPriceType() == priceType &&
                            StringUtils.isEmpty(e.getMemberLevelId()))
            ).collect(Collectors.toList());
        }else{
            log.info("过滤商品id不存在，商品skuId存在，商品价格类型存在，用户会员等级不存在时，没有对应关系");
        }


        if(filterList==null||filterList.size()==0){
            //8.商品id存在，商品skuId不存在，商品价格类型不存在，用户会员等级存在
            filterList = rewardList.stream().filter(e ->
                    (e.getProductId().equals(productId)&&
                            StringUtils.isEmpty(e.getSkuId())&&
                            (e.getPriceType() == null || e.getPriceType().equals("")) &&
                            e.getMemberLevelId().equals(memberLevelId))
            ).collect(Collectors.toList());
        }else{
            log.info("过滤商品id，商品skuId，商品价格类型，用户会员都存在时，没有对应关系");
        }

        if(filterList==null||filterList.size()==0){
            //9.商品id存在，商品skuId不存在，商品价格类型存在，用户会员等级不存在
            filterList = rewardList.stream().filter(e ->
                    (e.getProductId().equals(productId)&&
                            StringUtils.isEmpty(e.getSkuId())&&
                            e.getPriceType() == priceType &&
                            StringUtils.isEmpty(e.getMemberLevelId()))
            ).collect(Collectors.toList());
        }else{
            log.info("过滤商品id存在，商品skuId不存在，商品价格类型存在，用户会员等级不存在时，没有对应关系");
        }

        if(filterList==null||filterList.size()==0){
            //10.商品id存在，商品skuId存在，商品价格类型不存在，用户会员等级不存在
            filterList = rewardList.stream().filter(e ->
                    (e.getProductId().equals(productId)&&
                            e.getSkuId().equals(skuId)&&
                            (e.getPriceType() == null || e.getPriceType().equals(""))  &&
                            StringUtils.isEmpty(e.getMemberLevelId()))
            ).collect(Collectors.toList());
        }else{
            log.info("过滤商品id存在，商品skuId存在，商品价格类型不存在，用户会员等级不存在时，没有对应关系");
        }


        if(filterList==null||filterList.size()==0){
            //11.商品id不存在，商品skuId存在，商品价格类型不存在，用户会员等级不存在
            filterList = rewardList.stream().filter(e ->
                    (StringUtils.isEmpty(e.getProductId())&&
                            e.getSkuId().equals(skuId)&&
                            (e.getPriceType() == null || e.getPriceType().equals("")) &&
                            StringUtils.isEmpty(e.getMemberLevelId()))
            ).collect(Collectors.toList());
        }else{
            log.info("过滤商品id不存在，商品skuId存在，商品价格类型不存在，用户会员等级不存在时，没有对应关系");
        }


        if(filterList==null||filterList.size()==0){
            //12.商品id不存在，商品skuId不存在，商品价格类型存在，用户会员等级不存在
            filterList = rewardList.stream().filter(e ->
                    (StringUtils.isEmpty(e.getProductId())&&
                            StringUtils.isEmpty(e.getSkuId())&&
                            e.getPriceType() == priceType &&
                            StringUtils.isEmpty(e.getMemberLevelId()))
            ).collect(Collectors.toList());
        }else{
            log.info("过滤商品id不存在，商品skuId不存在，商品价格类型存在，用户会员等级不存在时，没有对应关系");
        }

        if(filterList==null||filterList.size()==0){
            //13.商品id不存在，商品skuId不存在，商品价格类型不存在，用户会员等级存在
            filterList = rewardList.stream().filter(e ->
                    (StringUtils.isEmpty(e.getProductId())&&
                            StringUtils.isEmpty(e.getSkuId())&&
                            (e.getPriceType() == null || e.getPriceType().equals("")) &&
                            e.getMemberLevelId().equals(memberLevelId))
            ).collect(Collectors.toList());
        }else{
            log.info("过滤商品id，商品skuId，商品价格类型，用户会员都存在时，没有对应关系");
        }

        if(filterList==null||filterList.size()==0){
            //14.商品id存在，商品skuId不存在，商品价格类型不存在，用户会员等级不存在
            filterList = rewardList.stream().filter(e ->
                    (e.getProductId().equals(productId)&&
                            StringUtils.isEmpty(e.getSkuId())&&
                            (e.getPriceType() == null || e.getPriceType().equals("")) &&
                            StringUtils.isEmpty(e.getMemberLevelId()))
            ).collect(Collectors.toList());
        }else{
            log.info("过滤商品id，商品skuId，商品价格类型，用户会员都存在时，没有对应关系");
        }

        if(filterList==null||filterList.size()==0){
            //15.商品id不存在，商品skuId不存在，商品价格类型不存在，用户会员等级不存在
            filterList = rewardList.stream().filter(e ->
                    (StringUtils.isEmpty(e.getProductId())&&
                            StringUtils.isEmpty(e.getSkuId())&&
                            (e.getPriceType() == null || e.getPriceType().equals("")) &&
                            StringUtils.isEmpty(e.getMemberLevelId()))
            ).collect(Collectors.toList());
        }else{
            log.info("过滤商品id不存在，商品skuId不存在，商品价格类型不存在，用户会员等级不存在时，没有对应关系");
        }

        if(filterList!=null&&filterList.size()>0){
            rewardSchemeDataVo = filterList.get(0);
        }


        return rewardSchemeDataVo;
    }

    @Override
    @Transactional
    public void handleRewardSchemeRoyalty(OrderVo orderVo) {
        TenantContextHolder.setTenantId(orderVo.getTenantId());
        log.info("开始处理奖励提成...");
        if(orderVo.getCompleteTime()!=null){
            String completeTime = LocalDateTimeUtils.formatTime(orderVo.getCompleteTime(), "yyyy-MM-dd HH:mm:ss");
            Integer type = RewardTypeEnum.ROYALTY.getStatus();
            List<OrderItemVo> orderItemList = orderVo.getOrderItemList();
            List<RewardSchemeDataVo>rewardList = remoteGoodsService.getRewardSchemeData(completeTime,type);
            String shopUserId = orderVo.getUserId();
            MiniAccount miniAccount = miniAccountService.getByShopUserId(shopUserId);

            String userId = "";//用户userId
            if(miniAccount!=null){
                userId = miniAccount.getUserId();
            }
            String parentId = "";//上级userId
            if(miniAccount!=null){
                parentId = miniAccount.getParentId();
            }
            String aboveParentId = "";//上上级userId
            if(miniAccount!=null){
                aboveParentId = miniAccount.getAboveParentId();
            }
            if(rewardList.size()>0){
                BigDecimal payAmount = orderVo.getPayAmount();//应付金额
                BigDecimal totalAmount = orderVo.getTotalAmount();//订单总金额
                BigDecimal freightAmount = orderVo.getFreightAmount();//运费金额
                if(freightAmount == null){
                    freightAmount = BigDecimal.ZERO;
                }
                for (OrderItemVo orderItemVo : orderItemList) {
                    BigDecimal realAmount = orderItemVo.getRealAmount();//明细金额
                    //1.个人提成
                    if(StringUtils.isNotEmpty(userId)){
                        if(miniAccount!=null){
                            RewardSchemeDataVo rewardSchemeDataVo = handleRewardSchemeRoyaltyData(miniAccount,rewardList,orderItemVo);
                            if(rewardSchemeDataVo!=null){
                                BigDecimal royaltyRate = rewardSchemeDataVo.getRoyaltyRate();
                                BigDecimal royaltyAmount = rewardSchemeDataVo.getRoyaltyAmount();
                                MiniAccountCommissionDto dto = new MiniAccountCommissionDto();
                                dto.setCommissionType(CommissionTypeEnum.REWARD_ROYALTY.getType());
                                dto.setSourceShopUserId(shopUserId);
                                dto.setOrderId(orderVo.getId()+"");
                                MiniAccountExtends miniAccountExtends = miniAccountExtendsService.findByUserId(userId);
                                dto.setUserId(miniAccountExtends.getShopUserId());
                                dto.setRewardId(rewardSchemeDataVo.getRewardId());
                                dto.setRewardDetId(rewardSchemeDataVo.getId()+"");
                                BigDecimal amount = BigDecimal.ZERO;
                                if(royaltyAmount!=null&&!royaltyAmount.equals("")){
                                    amount = royaltyAmount;
                                }
                                if(royaltyRate!=null&&!royaltyRate.equals("")){
                                    //提成金额 = （订单明细金额/（订单总金额-运费金额））* （订单实付金额-运费金额） * 提成比例
                                    totalAmount = totalAmount.subtract(freightAmount);
                                    payAmount = payAmount.subtract(freightAmount);
                                    BigDecimal actualScale = realAmount.divide(totalAmount,2, RoundingMode.HALF_UP);
                                    BigDecimal actualAmount = actualScale.multiply(payAmount);
                                    royaltyRate = royaltyRate.divide(new BigDecimal(100),2, RoundingMode.HALF_UP);
                                    amount = actualAmount.multiply(royaltyRate);
                                }
                                dto.setRemark("个人下单");
                                dto.setAmount(amount);
                                if(dto.getAmount().compareTo(BigDecimal.ZERO)>0){
                                    add(dto);
                                }else{
                                    log.info("个人下单获得的提成小于等于0，不添加...");
                                }
                            }

                        }else{
                            throw new ServiceException("用户不存在！");
                        }

                    }
                    //2.上级提成
                    if(StringUtils.isNotEmpty(parentId)){
                        MiniAccount parentMiniAccount = miniAccountMapper.selectOne(new QueryWrapper<MiniAccount>().eq("user_id", parentId));
                        if(parentMiniAccount!=null){
                            RewardSchemeDataVo rewardSchemeDataVo = handleRewardSchemeRoyaltyData(parentMiniAccount,rewardList,orderItemVo);
                            if(rewardSchemeDataVo!=null){
                                BigDecimal oneCommissionRate = rewardSchemeDataVo.getOneCommissionRate();
                                BigDecimal oneCommissionAmount = rewardSchemeDataVo.getOneCommissionAmount();
                                MiniAccountCommissionDto dto = new MiniAccountCommissionDto();
                                dto.setCommissionType(CommissionTypeEnum.REWARD_ROYALTY.getType());
                                dto.setSourceShopUserId(shopUserId);
                                dto.setOrderId(orderVo.getId()+"");
                                MiniAccountExtends miniAccountExtends = miniAccountExtendsService.findByUserId(parentId);
                                dto.setUserId(miniAccountExtends.getShopUserId());
                                dto.setRewardId(rewardSchemeDataVo.getRewardId());
                                dto.setRewardDetId(rewardSchemeDataVo.getId()+"");
                                BigDecimal amount = BigDecimal.ZERO;
                                if(oneCommissionAmount!=null&&!oneCommissionAmount.equals("")){
                                    amount = oneCommissionAmount;
                                }
                                if(oneCommissionRate!=null&&!oneCommissionRate.equals("")){
                                    //提成金额 = （订单明细金额/（订单总金额-运费金额））* （订单实付金额-运费金额） * 提成比例
                                    totalAmount = totalAmount.subtract(freightAmount);
                                    payAmount = payAmount.subtract(freightAmount);
                                    BigDecimal actualScale = realAmount.divide(totalAmount,2, RoundingMode.HALF_UP);
                                    BigDecimal actualAmount = actualScale.multiply(payAmount);
                                    oneCommissionRate = oneCommissionRate.divide(new BigDecimal(100),2, RoundingMode.HALF_UP);
                                    amount = actualAmount.multiply(oneCommissionRate);
                                }
                                dto.setRemark("分销下单");
                                dto.setAmount(amount);
                                if(dto.getAmount().compareTo(BigDecimal.ZERO)>0){
                                    add(dto);
                                }else{
                                    log.info("分销下单获得的提成小于等于0，不添加...");
                                }
                            }
                        }else{
                            throw new ServiceException("上级用户不存在！");
                        }
                    }
                    //3.上上级提成
                    if(StringUtils.isNotEmpty(aboveParentId)){
                        MiniAccount aboveParentMiniAccount = miniAccountMapper.selectOne(new QueryWrapper<MiniAccount>().eq("user_id", aboveParentId));
                        if(aboveParentMiniAccount!=null){
                            RewardSchemeDataVo rewardSchemeDataVo = handleRewardSchemeRoyaltyData(aboveParentMiniAccount,rewardList,orderItemVo);
                            if(rewardSchemeDataVo!=null){
                                BigDecimal twoCommissionRate = rewardSchemeDataVo.getTwoCommissionRate();
                                BigDecimal twoCommissionAmount = rewardSchemeDataVo.getTwoCommissionAmount();
                                MiniAccountCommissionDto dto = new MiniAccountCommissionDto();
                                dto.setCommissionType(CommissionTypeEnum.REWARD_ROYALTY.getType());
                                dto.setSourceShopUserId(shopUserId);
                                dto.setOrderId(orderVo.getId()+"");
                                MiniAccountExtends miniAccountExtends = miniAccountExtendsService.findByUserId(aboveParentId);
                                dto.setUserId(miniAccountExtends.getShopUserId());
                                dto.setRewardId(rewardSchemeDataVo.getRewardId());
                                dto.setRewardDetId(rewardSchemeDataVo.getId()+"");
                                BigDecimal amount = BigDecimal.ZERO;
                                if(twoCommissionAmount!=null&&!twoCommissionAmount.equals("")){
                                    amount = twoCommissionAmount;
                                }
                                if(twoCommissionRate!=null&&!twoCommissionRate.equals("")){
                                    //提成金额 = （订单明细金额/（订单总金额-运费金额））* （订单实付金额-运费金额） * 提成比例
                                    totalAmount = totalAmount.subtract(freightAmount);
                                    payAmount = payAmount.subtract(freightAmount);
                                    BigDecimal actualScale = realAmount.divide(totalAmount,2, RoundingMode.HALF_UP);
                                    BigDecimal actualAmount = actualScale.multiply(payAmount);
                                    twoCommissionRate = twoCommissionRate.divide(new BigDecimal(100),2, RoundingMode.HALF_UP);
                                    amount = actualAmount.multiply(twoCommissionRate);
                                }
                                dto.setRemark("分销下单");
                                dto.setAmount(amount);
                                if(dto.getAmount().compareTo(BigDecimal.ZERO)>0){
                                    add(dto);
                                }else{
                                    log.info("分销下单获得的提成小于等于0，不添加...");
                                }
                            }

                        }else{
                            throw new ServiceException("上上级用户不存在！");
                        }
                    }


                }
            }else{
                log.info("完成时间{}对应的奖励方案-提成不存在...",completeTime);
            }
        }else{
            log.info("订单未完成...");
        }
    }

    @Override
    public void handleRewardSchemeCommission(OrderVo orderVo) {
        TenantContextHolder.setTenantId(orderVo.getTenantId());
        log.info("开始处理奖励佣金...");
        if(orderVo.getCompleteTime()!=null){
            String completeTime = LocalDateTimeUtils.formatTime(orderVo.getCompleteTime(), "yyyy-MM-dd HH:mm:ss");
            Integer type = RewardTypeEnum.COMMISSION.getStatus();
            List<OrderItemVo> orderItemList = orderVo.getOrderItemList();
            List<RewardSchemeDataVo>rewardList = remoteGoodsService.getRewardSchemeData(completeTime,type);
            String shopUserId = orderVo.getUserId();
            MiniAccount miniAccount = miniAccountService.getByShopUserId(shopUserId);

            String userId = "";//用户userId
            if(miniAccount!=null){
                userId = miniAccount.getUserId();
            }
            MiniAccountExtends userExtends = miniAccountExtendsService.findByUserId(userId);
            BigDecimal memberMoney = userExtends.getMemberMoney();//会员消费额
            String parentId = "";//上级userId
            if(miniAccount!=null){
                parentId = miniAccount.getParentId();
            }
            String aboveParentId = "";//上上级userId
            if(miniAccount!=null){
                aboveParentId = miniAccount.getAboveParentId();
            }
            if(rewardList.size()>0){
                BigDecimal payAmount = orderVo.getPayAmount();//应付金额
                BigDecimal totalAmount = orderVo.getTotalAmount();//订单总金额
                BigDecimal freightAmount = orderVo.getFreightAmount();//运费金额
                if(freightAmount == null){
                    freightAmount = BigDecimal.ZERO;
                }
                for (OrderItemVo orderItemVo : orderItemList) {
                    BigDecimal realAmount = orderItemVo.getRealAmount();//明细金额
                    //1.上级佣金
                    if(StringUtils.isNotEmpty(parentId)){
                        MiniAccount parentMiniAccount = miniAccountMapper.selectOne(new QueryWrapper<MiniAccount>().eq("user_id", parentId));
                        if(parentMiniAccount!=null){
                            RewardSchemeDataVo rewardSchemeDataVo = handleRewardSchemeRoyaltyData(parentMiniAccount,rewardList,orderItemVo);
                            if(rewardSchemeDataVo!=null){
                                BigDecimal oneCommissionRate = rewardSchemeDataVo.getOneCommissionRate();
                                BigDecimal oneCommissionAmount = rewardSchemeDataVo.getOneCommissionAmount();
                                MiniAccountCommissionDto dto = new MiniAccountCommissionDto();
                                dto.setCommissionType(CommissionTypeEnum.REWARD_COMMISSION.getType());
                                dto.setSourceShopUserId(shopUserId);
                                dto.setOrderId(orderVo.getId()+"");
                                MiniAccountExtends miniAccountExtends = miniAccountExtendsService.findByUserId(parentId);
                                dto.setUserId(miniAccountExtends.getShopUserId());
                                dto.setRewardId(rewardSchemeDataVo.getRewardId());
                                dto.setRewardDetId(rewardSchemeDataVo.getId()+"");
                                BigDecimal amount = BigDecimal.ZERO;
                                if(oneCommissionAmount!=null&&!oneCommissionAmount.equals("")){
                                    amount = oneCommissionAmount;
                                }
                                if(oneCommissionRate!=null&&!oneCommissionRate.equals("")){
                                    //佣金金额 = （订单明细金额/（订单总金额-运费金额））* （订单实付金额-运费金额） * 佣金比例
                                    totalAmount = totalAmount.subtract(freightAmount);
                                    payAmount = payAmount.subtract(freightAmount);
                                    BigDecimal actualScale = realAmount.divide(totalAmount,2, RoundingMode.HALF_UP);
                                    BigDecimal actualAmount = actualScale.multiply(payAmount);
                                    oneCommissionRate = oneCommissionRate.divide(new BigDecimal(100),2, RoundingMode.HALF_UP);
                                    amount = actualAmount.multiply(oneCommissionRate);
                                }
                                if(orderItemVo.getPriceType()!=null&&!orderItemVo.getPriceType().equals("")){
                                    if(orderItemVo.getPriceType() == PriceTypeEnum.MEMBER.getStatus()){

                                        if(memberMoney.compareTo(BigDecimal.ZERO)>0){
                                            dto.setRemark("团队分佣");
                                        }else{
                                            dto.setRemark("会员首销分佣");
                                        }
                                    }else if(orderItemVo.getPriceType() == PriceTypeEnum.REPEAT.getStatus()){
                                        dto.setRemark("团队复购分佣");
                                    }else {
                                        dto.setRemark("团队分佣");
                                    }
                                }else{
                                    dto.setRemark("团队分佣");
                                }


                                dto.setAmount(amount);
                                if(dto.getAmount().compareTo(BigDecimal.ZERO)>0){
                                    add(dto);
                                }else{
                                    log.info("分销下单获得的佣金小于等于0，不添加...");
                                }
                            }

                        }else{
                            throw new ServiceException("上级用户不存在！");
                        }
                    }
                    //2.上上级佣金
                    if(StringUtils.isNotEmpty(aboveParentId)){
                        MiniAccount aboveParentMiniAccount = miniAccountMapper.selectOne(new QueryWrapper<MiniAccount>().eq("user_id", aboveParentId));
                        if(aboveParentMiniAccount!=null){
                            RewardSchemeDataVo rewardSchemeDataVo = handleRewardSchemeRoyaltyData(aboveParentMiniAccount,rewardList,orderItemVo);
                            if(rewardSchemeDataVo!=null){
                                BigDecimal twoCommissionRate = rewardSchemeDataVo.getTwoCommissionRate();
                                BigDecimal twoCommissionAmount = rewardSchemeDataVo.getTwoCommissionAmount();
                                MiniAccountCommissionDto dto = new MiniAccountCommissionDto();
                                dto.setCommissionType(CommissionTypeEnum.REWARD_COMMISSION.getType());
                                dto.setSourceShopUserId(shopUserId);
                                dto.setOrderId(orderVo.getId()+"");
                                MiniAccountExtends miniAccountExtends = miniAccountExtendsService.findByUserId(aboveParentId);
                                dto.setUserId(miniAccountExtends.getShopUserId());
                                dto.setRewardId(rewardSchemeDataVo.getRewardId());
                                dto.setRewardDetId(rewardSchemeDataVo.getId()+"");
                                BigDecimal amount = BigDecimal.ZERO;
                                if(twoCommissionAmount!=null&&!twoCommissionAmount.equals("")){
                                    amount = twoCommissionAmount;
                                }
                                if(twoCommissionRate!=null&&!twoCommissionRate.equals("")){
                                    //佣金金额 = （订单明细金额/（订单总金额-运费金额））* （订单实付金额-运费金额） * 佣金比例
                                    totalAmount = totalAmount.subtract(freightAmount);
                                    payAmount = payAmount.subtract(freightAmount);
                                    BigDecimal actualScale = realAmount.divide(totalAmount,2, RoundingMode.HALF_UP);
                                    BigDecimal actualAmount = actualScale.multiply(payAmount);
                                    twoCommissionRate = twoCommissionRate.divide(new BigDecimal(100),2, RoundingMode.HALF_UP);
                                    amount = actualAmount.multiply(twoCommissionRate);
                                }
                                if(orderItemVo.getPriceType()!=null&&!orderItemVo.getPriceType().equals("")){
                                    if(orderItemVo.getPriceType() == PriceTypeEnum.MEMBER.getStatus()){
                                        if(memberMoney.compareTo(BigDecimal.ZERO)>0){
                                            dto.setRemark("团队分佣");
                                        }else{
                                            dto.setRemark("会员首销分佣");
                                        }
                                    }else if(orderItemVo.getPriceType() == PriceTypeEnum.REPEAT.getStatus()){
                                        dto.setRemark("团队复购分佣");
                                    }else {
                                        dto.setRemark("团队分佣");
                                    }
                                }else{
                                    dto.setRemark("团队分佣");
                                }
                                dto.setAmount(amount);
                                if(dto.getAmount().compareTo(BigDecimal.ZERO)>0){
                                    add(dto);
                                }else{
                                    log.info("分销下单获得的佣金小于等于0，不添加...");
                                }
                            }
                        }else{
                            throw new ServiceException("上上级用户不存在！");
                        }
                    }
                }
            }else{
                log.info("完成时间{}对应的奖励方案-佣金不存在...",completeTime);
            }
        }else{
            log.info("订单未完成...");
        }
    }

    @Override
    public UserCommissionVo getUserCommissionVo() {
        // 非shop_user_id，是miniAccount的user_id
        String userId = CurUserUtil.getMiniReqeustAccountInfo().getUserId();
        UserCommissionVo userCommissionVo = this.baseMapper.getUserCommissionVo(userId);
        List<SpecialSetting> specialSettingList = remoteMiniInfoService.getSpecialSetting();
        Integer aggregationFlag = 0;
        if(specialSettingList!=null&&specialSettingList.size()>0){
            SpecialSetting specialSetting = specialSettingList.get(0);
            aggregationFlag = specialSetting.getAggregationFlag();
        }
        userCommissionVo.setAggregationFlag(aggregationFlag);
        return userCommissionVo;
    }

    @Override
    public PageUtils<DistributionOrderVo> getDistributionOrderVo(DistributionOrderParam distributionOrderParam) {
        CurUserDto curUser = CurUserUtil.getHttpCurUser();
        if (curUser == null) {
            throw new ServiceException("token错误", SystemCode.DATA_NOT_EXIST_CODE);
        }
        distributionOrderParam.setShopUserId(curUser.getUserId());
        IPage<DistributionOrderVo> page = this.baseMapper.getDistributionOrderVo(new Page<>(distributionOrderParam.getCurrent(), distributionOrderParam.getSize()), distributionOrderParam);
        return new PageUtils<>(page);
    }

    @Override
    public PageUtils<MiniAccountRoyaltyDetVo> searchMiniAccountRoyaltyDet(MiniAccountRoyaltyDetParam param) {
        IPage<MiniAccountRoyaltyDetVo> page = this.baseMapper.searchMiniAccountRoyaltyDet(new Page<>(param.getCurrent(), param.getSize()), param);
        return new PageUtils<>(page);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void transferCommission(ApiMiniAccountCommissionDto dto) {
        if(StringUtils.isEmpty(dto.getUserId())){
            throw new ServiceException("小程序用户id不能为空！", SystemCode.DATA_NOT_EXIST_CODE);
        }
        if(StringUtils.isEmpty(dto.getPhone())){
            throw new ServiceException("转赠用户手机号不能为空！", SystemCode.DATA_NOT_EXIST_CODE);
        }
        LambdaQueryWrapper<MiniAccount>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MiniAccount::getUserId,dto.getUserId());
        wrapper.eq(MiniAccount::getPhone,dto.getPhone());
        List<MiniAccount> list = this.miniAccountService.list(wrapper);
        if(list == null || list.size() == 0){
            throw new ServiceException("该用户不存在，请检查手机号是否正确！");
        }
        if(list.size()>1){
            throw new ServiceException("用户重复了，请联系管理员处理");
        }
        //赠予用户
        MiniAccount receiveMiniAccount = list.get(0);
        //赠予用户扩展信息
        MiniAccountExtends receiveMiniAccountExtends = miniAccountExtendsService.findByUserId(receiveMiniAccount.getUserId());
        //接收人用户ID
        String receiveShopUserId = receiveMiniAccountExtends.getShopUserId();

        CurUserDto curUser = CurUserUtil.getHttpCurUser();
        //当前用户
        MiniAccount miniAccount = miniAccountService.getByShopUserId(curUser.getUserId());
        if (miniAccount == null) {
            throw new ServiceException("数据错误！", SystemCode.DATA_NOT_EXIST_CODE);
        }
        //当前用户扩展信息
        MiniAccountExtends miniAccountExtends = miniAccountExtendsService.findByShopUserId(curUser.getUserId());
        String transferShopUserId = miniAccountExtends.getShopUserId();
        String phone = miniAccount.getPhone();
        //判断手机号是否存在
        if(StringUtils.isEmpty(phone)){
            throw new ServiceException("验证手机号不能为空！", SystemCode.DATA_NOT_EXIST_CODE);
        }
        String smsCode = dto.getSmsCode();
        //判断验证码是否存在
        if(StringUtils.isEmpty(smsCode)){
            throw new ServiceException("手机验证码不能为空！", SystemCode.DATA_NOT_EXIST_CODE);
        }
        //验证验证码正确性
        AccountRedis accountRedis = new AccountRedis();
        String redisKey = RedisConstant.PHONE_KEY.concat(dto.getType().toString()).concat(":").concat(phone);
        String code = accountRedis.get(redisKey);
        if(code == null || !code.equals(dto.getSmsCode())){
            throw new ServiceException("验证码不正确，请重新输入！", SystemCode.DATA_NOT_EXIST_CODE);
        }
        //验证密码不能为空
        String payPwd = dto.getPayPwd();
        if(StringUtils.isEmpty(payPwd)){
            throw new ServiceException("支付密码不能为空！", SystemCode.DATA_NOT_EXIST_CODE);
        }
        //验证密码
        String md5Pw = SecureUtil.md5(payPwd.concat(miniAccountExtends.getPaySalt()));
        if (!md5Pw.equals(miniAccountExtends.getPayPwd())) {
            throw new ServiceException("账号或密码错误！");
        }
        //验证转赠余额不能为空
        BigDecimal amount = dto.getAmount();
        if(amount == null || amount.equals("") || amount.compareTo(BigDecimal.ZERO)<=0){
            throw new ServiceException("转赠佣金必须大于0！");
        }
        //转赠金额不能大于当前可使用佣金
        if(amount.compareTo(miniAccount.getCurrentCommission())>0){
            throw new ServiceException("转赠金额不能大于个人余额！");
        }

        //当前用户添加一条佣金转赠记录-减掉佣金
        MiniAccountCommissionDto transferMiniAccountCommission = new MiniAccountCommissionDto();
        transferMiniAccountCommission.setAmount(amount.negate());
        transferMiniAccountCommission.setRemark("佣金转赠");
        transferMiniAccountCommission.setUserId(miniAccountExtends.getShopUserId());
        transferMiniAccountCommission.setReceiveShopUserId(receiveShopUserId);
        transferMiniAccountCommission.setCommissionType(CommissionTypeEnum.TRANSFER_COMMISSION.getType());
        add(transferMiniAccountCommission);
        //转赠用户添加一条佣金转赠记录-增加佣金
        MiniAccountCommissionDto receiveMiniAccountCommission = new MiniAccountCommissionDto();
        receiveMiniAccountCommission.setAmount(amount);
        receiveMiniAccountCommission.setRemark("佣金转赠");
        receiveMiniAccountCommission.setUserId(receiveMiniAccountExtends.getShopUserId());
        receiveMiniAccountCommission.setTransferShopUserId(transferShopUserId);
        receiveMiniAccountCommission.setCommissionType(CommissionTypeEnum.TRANSFER_COMMISSION.getType());
        add(receiveMiniAccountCommission);
        //删除验证码redis
        accountRedis.del(redisKey);
    }

    /**
     * 手动添加佣金
     * @param miniAccountCommissionDto
     * @return
     */
    @Override
    @Transactional
    public MiniAccountCommission manualAddCommission(MiniAccountCommissionDto miniAccountCommissionDto) {
        // 查询用户是否存在
        MiniAccount miniAccount = miniAccountService.getByShopUserId(miniAccountCommissionDto.getUserId());
        if (null == miniAccount) {
            throw new ServiceException("用户记录不存在，请刷新后重试！");
        }
        if(null == miniAccountCommissionDto.getCommissionType()){
            throw new ServiceException("请选择佣金类型");
        }
        if (miniAccountCommissionDto.getCommissionType() == CommissionTypeEnum.COMMISSION.getType()||
                miniAccountCommissionDto.getCommissionType() == CommissionTypeEnum.SAME_LEVEL.getType()||
                miniAccountCommissionDto.getCommissionType() == CommissionTypeEnum.DIFFER.getType()||
                miniAccountCommissionDto.getCommissionType() == CommissionTypeEnum.TEAM_COMMISSION.getType()||
                miniAccountCommissionDto.getCommissionType() == CommissionTypeEnum.CYCLE_COMMISSION.getType()) {
            if(StrUtil.isBlank(miniAccountCommissionDto.getOrderId())){
                throw new ServiceException("订单信息不能为空，请选择订单");
            }
            // 查询订单是否存在和订单的状态，以及订单是否给此人分佣过
            String shopId = ShopContextHolder.getShopId();
            ShopContextHolder.setShopId(CommonConstants.DEFAULT_SHOP_ID);
            Order order = this.remoteOrderService.getOrderById(Long.parseLong(miniAccountCommissionDto.getOrderId()));
            ShopContextHolder.setShopId(shopId);
            if (null == order) {
                throw new ServiceException("订单信息不存在，请重新选择");
            }
            if (order.getStatus().getCode() != OrderStatusEnum.COMPLETE.getCode()) {
                throw new ServiceException("订单完成状态才允许手动添加佣金");
            }
            // 查询此订单是否已经给过此用户佣金
            LambdaQueryWrapper<MiniAccountCommission> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(MiniAccountCommission::getOrderId, order.getId())
                    .eq(MiniAccountCommission::getUserId, miniAccountCommissionDto.getUserId());
            List<MiniAccountCommission> accountCommissionList = this.list(lambdaQueryWrapper);
            if (CollectionUtil.isNotEmpty(accountCommissionList)) {
                throw new ServiceException("该用户已经通过此订单获得分佣，不能多次分佣");
            }
            miniAccountCommissionDto.setSourceShopUserId(order.getUserId());
        }

        miniAccountCommissionDto.setPlatformUserId(CurUserUtil.getHttpCurUser().getUserId());
        miniAccountCommissionDto.setPlatformUserName(CurUserUtil.getHttpCurUser().getNikeName());
        miniAccountCommissionDto.setSource(CommonConstants.NUMBER_ONE);
        if (miniAccountCommissionDto.getCommissionType() == CommissionTypeEnum.COMMISSION.getType()||
                miniAccountCommissionDto.getCommissionType() == CommissionTypeEnum.SAME_LEVEL.getType()||
                miniAccountCommissionDto.getCommissionType() == CommissionTypeEnum.DIFFER.getType()||
                miniAccountCommissionDto.getCommissionType() == CommissionTypeEnum.TEAM_COMMISSION.getType()||
                miniAccountCommissionDto.getCommissionType() == CommissionTypeEnum.CYCLE_COMMISSION.getType()) {
            addMiniAccountCommission(miniAccountCommissionDto);
            return null;
        }else if(miniAccountCommissionDto.getCommissionType() == CommissionTypeEnum.ORDER_COMMISSION.getType()){
            miniAccountCommissionDto.setAmount(miniAccountCommissionDto.getAmount().negate());
            addMiniAccountCommission(miniAccountCommissionDto);
            return null;
        }else if (miniAccountCommissionDto.getCommissionType() == CommissionTypeEnum.CASH.getType()) {
            // 提现
            MiniAccountManualCommissionCashDto miniAccountManualCommissionCashDto = new MiniAccountManualCommissionCashDto();
            miniAccountManualCommissionCashDto.setUserId(miniAccountCommissionDto.getUserId());
            miniAccountManualCommissionCashDto.setAmount(miniAccountCommissionDto.getAmount());
            miniAccountManualCommissionCashDto.setPlatformRemark(miniAccountCommissionDto.getPlatformRemark());
            miniAccountCommissionDto.setRemark(CommissionTypeEnum.CASH.getDesc());
            MiniAccountCommission commission = add(miniAccountCommissionDto);
            this.miniAccountCommissionCashService.manualAddCash(miniAccountManualCommissionCashDto);
            return commission;
        }
        return null;
    }

    @Override
    public void exportMiniAccountRoyaltyDet(MiniAccountRoyaltyDetParam param ) {
        // 设置导出最大限制
        HuToolExcelUtils.exportParamToMax(param);

        // 获取查询数据
        PageUtils<MiniAccountRoyaltyDetVo> pageUtils = searchMiniAccountRoyaltyDet(param);
        List<MiniAccountRoyaltyDetVo> dataList = pageUtils.getList();

        // 使用Lambda转换方式导出Excel
        HuToolExcelUtils.exportData(dataList, "提成明细", item -> new MiniAccountRoyaltyDetExcelVo());
    }

    @Override
    public PageUtils<MiniAccountCommissionManageVo> searchMiniAccountCommissionDet(MiniAccountCommissionManageParam param) {
        IPage<MiniAccountCommissionManageVo> page = this.baseMapper.searchMiniAccountCommissionDet(new Page<>(param.getCurrent(), param.getSize()), param);
        return new PageUtils<>(page);
    }

    @Override
    public void exportMiniAccountCommissionManage(MiniAccountCommissionManageParam param) {
        // 设置导出最大限制
        HuToolExcelUtils.exportParamToMax(param);
        PageUtils<MiniAccountCommissionManageVo> pageUtils = searchMiniAccountCommissionDet(param);
        List<MiniAccountCommissionManageVo> dataList = pageUtils.getList();
        List<String>stringList = new ArrayList<>();
        stringList.add("amount");
        // 使用Lambda转换方式导出Excel
        HuToolExcelUtils.exportData(dataList, "佣金明细", item -> new MiniAccountCommissionManageExcelVo(),stringList);
    }
    @Override
    @Transactional
    public void rollback(Long orderId) {
        LambdaQueryWrapper<MiniAccountCommission>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MiniAccountCommission::getOrderId,orderId);
        List<MiniAccountCommission> list = this.list(wrapper);
        if(list!=null&&list.size()>0){
            for (MiniAccountCommission miniAccountCommission : list) {
                MiniAccount account = miniAccountService.getByShopUserId(miniAccountCommission.getUserId());
                BigDecimal amount = miniAccountCommission.getAmount();
                BigDecimal commission = account.getCommission()==null?BigDecimal.ZERO:account.getCommission();
                BigDecimal currentCommission = account.getCurrentCommission()==null?BigDecimal.ZERO:account.getCurrentCommission();

                account.setCommission(commission.subtract(amount));
                account.setCurrentCommission(currentCommission.subtract(amount));
                LambdaUpdateWrapper<MiniAccount> userUpdateWrapper = new LambdaUpdateWrapper<>();
                userUpdateWrapper.set(MiniAccount::getCommission, account.getCommission());
                userUpdateWrapper.set(MiniAccount::getCurrentCommission, account.getCurrentCommission());
                userUpdateWrapper.eq(MiniAccount::getId, account.getId());
                this.miniAccountService.update(null, userUpdateWrapper);

                MiniAccountCommission accountCommission = new MiniAccountCommission();
                accountCommission.setLastCommission(currentCommission);
                accountCommission.setTotalCommission(account.getCurrentCommission());
                accountCommission.setAmount(amount.negate());
                accountCommission.setCommissionType(CommissionTypeEnum.DELETE_ORDER.getType());
                accountCommission.setUserId(miniAccountCommission.getUserId());
                accountCommission.setRemark("删除订单");
                accountCommission.setSource(CommonConstants.NUMBER_ONE);
                this.save(accountCommission);
            }
        }
    }

    @Override
    public PageUtils<RegionCommissionVo> getRegionCommission(RegionCommissionParam param) {
        CurUserDto curUser = CurUserUtil.getHttpCurUser();
        param.setShopUserId(curUser.getUserId());
        MemberType regionMemberType = memberTypeService.getRegionMemberType();
        if(regionMemberType!=null){
            param.setMemberTypeId(regionMemberType.getId());
        }else{
            throw new ServiceException("区域会员不存在！");
        }
        if(StringUtils.isNotEmpty(param.getStartTime())){
            param.setStartTime(param.getStartTime()+" 00:00:00");
        }
        if(StringUtils.isNotEmpty(param.getEndTime())){
            param.setEndTime(param.getEndTime()+" 23:59:59");
        }
        IPage<RegionCommissionVo>page = this.baseMapper.getRegionCommission(new Page<RegionCommissionVo>(param.getCurrent(),param.getSize()),param);
        return new PageUtils<>(page);
    }
}
