package com.medusa.gruul.account.model.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 16:10 2025/7/21
 */
@Data
@ApiModel(value = "用户金豆变更记录审核DTO")
public class MiniAccountGoldenChangeAuditDto {

    @ApiModelProperty(value = "id")
    @NotNull(message = "金豆变更记录id不能为空")
    private Long id;


    @ApiModelProperty(value = "审核状态:100->待审核，101->审核通过，200->驳回")
    @NotNull(message = "审核状态不能为空")
    private Integer auditStatus;

    @ApiModelProperty(value = "审核原因")
    private String auditReason;

}
