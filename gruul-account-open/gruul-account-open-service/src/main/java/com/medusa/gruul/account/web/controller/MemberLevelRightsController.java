package com.medusa.gruul.account.web.controller;



import com.medusa.gruul.account.model.vo.MemberLevelRightsVo;
import com.medusa.gruul.account.service.IMemberLevelRightsService;
import com.medusa.gruul.common.core.util.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 小程序用户表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2019-11-18
 */
@RestController
@RequestMapping("/member-level-rights")
@Api(tags = "会员等级权益相关接口")
public class MemberLevelRightsController {

    @Autowired
    private IMemberLevelRightsService memberLevelRightsService;


    @PostMapping("/getRightsList")
    @ApiOperation(value = "获取会员权益信息")
    public Result getRightsList() {
        //查询所有的权益信息
        List<MemberLevelRightsVo> rightsVoList= memberLevelRightsService.selectRightsVoAllList();
        return Result.ok(rightsVoList);
    }



}
