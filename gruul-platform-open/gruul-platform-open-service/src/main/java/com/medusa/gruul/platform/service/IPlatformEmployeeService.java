package com.medusa.gruul.platform.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.medusa.gruul.common.core.util.PageUtils;
import com.medusa.gruul.platform.api.entity.PlatformEmployee;
import com.medusa.gruul.platform.api.model.dto.OutPlatformEmployeeDto;
import com.medusa.gruul.platform.api.model.dto.PlatformEmployeeDto;
import com.medusa.gruul.platform.api.model.vo.DepartmentVo;
import com.medusa.gruul.platform.api.model.vo.EmployeeVo;
import com.medusa.gruul.platform.model.dto.BindStoreFrontDto;
import com.medusa.gruul.platform.model.dto.BindingWarehouseDto;
import com.medusa.gruul.platform.model.dto.ConsoleUpdateDto;
import com.medusa.gruul.platform.model.dto.EmployeeScheduleDto;
import com.medusa.gruul.platform.model.param.DepartmentParam;
import com.medusa.gruul.platform.model.param.EmployeeParam;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 17:42 2024/9/12
 */
public interface IPlatformEmployeeService extends IService<PlatformEmployee> {

    /**
     * 接收外部系统职员信息
     * @param outPlatformEmployeeDto
     * @return
     */
    PlatformEmployee newAdd(OutPlatformEmployeeDto outPlatformEmployeeDto);

    /**
     * 新增职员
     * @param platformEmployeeDto
     * @return
     */
    void addEmployee(PlatformEmployeeDto platformEmployeeDto);

    /**
     * 修改职员
     * @param platformEmployeeDto
     * @return
     */
    void updateEmployee(PlatformEmployeeDto platformEmployeeDto);

    /**
     * 删除职员
     * @param platformEmployeeDto
     */
    void deleteEmployee(PlatformEmployeeDto platformEmployeeDto);

    /**
     * 获取职员编号
     * @return
     */
    String getEmpNumber();


    /**
     * 分页查询平台职员列表
     * @param employeeParam
     * @return
     */
    PageUtils<EmployeeVo> searchEmployee(EmployeeParam employeeParam);

    /**
     * 分页查询可绑定平台职员
     * @param employeeParam
     * @return
     */
    PageUtils<EmployeeVo> searchBindEmployee(EmployeeParam employeeParam);
    /**
     * 职员绑定仓库
     * @param dto
     */
    void bindingWarehouse(BindingWarehouseDto dto);

    /**
     * 职员绑定门店
     * @param dto
     */
    void bindingStoreFront(BindStoreFrontDto dto);

    boolean editWorkStatus(PlatformEmployeeDto dto);

    boolean batchEditWorkStatus(EmployeeScheduleDto employeeScheduleDto);

}
