package com.medusa.gruul.platform.model.dto;


import com.baomidou.mybatisplus.annotation.TableField;
import com.medusa.gruul.common.core.param.QueryParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 平台用户查询条件信息
 */
@Data
public class PlatformAccountInfoParamDto extends QueryParam {


    @ApiModelProperty(value = "用户名称")
    private String nikeName;

    @NotBlank(message = "手机号不能为空")
    private String phone;

    /**
     * 账号状态  0-正常  1-禁用
     */
    @ApiModelProperty(value = "账号状态")
    private Integer forbidStatus;

    @ApiModelProperty(value = "账号类型")
    private Integer accountType;

    @ApiModelProperty(value = "部门名称")
    private String departmentName;

    @ApiModelProperty(value = "职员名称")
    private String employeeName;

    @ApiModelProperty(value = "仓库名称")
    private String stockName;

    @ApiModelProperty(value = "门店名称")
    private String storeFrontName;
}
