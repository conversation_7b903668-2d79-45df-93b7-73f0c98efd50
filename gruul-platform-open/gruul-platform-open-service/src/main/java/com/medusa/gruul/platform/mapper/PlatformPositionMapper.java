package com.medusa.gruul.platform.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.medusa.gruul.platform.api.entity.PlatformPosition;
import com.medusa.gruul.platform.api.model.vo.PlatformPositionVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 商家职位表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
@Mapper
public interface PlatformPositionMapper extends BaseMapper<PlatformPosition> {
    List<PlatformPositionVo> pagePositionWithEmployee(Page<PlatformPositionVo> page, @Param("employeeId") Long employeeId);
}