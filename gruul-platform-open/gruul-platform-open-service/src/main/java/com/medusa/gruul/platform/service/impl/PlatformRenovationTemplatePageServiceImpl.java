package com.medusa.gruul.platform.service.impl;

import com.medusa.gruul.platform.api.entity.PlatformRenovationTemplatePage;
import com.medusa.gruul.platform.mapper.PlatformRenovationTemplatePageMapper;
import com.medusa.gruul.platform.service.IPlatformRenovationTemplatePageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 平台装修模板页面表
 * @Author: jeecg-boot
 * @Date:   2023-09-06
 * @Version: V1.0
 */
@Service
@Slf4j
public class PlatformRenovationTemplatePageServiceImpl extends ServiceImpl<PlatformRenovationTemplatePageMapper, PlatformRenovationTemplatePage> implements IPlatformRenovationTemplatePageService {

}
