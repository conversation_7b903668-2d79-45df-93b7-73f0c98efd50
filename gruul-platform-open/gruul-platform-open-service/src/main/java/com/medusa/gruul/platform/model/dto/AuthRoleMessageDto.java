package com.medusa.gruul.platform.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 16:08 2025/5/27
 */
@Data
@ApiModel(value="AuthRoleMessageDto对象", description="授权角色信息dto")
public class AuthRoleMessageDto {

    /**
     * 角色id
     */
    @ApiModelProperty(value = "角色id")
    private Long roleId;

    /**
     * 消息类型
     */
    @ApiModelProperty(value = "消息类型")
    private List<Integer>messageTypes;

}
