package com.medusa.gruul.account.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: rbw
 * @Description: 查询用户是否设置支付密码
 */
@Data
@ApiModel(value = "查询用户是否设置支付密码")
public class CheckAccountPayPwdDto {



    @ApiModelProperty("是否代客户下单：0.否；1.是")
    private Integer replaceCreateOrderFlag;

    @ApiModelProperty("用户id")
    private String userId;


}
