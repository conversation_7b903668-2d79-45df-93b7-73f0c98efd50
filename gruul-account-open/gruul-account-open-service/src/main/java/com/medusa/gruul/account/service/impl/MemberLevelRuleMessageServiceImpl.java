package com.medusa.gruul.account.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.medusa.gruul.account.api.entity.*;
import com.medusa.gruul.account.api.model.AccountInfoDto;
import com.medusa.gruul.account.mapper.*;
import com.medusa.gruul.account.model.dto.MemberLevelRuleDto;
import com.medusa.gruul.account.model.dto.MemberLevelRuleMessageDto;
import com.medusa.gruul.account.model.dto.MemberLevelRuleProductDto;
import com.medusa.gruul.account.model.param.MemberLevelRuleMessageParam;
import com.medusa.gruul.account.model.vo.*;
import com.medusa.gruul.account.service.IMemberLevelRuleMessageService;
import com.medusa.gruul.account.service.IMemberLevelRuleService;
import com.medusa.gruul.account.service.IMiniAccountService;
import com.medusa.gruul.common.core.constant.CommonConstants;
import com.medusa.gruul.common.core.constant.enums.MemberLevelRuleTypeEnum;
import com.medusa.gruul.common.core.constant.enums.MemberTypeStatusEnum;
import com.medusa.gruul.common.core.exception.ServiceException;
import com.medusa.gruul.common.core.util.CurUserUtil;
import com.medusa.gruul.common.dto.CurUserDto;
import com.medusa.gruul.goods.api.entity.MemberLevelGoodsAgainPrice;
import com.medusa.gruul.goods.api.entity.MemberLevelGoodsPrice;
import com.medusa.gruul.goods.api.entity.SkuStock;
import com.medusa.gruul.goods.api.feign.RemoteGoodsService;
import com.medusa.gruul.goods.api.model.vo.manager.ProductShowCategoryVo;
import com.medusa.gruul.goods.api.model.vo.manager.ProductVo;
import com.medusa.gruul.order.api.enums.MemberPriceTypeEnum;
import com.medusa.gruul.order.api.feign.RemoteOrderService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 9:12 2025/3/18
 */
@Service
public class MemberLevelRuleMessageServiceImpl extends ServiceImpl<MemberLevelRuleMessageMapper,MemberLevelRuleMessage>
        implements IMemberLevelRuleMessageService {

    @Autowired
    private MemberLevelRuleMapper memberLevelRuleMapper;

    @Autowired
    private MemberLevelRuleProductMapper memberLevelRuleProductMapper;

    @Autowired
    private MemberTypeMapper memberTypeMapper;
    @Autowired
    private RemoteGoodsService remoteGoodsService;
    @Autowired
    private IMiniAccountService miniAccountService;
    @Autowired
    private RemoteOrderService remoteOrderService;
    @Autowired
    private MemberLevelMapper memberLevelMapper;
    @Autowired
    private MemberLevelRelationMapper memberLevelRelationMapper;
    @Autowired
    private IMemberLevelRuleService memberLevelRuleService;

    /**
     * 添加或者编辑会员等级规则信息
     * @param memberLevelRuleMessageDto
     * @return
     */
    @Override
    @Transactional
    public String addOrUpdateMemberLevelRuleMessage(MemberLevelRuleMessageDto memberLevelRuleMessageDto) {
        String result = "";
        String id = memberLevelRuleMessageDto.getId();
        CurUserDto curUserDto = CurUserUtil.getHttpCurUser();
        String userId = curUserDto.getUserId();
        String nikeName = curUserDto.getNikeName();
        Long memberTypeId = memberLevelRuleMessageDto.getMemberTypeId();
        if(memberTypeId == null || memberTypeId.equals("")){
            throw new ServiceException("会员等级规则-会员类型id不能为空！");
        }
        if(StringUtils.isEmpty(id)){
            //新增
            MemberLevelRuleMessage memberLevelRuleMessage = new MemberLevelRuleMessage();
            BeanUtils.copyProperties(memberLevelRuleMessageDto,memberLevelRuleMessage);
            memberLevelRuleMessage.setCreateUserName(nikeName);
            memberLevelRuleMessage.setCreateUserId(Long.valueOf(userId));
            this.save(memberLevelRuleMessage);
            Long mainId = memberLevelRuleMessage.getId();
            List<MemberLevelRuleDto> rules = memberLevelRuleMessageDto.getRules();
            if(rules!=null&&rules.size()>0){
                for (MemberLevelRuleDto memberLevelRuleDto : rules) {
                    MemberLevelRule memberLevelRule = new MemberLevelRule();
                    BeanUtils.copyProperties(memberLevelRuleDto,memberLevelRule);
                    memberLevelRule.setId(null);
                    memberLevelRule.setCreateUserName(nikeName);
                    memberLevelRule.setCreateUserId(Long.valueOf(userId));
                    memberLevelRule.setMainId(String.valueOf(mainId));
                    memberLevelRuleMapper.insert(memberLevelRule);
                }
            }
            List<MemberLevelRuleProductDto> products = memberLevelRuleMessageDto.getProducts();
            if(products!=null&&products.size()>0){
                for (MemberLevelRuleProductDto memberLevelRuleProductDto : products) {
                    Long productMemberTypeId = memberLevelRuleProductDto.getMemberTypeId();
                    if(productMemberTypeId == null || productMemberTypeId.equals("")){
                        throw new ServiceException("会员等级规则商品-会员类型id不能为空！");
                    }
                    MemberLevelRuleProduct memberLevelRuleProduct = new MemberLevelRuleProduct();
                    BeanUtils.copyProperties(memberLevelRuleProductDto,memberLevelRuleProduct);
                    memberLevelRuleProduct.setId(null);
                    memberLevelRuleProduct.setCreateUserName(nikeName);
                    memberLevelRuleProduct.setCreateUserId(Long.valueOf(userId));
                    memberLevelRuleProduct.setMainId(String.valueOf(mainId));
                    memberLevelRuleProductMapper.insert(memberLevelRuleProduct);
                }
            }
            result = "新增成功";
        }else{

            MemberLevelRuleMessage memberLevelRuleMessage = this.getById(id);

            if(memberLevelRuleMessage == null){
                throw new ServiceException("会员等级规则不存在");
            }

            //编辑
            LambdaQueryWrapper<MemberLevelRule>ruleWrapper = new LambdaQueryWrapper<>();
            ruleWrapper.eq(MemberLevelRule::getMainId,id);
            memberLevelRuleMapper.delete(ruleWrapper);
            LambdaQueryWrapper<MemberLevelRuleProduct>productWrapper = new LambdaQueryWrapper<>();
            productWrapper.eq(MemberLevelRuleProduct::getMainId,id);
            memberLevelRuleProductMapper.delete(productWrapper);

            BeanUtils.copyProperties(memberLevelRuleMessageDto,memberLevelRuleMessage);
            memberLevelRuleMessage.setLastModifyUserName(nikeName);
            memberLevelRuleMessage.setLastModifyUserId(Long.valueOf(userId));
            this.updateById(memberLevelRuleMessage);
            Long mainId = memberLevelRuleMessage.getId();
            List<MemberLevelRuleDto> rules = memberLevelRuleMessageDto.getRules();
            if(rules!=null&&rules.size()>0){
                for (MemberLevelRuleDto memberLevelRuleDto : rules) {
                    MemberLevelRule memberLevelRule = new MemberLevelRule();
                    BeanUtils.copyProperties(memberLevelRuleDto,memberLevelRule);
                    memberLevelRule.setId(null);
                    memberLevelRule.setCreateUserName(nikeName);
                    memberLevelRule.setCreateUserId(Long.valueOf(userId));
                    memberLevelRule.setMainId(String.valueOf(mainId));
                    memberLevelRuleMapper.insert(memberLevelRule);
                }
            }
            List<MemberLevelRuleProductDto> products = memberLevelRuleMessageDto.getProducts();
            if(products!=null&&products.size()>0){
                for (MemberLevelRuleProductDto memberLevelRuleProductDto : products) {
                    Long productMemberTypeId = memberLevelRuleProductDto.getMemberTypeId();
                    if(productMemberTypeId == null || productMemberTypeId.equals("")){
                        throw new ServiceException("会员等级规则商品-会员类型id不能为空！");
                    }
                    MemberLevelRuleProduct memberLevelRuleProduct = new MemberLevelRuleProduct();
                    BeanUtils.copyProperties(memberLevelRuleProductDto,memberLevelRuleProduct);
                    memberLevelRuleProduct.setId(null);
                    memberLevelRuleProduct.setCreateUserName(nikeName);
                    memberLevelRuleProduct.setCreateUserId(Long.valueOf(userId));
                    memberLevelRuleProduct.setMainId(String.valueOf(mainId));
                    memberLevelRuleProductMapper.insert(memberLevelRuleProduct);
                }
            }
            result = "编辑成功";
        }
        return result;
    }

    /**
     * 获取会员等级规则信息
     * @param param
     * @return
     */
    @Override
    public MemberLevelRuleMessageVo getMemberLevelRuleMessage(MemberLevelRuleMessageParam param) {


        MemberLevelRuleMessageVo memberLevelRuleMessageVo = new MemberLevelRuleMessageVo();
        LambdaQueryWrapper<MemberLevelRuleMessage>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MemberLevelRuleMessage::getDeleted, CommonConstants.NUMBER_ZERO);
        Long memberTypeId = param.getMemberTypeId();
        wrapper.eq(null!= memberTypeId, MemberLevelRuleMessage::getMemberTypeId,memberTypeId);
        List<MemberLevelRuleMessage> memberLevelRuleMessageList = this.list(wrapper);

        if(CollectionUtil.isNotEmpty(memberLevelRuleMessageList) && memberLevelRuleMessageList.size() > 1){
            return memberLevelRuleMessageVo;
        }
        MemberLevelRuleMessage memberLevelRuleMessage = memberLevelRuleMessageList.get(0);

        if(memberLevelRuleMessage!=null){
            memberLevelRuleMessageVo.setId(memberLevelRuleMessage.getId());
            memberLevelRuleMessageVo.setDescription(memberLevelRuleMessage.getDescription());
            memberLevelRuleMessageVo.setType(memberLevelRuleMessage.getType());
            memberLevelRuleMessageVo.setMemberTypeId(memberLevelRuleMessage.getMemberTypeId());
            memberLevelRuleMessageVo.setOrderMemberTypeIds(memberLevelRuleMessage.getOrderMemberTypeIds());
        }

        List<MemberLevelRuleVo> rules = memberLevelRuleMapper.getMemberLevelRule(memberTypeId);
        memberLevelRuleMessageVo.setRules(rules);
        List<MemberLevelRuleProductVo>products = memberLevelRuleProductMapper.getMemberLevelRuleProduct(memberTypeId);
        memberLevelRuleMessageVo.setProducts(products);
        return memberLevelRuleMessageVo;
    }

    @Override
    public List<MemberLevelRuleMessageVo> getMemberLevelRuleMessageList() {

        LambdaQueryWrapper<MemberLevelRuleMessage>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MemberLevelRuleMessage::getDeleted, CommonConstants.NUMBER_ZERO);
        List<MemberLevelRuleMessage> list = this.list(wrapper);
        List<MemberLevelRuleMessageVo> memberLevelRuleMessageList = new ArrayList<>();
        if(list!=null&&list.size()>0){
            for (MemberLevelRuleMessage memberLevelRuleMessage : list) {
                MemberLevelRuleMessageVo memberLevelRuleMessageVo = new MemberLevelRuleMessageVo();
                memberLevelRuleMessageVo.setId(memberLevelRuleMessage.getId());
                memberLevelRuleMessageVo.setDescription(memberLevelRuleMessage.getDescription());
                memberLevelRuleMessageVo.setType(memberLevelRuleMessage.getType());
                memberLevelRuleMessageVo.setMemberTypeId(memberLevelRuleMessage.getMemberTypeId());
                memberLevelRuleMessageVo.setOrderMemberTypeIds(memberLevelRuleMessage.getOrderMemberTypeIds());
                List<MemberLevelRuleVo> rules = memberLevelRuleMapper.getMemberLevelRule(memberLevelRuleMessage.getMemberTypeId());
                memberLevelRuleMessageVo.setRules(rules);
                List<MemberLevelRuleProductVo>products = memberLevelRuleProductMapper.getMemberLevelRuleProduct(memberLevelRuleMessage.getMemberTypeId());
                memberLevelRuleMessageVo.setProducts(products);
                memberLevelRuleMessageList.add(memberLevelRuleMessageVo);
            }
        }
        return memberLevelRuleMessageList;
    }

    @Override
    public Integer getHomeFlagByMemberTypeId(Long memberTypeId) {

        Integer homeFlag = 0;

        if(memberTypeId == null){
            throw new ServiceException("会员类型id不能为空，无法升级！");
        }

        MemberType memberType = memberTypeMapper.selectById(memberTypeId);
        if(memberType == null){
            throw new ServiceException("会员类型不存在，无法升级！");
        }

        if(memberType.getStatus() == MemberTypeStatusEnum.NO.getStatus()){
            throw new ServiceException("该会员升级已停用，无法升级！");
        }

        LambdaQueryWrapper<MemberLevelRuleMessage>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MemberLevelRuleMessage::getMemberTypeId,memberTypeId);
        List<MemberLevelRuleMessage> list = this.list(wrapper);
        if(list!=null&&list.size()>0){
            if(list.size()>1){
                throw new ServiceException("会员等级规则存在多条，获取数据失败！");
            }else{
                MemberLevelRuleMessage memberLevelRuleMessage = list.get(0);
                LambdaQueryWrapper<MemberLevelRuleProduct>queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(MemberLevelRuleProduct::getMainId,memberLevelRuleMessage.getId());
                int count = memberLevelRuleProductMapper.selectCount(queryWrapper);
                if(count>0){
                    homeFlag = 0;
                }else{
                    homeFlag = 1;
                }
            }
        }else{
            homeFlag = 1;
        }


        return homeFlag;
    }

    @Override
    public List<UpgradeMemberDataVo> getUpgradeMemberData(Long memberTypeId) {

        CurUserDto curUserDto = CurUserUtil.getHttpCurUser();

        if(memberTypeId == null){
            throw new ServiceException("会员类型id不能为空，获取数据失败！");
        }

        MemberType memberType = memberTypeMapper.selectById(memberTypeId);
        if(memberType == null){
            throw new ServiceException("会员类型不存在，获取数据失败！");
        }

        if(memberType.getStatus() == MemberTypeStatusEnum.NO.getStatus()){
            throw new ServiceException("该会员升级已停用，获取数据失败！");
        }

        List<UpgradeMemberDataVo>upgradeMemberDataList = new ArrayList<>();
        MiniAccount miniAccount = miniAccountService.getByShopUserId(curUserDto.getUserId());


        //根据会员的id来获取对应的全部会员商品价格
        List<MemberLevelGoodsPrice> memberLevelGoodsPriceList = remoteGoodsService.selectMemberGoodsPrice(miniAccount.getMemberLevelId());
        List<MemberLevelGoodsAgainPrice> memberLevelGoodsAgainPriceList = remoteGoodsService.selectMemberGoodsAgainPrice(miniAccount.getMemberLevelId());

        LambdaQueryWrapper<MemberLevelRuleMessage>memberLevelRuleMessageWrapper = new LambdaQueryWrapper<>();
        memberLevelRuleMessageWrapper.eq(MemberLevelRuleMessage::getMemberTypeId,memberTypeId);

        //复购价
        Integer count = remoteOrderService.getCountByMemberType(curUserDto.getUserId(),memberTypeId);


        LambdaQueryWrapper<MemberLevelRelation>memberLevelRelationWrapper = new LambdaQueryWrapper<>();
        memberLevelRelationWrapper.eq(MemberLevelRelation::getUserId,miniAccount.getUserId());
        memberLevelRelationWrapper.eq(MemberLevelRelation::getMemberTypeId,memberTypeId);


        MemberLevelRelation memberLevelRelation = memberLevelRelationMapper.selectOne(memberLevelRelationWrapper);
        MemberLevelRuleVo memberLevelRuleVo = null;
        if(memberLevelRelation!=null){
            memberLevelRuleVo = memberLevelRuleService.getByMemberLevelId(Long.valueOf(memberLevelRelation.getMemberLevelId()));
        }

        List<MemberLevelRuleMessage> list = this.list(memberLevelRuleMessageWrapper);
        if(list!=null&&list.size()>0){
            if(list.size()>1){
                throw new ServiceException("会员等级规则存在多条，获取数据失败！");
            }else{
                MemberLevelRuleMessage memberLevelRuleMessage = list.get(0);

                LambdaQueryWrapper<MemberLevelRuleProduct>memberLevelRuleProductWrapper = new LambdaQueryWrapper<>();
                memberLevelRuleProductWrapper.eq(MemberLevelRuleProduct::getMainId,memberLevelRuleMessage.getId());

                List<MemberLevelRuleProduct> memberLevelRuleProductList = memberLevelRuleProductMapper.selectList(memberLevelRuleProductWrapper);




                if(memberLevelRuleProductList!=null&&memberLevelRuleProductList.size()>0){
                    //如果包含指定商品数升级
                    if(memberLevelRuleMessage.getType().contains(MemberLevelRuleTypeEnum.APPOINT_GOODS.getStatus())){
                        LambdaQueryWrapper<MemberLevelRule>memberLevelRuleWrapper = new LambdaQueryWrapper<>();
                        memberLevelRuleWrapper.eq(MemberLevelRule::getMainId,memberLevelRuleMessage.getId());
                        memberLevelRuleWrapper.gt(MemberLevelRule::getProductQty,BigDecimal.ZERO);
                        memberLevelRuleWrapper.gt(MemberLevelRule::getProductAmount,BigDecimal.ZERO);
                        List<MemberLevelRule> memberLevelRuleList = memberLevelRuleMapper.selectList(memberLevelRuleWrapper);
                        if(memberLevelRuleList!=null&&memberLevelRuleList.size()>0){
                            for (MemberLevelRule memberLevelRule : memberLevelRuleList) {
                                UpgradeMemberDataVo upgradeMemberDataVo = new UpgradeMemberDataVo();
                                upgradeMemberDataVo.setUpdateFlag(CommonConstants.NUMBER_ONE);
                                upgradeMemberDataVo.setBuyFlag(CommonConstants.NUMBER_ONE);
                                upgradeMemberDataVo.setUpdateOrder(CommonConstants.NUMBER_ONE);
                                if(memberLevelRelation!=null
                                        &&StringUtils.isNotEmpty(memberLevelRelation.getMemberLevelId())){
                                    if(memberLevelRuleVo.getSort() >= memberLevelRule.getSort()){
                                        upgradeMemberDataVo.setBuyFlag(CommonConstants.NUMBER_ZERO);
                                    }
                                }
                                MemberLevel memberLevel = memberLevelMapper.selectById(memberLevelRule.getMemberLevelId());
                                upgradeMemberDataVo.setMemberLevelName(memberLevel.getMemberLevel());
                                upgradeMemberDataVo.setMemberTypeId(memberTypeId);
                                upgradeMemberDataVo.setMemberLevelId(Long.valueOf(memberLevel.getId()));
                                List<UpgradeMemberProductVo>upgradeMemberProductList = new ArrayList<>();
                                for (MemberLevelRuleProduct memberLevelRuleProduct : memberLevelRuleProductList) {
                                    UpgradeMemberProductVo upgradeMemberProductVo = new UpgradeMemberProductVo();
                                    String productId = memberLevelRuleProduct.getProductId();
                                    String skuId = memberLevelRuleProduct.getSkuId();
                                    ProductVo product = remoteGoodsService.findProductById(Long.valueOf(productId));
                                    SkuStock skuStock = remoteGoodsService.findSkuStockById(Long.valueOf(skuId));
                                    upgradeMemberProductVo.setProductId(Long.valueOf(productId));
                                    upgradeMemberProductVo.setSkuId(Long.valueOf(skuId));
                                    upgradeMemberProductVo.setProductName(product.getName());
                                    upgradeMemberProductVo.setSpecs(skuStock.getSpecs());
                                    upgradeMemberProductVo.setCategoryName(product.getCategoryName());
                                    upgradeMemberProductVo.setPic(skuStock.getPic());

                                    if(memberLevelRule.getProductQty()<=0){
                                        throw new ServiceException("指定商品消费额指标数量必须大于0,获取数据失败！");
                                    }
                                    if(memberLevelRule.getProductAmount().compareTo(BigDecimal.ZERO)<=0){
                                        throw new ServiceException("指定商品消费额指标金额必须大于0,获取数据失败！");
                                    }
                                    upgradeMemberProductVo.setQty(memberLevelRule.getProductQty());
                                    upgradeMemberProductVo.setAmount(memberLevelRule.getProductAmount());
                                    upgradeMemberProductVo.setPrice(memberLevelRule.getProductAmount().divide(new BigDecimal(memberLevelRule.getProductQty()),2,BigDecimal.ROUND_HALF_UP));

                                    upgradeMemberProductVo.setOriginalPrice(skuStock.getOriginalPrice());
                                    upgradeMemberProductVo.setOriginalAmount(skuStock.getOriginalPrice().multiply(new BigDecimal(memberLevelRule.getProductQty())));
                                    upgradeMemberProductList.add(upgradeMemberProductVo);
                                }
                                upgradeMemberDataVo.setUpgradeMemberProductList(upgradeMemberProductList);
                                upgradeMemberDataList.add(upgradeMemberDataVo);
                            }
                        }
                    }else{
                        UpgradeMemberDataVo upgradeMemberDataVo = new UpgradeMemberDataVo();
                        upgradeMemberDataVo.setMemberTypeId(memberTypeId);
                        upgradeMemberDataVo.setUpdateFlag(CommonConstants.NUMBER_ZERO);
                        upgradeMemberDataVo.setBuyFlag(CommonConstants.NUMBER_ONE);
                        upgradeMemberDataVo.setUpdateOrder(CommonConstants.NUMBER_ONE);
                        List<UpgradeMemberProductVo>upgradeMemberProductList = new ArrayList<>();
                        for (MemberLevelRuleProduct memberLevelRuleProduct : memberLevelRuleProductList) {
                            UpgradeMemberProductVo upgradeMemberProductVo = new UpgradeMemberProductVo();
                            String productId = memberLevelRuleProduct.getProductId();
                            String skuId = memberLevelRuleProduct.getSkuId();
                            ProductVo product = remoteGoodsService.findProductById(Long.valueOf(productId));
                            SkuStock skuStock = remoteGoodsService.findSkuStockById(Long.valueOf(skuId));
                            upgradeMemberProductVo.setProductId(Long.valueOf(productId));
                            upgradeMemberProductVo.setSkuId(Long.valueOf(skuId));
                            upgradeMemberProductVo.setProductName(product.getName());
                            upgradeMemberProductVo.setSpecs(skuStock.getSpecs());
                            upgradeMemberProductVo.setCategoryName(product.getCategoryName());
                            upgradeMemberProductVo.setPic(skuStock.getPic());
                            upgradeMemberProductVo.setPrice(skuStock.getPrice());
                            upgradeMemberProductVo.setOriginalPrice(skuStock.getOriginalPrice());
                            upgradeMemberProductVo.setQty(CommonConstants.NUMBER_ZERO);
                            upgradeMemberProductVo.setAmount(BigDecimal.ZERO);
                            upgradeMemberProductVo.setOriginalAmount(BigDecimal.ZERO);

                            //会员价
                            if(CollUtil.isNotEmpty(memberLevelGoodsPriceList)){
                                //获取商品规格会员价
                                List<MemberLevelGoodsPrice> memberLevelGoodsPrices = memberLevelGoodsPriceList.stream().filter(memberLevelGoodsPrice->  memberLevelGoodsPrice.getSkuId().equals(Long.valueOf(skuId))).collect(Collectors.toList());
                                //将商品的价格替换成会员价
                                if(CollectionUtil.isNotEmpty(memberLevelGoodsPrices)){
                                    if (memberLevelGoodsPrices.get(0).getMemberLevelPrice()!=null){
                                        if(MemberPriceTypeEnum.MEMBER_PRICE_FIXED_AMOUNT.getStatus()==product.getMemberPriceType()){
                                            upgradeMemberProductVo.setPrice(memberLevelGoodsPrices.get(0).getMemberLevelPrice());
                                        }else if(MemberPriceTypeEnum.MEMBER_PRICE_PERCENTAGE.getStatus()==product.getMemberPriceType()){
                                            upgradeMemberProductVo.setPrice(skuStock.getPrice().multiply(memberLevelGoodsPrices.get(0).getMemberLevelPrice()).divide(new BigDecimal(100),4, BigDecimal.ROUND_HALF_UP));
                                        }
                                    }
                                }
                            }
                            if(count>0&&CollUtil.isNotEmpty(memberLevelGoodsAgainPriceList)){
                                List<MemberLevelGoodsAgainPrice> memberLevelGoodsAgainPrices = memberLevelGoodsAgainPriceList.stream().filter(memberLevelGoodsAgainPrice->  memberLevelGoodsAgainPrice.getSkuId().equals(Long.valueOf(skuId))).collect(Collectors.toList());
                                //将商品的价格替换成复购价
                                if(CollectionUtil.isNotEmpty(memberLevelGoodsAgainPrices)){
                                    if (memberLevelGoodsAgainPrices.get(0).getMemberLevelAgainPrice()!=null){
                                        if(MemberPriceTypeEnum.MEMBER_PRICE_FIXED_AMOUNT.getStatus()==product.getMemberAgainPriceType()){
                                            upgradeMemberProductVo.setPrice(memberLevelGoodsAgainPrices.get(0).getMemberLevelAgainPrice());
                                        }else if(MemberPriceTypeEnum.MEMBER_PRICE_PERCENTAGE.getStatus()==product.getMemberAgainPriceType()){
                                            upgradeMemberProductVo.setPrice(skuStock.getPrice().multiply(memberLevelGoodsAgainPrices.get(0).getMemberLevelAgainPrice()).divide(new BigDecimal(100),4, BigDecimal.ROUND_HALF_UP));
                                        }
                                    }
                                }
                            }
                            upgradeMemberProductList.add(upgradeMemberProductVo);
                        }
                        upgradeMemberDataVo.setUpgradeMemberProductList(upgradeMemberProductList);
                        upgradeMemberDataList.add(upgradeMemberDataVo);
                    }
                }else{
                    throw new ServiceException("指定升级商品不存在，获取数据失败！");
                }
            }
        }else{
            throw new ServiceException("会员等级规则不存在，获取数据失败！");
        }

        return upgradeMemberDataList;
    }
}
