package com.medusa.gruul.account.model.param;

import com.medusa.gruul.common.core.param.QueryParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 14:22 2025/3/14
 */
@ApiModel(value = "MiniAccountRoyaltyParam 实体", description = "提成汇总查询 param")
@Data
public class MiniAccountRoyaltyParam extends QueryParam {

    @ApiModelProperty(value = "会员昵称")
    private String nikeName;

    @ApiModelProperty(value = "会员号码")
    private String phone;

    @ApiModelProperty(value = "提成为零不显示")
    private Boolean rewardRoyaltyFlag;

}
