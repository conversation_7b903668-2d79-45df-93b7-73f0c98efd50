package com.medusa.gruul.platform.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.medusa.gruul.platform.api.entity.PlatformEmployeePosition;
import com.medusa.gruul.platform.api.entity.PlatformPosition;

import java.util.List;

/**
 * <p>
 * 职员职位关联表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
public interface IPlatformEmployeePositionService extends IService<PlatformEmployeePosition> {

    /**
     * 批量保存职员职位关联
     *
     * @param employeeId 职员ID
     * @param positionIds 职位ID列表
     * @return 是否成功
     */
    boolean saveEmployeePositions(Long employeeId, List<Long> positionIds);

    /**
     * 根据职员ID获取职位ID列表
     *
     * @param employeeId 职员ID
     * @return 职位ID列表
     */
    List<Long> getPositionIdsByEmployeeId(Long employeeId);

    /**
     * 根据职位ID获取职员ID列表
     *
     * @param positionId 职位ID
     * @return 职员ID列表
     */
    List<Long> getEmployeeIdsByPositionId(Long positionId);

    /**
     * 检查职位是否被使用
     *
     * @param positionId 职位ID
     * @return 是否被使用
     */
    boolean isPositionUsed(Long positionId);
} 