package com.medusa.gruul.account.model.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "新增或修改用户优惠券验证码DTO")
public class MiniAccountCouponCodeDto {

    /**
     * 用户优惠券ID
     */
    @ApiModelProperty(value = "用户优惠券ID")
    private Long miniAccountCouponId;


}
