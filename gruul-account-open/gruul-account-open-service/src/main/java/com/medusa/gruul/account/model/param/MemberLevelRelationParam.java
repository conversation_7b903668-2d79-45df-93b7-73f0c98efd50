package com.medusa.gruul.account.model.param;

import com.baomidou.mybatisplus.annotation.TableField;
import com.medusa.gruul.common.core.param.QueryParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 10:33 2025/6/20
 */
@Data
@ApiModel(value = "MemberLevelRelationParam 实体", description = "会员等级，类型记录 param")
public class MemberLevelRelationParam extends QueryParam {

    /**
     * 会员user_id
     */
    @ApiModelProperty(value = "会员user_id")
    private String userId;

    /**
     * 会员类型ID
     */
    @ApiModelProperty(value = "会员类型ID")
    private Long memberTypeId;

    /**
     * 会员等级ID
     */
    @ApiModelProperty(value = "会员等级ID")
    private String memberLevelId;
}
