package com.medusa.gruul.account.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户通行票验证码DTO
 * <AUTHOR>
 */
@Data
@ApiModel(value = "新增或修改用户通行票验证码DTO")
public class MiniAccountPassTicketCodeDto {
    /*@ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "用户id")
    private String userId;*/

    @ApiModelProperty(value = "用户通行票id")
    private Long miniAccountPassTicketId;

/*    @ApiModelProperty(value = "店铺id")
    private Long shopsPartnerId;

    @ApiModelProperty(value = "验证码")
    private String verifyCode;

    @ApiModelProperty(value = "状态:100->未用;101->已用;200->已失效")
    private Integer status;

    @ApiModelProperty(value = "开始时间")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "结束时间")
    private LocalDateTime endTime;*/

}
