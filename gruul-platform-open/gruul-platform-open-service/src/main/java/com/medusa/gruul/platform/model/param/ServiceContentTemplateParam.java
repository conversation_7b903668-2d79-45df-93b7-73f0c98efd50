package com.medusa.gruul.platform.model.param;

import com.medusa.gruul.common.core.param.QueryParam;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 服务项目模板查询DTO
 */
@Data
@ApiModel(value = "服务项目模板查询DTO", description = "服务项目模板查询DTO")
public class ServiceContentTemplateParam extends QueryParam{

    /**
     * 服务项目名称
     */
    @ApiModelProperty(value = "服务项目名称")
    private String serviceName;

    /**
     * 状态(0-停用, 1-启用)
     */
    @ApiModelProperty(value = "状态(0-停用, 1-启用)")
    private Integer status;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    
} 