package com.medusa.gruul.platform.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.medusa.gruul.account.api.entity.MiniAccount;
import com.medusa.gruul.account.api.feign.RemoteMiniAccountService;
import com.medusa.gruul.account.api.model.AccountInfoDto;
import com.medusa.gruul.common.core.exception.ServiceException;
import com.medusa.gruul.common.core.util.CurUserUtil;
import com.medusa.gruul.common.core.util.SystemCode;
import com.medusa.gruul.common.dto.CurMiniUserInfoDto;
import com.medusa.gruul.common.dto.CurUserDto;
import com.medusa.gruul.platform.api.entity.ReservationOrder;
import com.medusa.gruul.platform.api.entity.ReservationOrderEvaluate;
import com.medusa.gruul.platform.api.enums.ReservationEnums;
import com.medusa.gruul.platform.mapper.ReservationOrderEvaluateMapper;
import com.medusa.gruul.platform.mapper.ReservationOrderMapper;
import com.medusa.gruul.platform.model.dto.ReservationOrderEvaluateDto;
import com.medusa.gruul.platform.model.param.ReservationOrderEvaluateQueryParam;
import com.medusa.gruul.platform.model.vo.ReservationOrderEvaluateVo;
import com.medusa.gruul.platform.service.IReservationOrderEvaluateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Arrays;

/**
 * 预约单评价表 服务实现类
 *
 * <AUTHOR>
 * @Date: 2025-06-11
 */
@Slf4j
@Service
public class ReservationOrderEvaluateServiceImpl extends ServiceImpl<ReservationOrderEvaluateMapper, ReservationOrderEvaluate> implements IReservationOrderEvaluateService {

    @Resource
    private ReservationOrderMapper reservationOrderMapper;
    @Autowired
    private RemoteMiniAccountService remoteMiniAccountService;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean evaluate(ReservationOrderEvaluateDto dto) {
        // 获取当前用户信息
        CurUserDto curUser = CurUserUtil.getHttpCurUser();
        if (curUser == null){
            throw new ServiceException("用户未登录", SystemCode.UNAUTHORIZED.getCode());
        }
        // 校验
        ReservationOrder orderEntity = reservationOrderMapper.selectById(dto.getReservationOrderId());
        if (orderEntity == null) {
            throw new ServiceException("预约单不存在");
        }else if (orderEntity.getStatus() != ReservationEnums.OrderStatus.WAIT_EVALUATE.getCode()) {
            throw new ServiceException("该预约单状态不是待评价状态，无法评价");
        }else if (!orderEntity.getUserId().equals(Long.valueOf(curUser.getUserId()))) {
            throw new ServiceException("该预约单不属于当前用户");
        }

        // 检查是否已经评价过
        LambdaQueryWrapper<ReservationOrderEvaluate> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ReservationOrderEvaluate::getReservationOrderId, dto.getReservationOrderId())
                .eq(ReservationOrderEvaluate::getDeleted, false);
        if (this.count(queryWrapper) > 0) {
            throw new ServiceException("该预约单已评价，不能重复评价");
        }

        // 创建评价实体
        ReservationOrderEvaluate entity = new ReservationOrderEvaluate();
        BeanUtils.copyProperties(dto, entity);
        entity.setUserId(Long.valueOf(curUser.getUserId()));
        entity.setUserName(curUser.getNikeName());
        entity.setUserAvatarUrl(curUser.getAvatarUrl());
        entity.setCreateUserId(Long.valueOf(curUser.getUserId()));
        entity.setCreateUserName(curUser.getNikeName());
        entity.setShopId(orderEntity.getShopId());
        // 分数>=3为满意，小于3为不满意
        if (dto.getRate()>=3){
            entity.setSuccessScore(ReservationEnums.EvaluateScore.SATISFIED.getCode());
        }else {
            entity.setSuccessScore(ReservationEnums.EvaluateScore.UNSATISFIED.getCode());
        }
        // 保存评价
        boolean result = this.save(entity);

        // 更新预约单状态为已完成
        if (result) {
            orderEntity.setStatus(ReservationEnums.OrderStatus.COMPLETED.getCode());
            orderEntity.setUpdateTime(LocalDateTime.now());
            reservationOrderMapper.updateById(orderEntity);
        }

        return result;
    }

    @Override
    public IPage<ReservationOrderEvaluateVo> getPage(ReservationOrderEvaluateQueryParam param) {
        Page<ReservationOrderEvaluateVo> page = new Page<>(param.getCurrent(), param.getSize());
        return this.baseMapper.getPage(page, param);
    }

    public ReservationOrderEvaluateVo getByOrderId(Long id) {
        LambdaQueryWrapper<ReservationOrderEvaluate> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ReservationOrderEvaluate::getReservationOrderId, id).last("limit 1");
        ReservationOrderEvaluate reservationOrderEvaluate = this.baseMapper.selectOne(queryWrapper);
        ReservationOrderEvaluateVo vo = new ReservationOrderEvaluateVo();
        if (reservationOrderEvaluate == null) {
            throw new ServiceException("评价不存在");
        }
        BeanUtils.copyProperties(reservationOrderEvaluate, vo);

        return vo;
    }
} 