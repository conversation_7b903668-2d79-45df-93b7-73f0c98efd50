package com.medusa.gruul.account.model.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 10:00 2025/3/18
 */
@Data
@ApiModel(value = "新增/修改会员等级信息dto")
public class MemberLevelRuleMessageDto {

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "升级方式->1.消费额升级，2.积分升级，3.商品消费额升级，4.指定商品数，5.直推会员，6.团队业绩")
    private String type;

    @ApiModelProperty(value = "会员说明")
    private String description;

    @ApiModelProperty(value = "会员类型id")
    private Long memberTypeId;
    /**
     * 订单会员类型ID
     */
    @ApiModelProperty(value = "订单会员类型ID")
    private String orderMemberTypeIds;

    @ApiModelProperty(value = "会员等级规则")
    private List<MemberLevelRuleDto>rules;

    @ApiModelProperty(value = "会员等级规则商品")
    private List<MemberLevelRuleProductDto>products;

}
