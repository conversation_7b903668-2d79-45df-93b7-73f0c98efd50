package com.medusa.gruul.platform.model.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.medusa.gruul.common.data.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;


/**
 * @Description: 平台装修模板页面组件属性表
 * @Author: jeecg-boot
 * @Date:   2023-09-06
 * @Version: V1.0
 */
@Data
@ApiModel(value="PlatformRenovationPageAssemblyDto对象", description="平台装修模板页面组件属性表")
public class PlatformRenovationPageAssemblyDto {
    
	/**id*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
	private Long id;

	/**所属页面ID*/
    @ApiModelProperty(value = "所属页面ID")
	private Long pageId;
	/**组件属性json串*/
    @ApiModelProperty(value = "组件属性json串")
	private String properties;
}
