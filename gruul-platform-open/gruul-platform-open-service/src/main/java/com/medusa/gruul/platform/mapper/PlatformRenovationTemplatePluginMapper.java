package com.medusa.gruul.platform.mapper;

import java.util.List;

import com.medusa.gruul.platform.api.entity.PlatformRenovationTemplatePlugin;
import org.apache.ibatis.annotations.Param;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: 平台装修模板全局控件属性表
 * @Author: jeecg-boot
 * @Date:   2023-09-06
 * @Version: V1.0
 */
public interface PlatformRenovationTemplatePluginMapper extends BaseMapper<PlatformRenovationTemplatePlugin> {

}
