package com.medusa.gruul.platform.model.dto;

import com.medusa.gruul.common.core.param.QueryParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 预约分页查询DTO
 *
 * <AUTHOR>
 * @since 2025-06-10
 */
@Data
@ApiModel(value = "预约分页查询DTO", description = "预约分页查询DTO")
public class ReservationPageQueryDto extends QueryParam {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "shouId")
    private String shouId;

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "userId")
    private Long userId;

    @ApiModelProperty(value = "userName")
    private String userName;

    /**
     * 手机号
     */
    @ApiModelProperty(value = "手机号")
    private String mobile;

    /**
     * 预约职员ID
     */
    @ApiModelProperty(value = "预约职员ID")
    private Long employeeId;

    /**
     * 预约职员ID
     */
    @ApiModelProperty(value = "预约职员Name")
    private String employeeName;

    /**
     * 预约项目ID
     */
    @ApiModelProperty(value = "预约项目ID")
    private Long serviceId;

    /**
     * 预约项目ID
     */
    @ApiModelProperty(value = "预约项目名称")
    private String serviceName;


    /**
     * 状态
     */
    @ApiModelProperty(value = "状态(101-> 正常，104-> 待评价，105-> 已完成，300-> 超时取消，301-> 用户取消)")
    private Integer status;

    /**
     * 预约开始时间-开始
     */
    @ApiModelProperty(value = "预约开始时间-开始")
    private LocalDateTime reservationTimeStartBegin;

    /**
     * 预约开始时间-结束
     */
    @ApiModelProperty(value = "预约开始时间-结束")
    private LocalDateTime reservationTimeStartEnd;

} 