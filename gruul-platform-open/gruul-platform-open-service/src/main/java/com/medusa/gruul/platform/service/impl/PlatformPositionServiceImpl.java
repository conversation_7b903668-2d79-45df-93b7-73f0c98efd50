package com.medusa.gruul.platform.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.medusa.gruul.common.core.exception.ServiceException;
import com.medusa.gruul.common.core.util.CurUserUtil;
import com.medusa.gruul.common.core.util.SpringContextHolder;
import com.medusa.gruul.common.dto.CurUserDto;
import com.medusa.gruul.platform.api.entity.PlatformEmployeePosition;
import com.medusa.gruul.platform.api.entity.PlatformPosition;
import com.medusa.gruul.platform.api.entity.PlatformPositionTemplate;
import com.medusa.gruul.platform.api.enums.PlatformPositionEnums;
import com.medusa.gruul.platform.api.model.vo.PlatformPositionVo;
import com.medusa.gruul.platform.mapper.PlatformPositionMapper;
import com.medusa.gruul.platform.api.model.dto.MessageTemplateCopyDto;
import com.medusa.gruul.platform.model.param.PlatformPositionParam;
import com.medusa.gruul.platform.service.IPlatformEmployeePositionService;
import com.medusa.gruul.platform.service.IPlatformPositionService;
import com.medusa.gruul.platform.service.IPlatformPositionTemplateService;
import com.medusa.gruul.shops.api.enums.PartnerModelEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.ConcurrentMap;
import java.util.stream.Collectors;

/**
 * <p>
 * 商家职位表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
@Service
@Slf4j
public class PlatformPositionServiceImpl extends ServiceImpl<PlatformPositionMapper, PlatformPosition> implements IPlatformPositionService {

    @Autowired
    private IPlatformEmployeePositionService platformEmployeePositionService;

    @Autowired
    private PlatformPositionMapper platformPositionMapper;

    @Autowired
    private IPlatformPositionTemplateService iPlatformPositionTemplateService;

    @Override
    public boolean savePosition(PlatformPosition platformPosition) {
        // 校验职位名称是否重复
        checkPositionNameDuplicate(platformPosition);
        
        // 校验职位名称是否为空
        if (StringUtils.isBlank(platformPosition.getPositionName())) {
            throw new ServiceException("职位名称不能为空");
        }
        
        // 默认启用状态
        if (platformPosition.getStatus() == null) {
            platformPosition.setStatus(PlatformPositionEnums.StatusEnum.ENABLED.getCode());
        }
        CurUserDto curUser = CurUserUtil.getHttpCurUser();
        platformPosition.setCreateUserId(Long.parseLong(curUser.getUserId()));
        platformPosition.setCreateUserName(curUser.getNikeName());
        return save(platformPosition);
    }

    @Override
    public boolean updatePosition(PlatformPosition platformPosition) {
        if (platformPosition.getId() == null){
            throw new ServiceException("职位ID不能为空");
        }
        // 校验职位名称是否重复
        checkPositionNameDuplicate(platformPosition);
        
        // 校验职位名称是否为空
        if (StringUtils.isBlank(platformPosition.getPositionName())) {
            throw new ServiceException("职位名称不能为空");
        }
        CurUserDto curUser = CurUserUtil.getHttpCurUser();
        platformPosition.setLastModifyUserId(Long.parseLong(curUser.getUserId()));
        platformPosition.setLastModifyUserName(curUser.getNikeName());
        return updateById(platformPosition);
    }

    @Override
    public boolean removePosition(Long id) {
        // 检查职位是否被使用
        if (platformEmployeePositionService.isPositionUsed(id)) {
            throw new ServiceException("该职位已被使用，不能删除");
        }
        return  removeById(id);
    }

    @Override
    public IPage<PlatformPosition> pagePosition(PlatformPositionParam platformPositionParam) {
        Page<PlatformPosition> page = new Page<>(platformPositionParam.getCurrent(), platformPositionParam.getSize());
        
        LambdaQueryWrapper<PlatformPosition> queryWrapper = new LambdaQueryWrapper<>();
        
        // 根据职位名称模糊查询
        if (StringUtils.isNotBlank(platformPositionParam.getPositionName())) {
            queryWrapper.like(PlatformPosition::getPositionName, platformPositionParam.getPositionName());
        }
        
        // 根据状态精确查询
        if (platformPositionParam.getStatus() != null) {
            queryWrapper.eq(PlatformPosition::getStatus, platformPositionParam.getStatus());
        }
        
        // 根据备注模糊查询
        if (StringUtils.isNotBlank(platformPositionParam.getRemark())) {
            queryWrapper.like(PlatformPosition::getRemark, platformPositionParam.getRemark());
        }
        
        // 关键字模糊查询（职位名称或备注）
        if (StringUtils.isNotBlank(platformPositionParam.getKeyword())) {
            queryWrapper.and(wrapper -> wrapper
                    .like(PlatformPosition::getPositionName, platformPositionParam.getKeyword())
                    .or()
                    .like(PlatformPosition::getRemark, platformPositionParam.getKeyword())
            );
        }
        //  删除状态为false
        queryWrapper.eq(PlatformPosition::getDeleted, false);
        // 按照ID降序排序
        queryWrapper.orderByDesc(PlatformPosition::getId);
        
        IPage<PlatformPosition> iPage = page(page, queryWrapper);
        
        return iPage;
    }

    @Override
    public IPage<PlatformPositionVo> pagePositionWithEmployee(PlatformPositionParam platformPositionParam) {
        Long employeeId = platformPositionParam.getEmployeeId();
        if (employeeId == null) {
            throw new ServiceException("员工ID不能为空");
        }
        Page<PlatformPositionVo> page = new Page<>(platformPositionParam.getCurrent(), platformPositionParam.getSize());
        List<PlatformPositionVo> list = platformPositionMapper.pagePositionWithEmployee(page,employeeId);
      /*  //先查出 职位信息
        IPage<PlatformPosition> iPage = SpringContextHolder.getBean(this.getClass()).pagePosition(positionParam);
        List<PlatformPosition> records = iPage.getRecords();
        List<PlatformPositionVo> result = new ArrayList<>((int) iPage.getSize());
        BeanUtils.copyProperties(records,result);
        if (records != null && !records.isEmpty()) {
            List<Long> idsByEmployeeIds = platformEmployeePositionService.getPositionIdsByEmployeeId(employeeId);
            result.forEach(position -> {
                if (idsByEmployeeIds.contains(position.getId())) {
                    position.setIsEmployeePosts(PlatformPositionEnums.DefaultEnum.YES.getCode());
                }else {
                    position.setIsEmployeePosts(PlatformPositionEnums.DefaultEnum.NO.getCode());
                }
            });
        }
        IPage<PlatformPositionVo> iPageResult = new Page<>(positionParam.getCurrent(), positionParam.getSize(), iPage.getTotal());*/
        return page.setRecords(list);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void copyFromTemplate(MessageTemplateCopyDto  msg) {
        if (!msg.getPartnerModel().equals(PartnerModelEnum.OPERATION_CENTER.getType())||
                StringUtils.isBlank(msg.getTenantId()) ||
                StringUtils.isBlank(msg.getShopId())){
            throw new ServiceException("参数错误");
        }
        // 查询模板
        List<PlatformPositionTemplate> templateList = iPlatformPositionTemplateService.list(
                new LambdaQueryWrapper<PlatformPositionTemplate>()
                        .eq(PlatformPositionTemplate::getPartnerModel, msg.getPartnerModel())
                        .eq(PlatformPositionTemplate::getStatus, PlatformPositionEnums.StatusEnum.ENABLED.getCode())
        );

        if (!templateList.isEmpty()) {


            // 获取所有模板中的职位名称
            List<String> allPositionNames = templateList.stream()
                    .map(PlatformPositionTemplate::getPositionName)
                    .collect(Collectors.toList());

            // 查询已存在的职位，避免重复插入
            List<String> existingPositionNames = new ArrayList<>();

            // 使用 IN 查询一次性获取所有已存在的职位名称
            if (!allPositionNames.isEmpty()) {
                LambdaQueryWrapper<PlatformPosition> positionWrapper = new LambdaQueryWrapper<>();
                positionWrapper.eq(PlatformPosition::getShopId, msg.getShopId())
                        .in(PlatformPosition::getPositionName, allPositionNames);

                List<PlatformPosition> existingPositions = super.list(positionWrapper);
                existingPositionNames = existingPositions.stream()
                        .map(PlatformPosition::getPositionName)
                        .collect(Collectors.toList());
            }

            // 创建需要保存的职位列表
            List<PlatformPosition> positions = new ArrayList<>(templateList.size() - existingPositionNames.size());
            for (PlatformPositionTemplate template : templateList) {
                if (!existingPositionNames.contains(template.getPositionName())) {
                    PlatformPosition position = new PlatformPosition();
                    BeanUtils.copyProperties(template, position, "id", "partnerModel");
                    position.setShopId(msg.getShopId());
                    position.setTenantId(msg.getTenantId());
                    positions.add(position);
                }
            }

            // 批量保存职位
            if (!positions.isEmpty()) {
                boolean saveBatch = SpringContextHolder.getBean(IPlatformPositionService.class).saveBatch(positions);
                if (!saveBatch) {
                    throw new ServiceException("职位复制失败: 无法保存职位到数据库");
                }
            }
        }
    }


    /**
     * 校验职位名称是否重复
     *
     * @param platformPosition 职位信息
     */
    private void checkPositionNameDuplicate(PlatformPosition platformPosition) {
        LambdaQueryWrapper<PlatformPosition> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PlatformPosition::getPositionName, platformPosition.getPositionName());
        
        // 如果是修改操作，需要排除自身
        if (platformPosition.getId() != null) {
            queryWrapper.ne(PlatformPosition::getId, platformPosition.getId());
        }
        
        int count = count(queryWrapper);
        if (count > 0) {
            throw new ServiceException("职位名称已存在");
        }
    }

    public Map<Long, List<PlatformPositionVo>> getPositionsByEmployeeIds(List<Long> employeeIds) {
        LambdaQueryWrapper<PlatformEmployeePosition> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(PlatformEmployeePosition::getEmployeeId, employeeIds);
        List<PlatformEmployeePosition> employeePositionList = platformEmployeePositionService.list(queryWrapper);

        if (CollectionUtils.isEmpty(employeePositionList)) {
            return new HashMap<>();
        }
        Set<Long> positionIds = employeePositionList.stream().map(PlatformEmployeePosition::getPositionId).collect(Collectors.toSet());
        List<PlatformPosition> positionList = platformPositionMapper.selectBatchIds(positionIds);
        if (CollectionUtils.isEmpty(positionList)){
            return  new HashMap<>();
        }
        Map<Long, PlatformPositionVo> positionMap = positionList.stream().collect(
                Collectors.toMap(PlatformPosition::getId, PlatformPosition -> {
                    PlatformPositionVo vo = new PlatformPositionVo();
                    BeanUtils.copyProperties(PlatformPosition, vo);
                    return vo;
                }));

        // key:员工ID，value:职位列表
        ConcurrentMap<Long, List<PlatformPositionVo>> result = employeePositionList.stream().collect(Collectors.
                groupingByConcurrent(PlatformEmployeePosition::getEmployeeId,
                        Collectors.mapping(item -> positionMap.get(item.getPositionId()), Collectors.toList())));

        return result;
    }
} 