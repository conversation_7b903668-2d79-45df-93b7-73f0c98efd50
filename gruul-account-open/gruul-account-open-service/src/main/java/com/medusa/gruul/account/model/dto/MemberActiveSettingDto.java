package com.medusa.gruul.account.model.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 14:23 2025/6/3
 */
@Data
@ApiModel(value = "新增/编辑会员有效期设置Dto")
public class MemberActiveSettingDto {

    @ApiModelProperty(value = "有效期天数")
    private Integer activeDays;

    @ApiModelProperty(value = "剩余天数提醒")
    private Integer remainingDays;

    @ApiModelProperty(value = "状态:0-停用，1-启用")
    private Integer status;

    @ApiModelProperty(value = "激活会员商品列表")
    private List<MemberActiveProductDto>productList;
}
