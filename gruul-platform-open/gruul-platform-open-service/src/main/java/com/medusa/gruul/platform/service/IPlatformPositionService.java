package com.medusa.gruul.platform.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.medusa.gruul.platform.api.entity.PlatformPosition;
import com.medusa.gruul.platform.api.model.vo.PlatformPositionVo;
import com.medusa.gruul.platform.api.model.dto.MessageTemplateCopyDto;
import com.medusa.gruul.platform.model.param.PlatformPositionParam;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 商家职位表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
public interface IPlatformPositionService extends IService<PlatformPosition> {

    /**
     * 新增职位
     *
     * @param platformPosition 职位信息
     * @return 是否成功
     */
    boolean savePosition(PlatformPosition platformPosition);

    /**
     * 修改职位
     *
     * @param platformPosition 职位信息
     * @return 是否成功
     */
    boolean updatePosition(PlatformPosition platformPosition);

    /**
     * 删除职位
     *
     * @param id 职位ID
     * @return 是否成功
     */
    boolean removePosition(Long id);

    /**
     * 分页查询职位
     *
     * @param platformPositionParam 查询参数
     * @return 分页结果
     */
    IPage<PlatformPosition> pagePosition(PlatformPositionParam platformPositionParam);

    /**
     * 分页查询职位列表 并根据员工ID确认是否有该职位
     *
     * @param platformPositionParam 查询参数
     * @return 分页结果
     */
    IPage<PlatformPositionVo> pagePositionWithEmployee(PlatformPositionParam platformPositionParam);

    /**
     * 复制职位
     *
     * @param messageTemplateCopyDto 适用模版类型
     */
    void  copyFromTemplate(MessageTemplateCopyDto messageTemplateCopyDto);

    /**
     * 根据员工ID获取职位列表
     *获取职位列表 key:职位ID value:职位名称
     * @param employeeIds 员工ID列表
     * @return 职位Map
     */
    Map<Long, List<PlatformPositionVo>> getPositionsByEmployeeIds(List<Long> employeeIds);
}