package com.medusa.gruul.platform.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.medusa.gruul.common.core.constant.CommonConstants;
import com.medusa.gruul.common.core.constant.enums.SourceTypeEnum;
import com.medusa.gruul.common.core.encrypt.AESUtil;
import com.medusa.gruul.common.core.exception.ServiceException;
import com.medusa.gruul.common.core.util.*;
import com.medusa.gruul.goods.api.entity.Product;
import com.medusa.gruul.goods.api.entity.Warehouse;
import com.medusa.gruul.goods.api.enums.ProductStatusEnum;
import com.medusa.gruul.goods.api.enums.ProductTypeEnum;
import com.medusa.gruul.goods.api.feign.RemoteGoodsService;
import com.medusa.gruul.order.api.feign.RemoteOrderService;
import com.medusa.gruul.platform.api.entity.AccountInfo;
import com.medusa.gruul.platform.api.entity.AuthUserRole;
import com.medusa.gruul.platform.api.entity.PlatformEmployee;
import com.medusa.gruul.platform.api.entity.PlatformStoreFront;
import com.medusa.gruul.platform.api.model.dto.OutPlatformStoreFrontDto;
import com.medusa.gruul.platform.api.model.dto.ShopInfoDto;
import com.medusa.gruul.platform.api.model.dto.StoreFrontAddressDto;
import com.medusa.gruul.platform.api.model.vo.DepartmentVo;
import com.medusa.gruul.platform.api.model.vo.StoreFrontVo;
import com.medusa.gruul.platform.mapper.PlatformEmployeeMapper;
import com.medusa.gruul.platform.mapper.PlatformStoreFrontMapper;
import com.medusa.gruul.platform.model.dto.PlatformStoreFrontDto;
import com.medusa.gruul.platform.model.param.ApiStoreFrontParam;
import com.medusa.gruul.platform.model.param.StoreFrontParam;
import com.medusa.gruul.platform.model.vo.ApiStoreFrontVo;
import com.medusa.gruul.platform.service.IAccountInfoService;
import com.medusa.gruul.platform.service.IPlatformShopInfoService;
import com.medusa.gruul.platform.service.IPlatformStoreFrontService;
import com.medusa.gruul.shops.api.entity.ShopsPartner;
import com.medusa.gruul.shops.api.enums.TradeStatusEnum;
import com.medusa.gruul.shops.api.feign.RemoteShopsService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 17:28 2024/10/8
 */
@Service
public class PlatformStoreFrontServiceImpl extends ServiceImpl<PlatformStoreFrontMapper, PlatformStoreFront> implements IPlatformStoreFrontService {

    @Autowired
    private PlatformEmployeeMapper platformEmployeeMapper;
    @Autowired
    private RemoteShopsService remoteShopsService;
    @Autowired
    private RemoteGoodsService remoteGoodsService;
    @Autowired
    private IPlatformShopInfoService platformShopInfoService;
    @Autowired
    private IAccountInfoService accountInfoService;
    @Autowired
    private RemoteOrderService remoteOrderService;

    @Override
    public PlatformStoreFront newAdd(OutPlatformStoreFrontDto outPlatformStoreFrontDto) {
        PlatformStoreFront platformStoreFront = new PlatformStoreFront();
        if(null == outPlatformStoreFrontDto.getId()){
            //新增
            int numberCount = this.count(new LambdaQueryWrapper<PlatformStoreFront>()
                    .and(wrapper -> wrapper.eq(PlatformStoreFront::getStoreNumber, outPlatformStoreFrontDto.getStoreNumber()).or()
                            .eq(PlatformStoreFront::getStoreFullName, outPlatformStoreFrontDto.getStoreFullName()))
            );
            if(numberCount > 0){
                throw new ServiceException("门店名称或者编号已存在！", SystemCode.DATA_EXISTED.getCode());
            }
            BeanUtil.copyProperties(outPlatformStoreFrontDto, platformStoreFront);
            platformStoreFront.setSourceType(SourceTypeEnum.OTHER.getStatus());
            this.save(platformStoreFront);
        }else{
            //更新
            PlatformStoreFront dbData = this.baseMapper.selectById(outPlatformStoreFrontDto.getId());
            if(null == dbData){
                throw new ServiceException("id对应的记录不存在！", SystemCode.DATA_NOT_EXIST.getCode());
            }
            BeanUtil.copyProperties(outPlatformStoreFrontDto, platformStoreFront);
            platformStoreFront.setId(dbData.getId());
            this.updateById(platformStoreFront);
        }
        return platformStoreFront;
    }

    @Override
    public PageUtils<StoreFrontVo> searchStoreFront(StoreFrontParam storeFrontParam) {
        if(null == storeFrontParam || (StrUtil.isBlank(storeFrontParam.getParentClassCode()) && StrUtil.isBlank(storeFrontParam.getStoreNumber())
                && StrUtil.isBlank(storeFrontParam.getStoreFullName())&& StrUtil.isBlank(storeFrontParam.getDepartmentName())
                && StrUtil.isBlank(storeFrontParam.getEmployeeName()) && StrUtil.isBlank(storeFrontParam.getStockName()))){
            storeFrontParam.setFirstClassFlag("1");
        }
        IPage<StoreFrontVo> page = this.baseMapper.searchStoreFront(new Page<>(storeFrontParam.getCurrent(), storeFrontParam.getSize()),
                storeFrontParam);
        return new PageUtils<>(page);
    }

    @Override
    public String getStoreNumber(String parentCode) {
        String lastStoreNumber = "";
        String storeNumber = "";
        if(StringUtils.isNotEmpty(parentCode)){
            lastStoreNumber = baseMapper.getLastStoreNumberByParentCode(parentCode);
        }else{
            lastStoreNumber = baseMapper.getLastStoreNumber();
        }
        lastStoreNumber  = lastStoreNumber==null?"":lastStoreNumber.trim();
        if(lastStoreNumber.matches("\\w*\\d$")){
            for(int j=lastStoreNumber.length()-1;j>=0;j--){
                if(!Character.isDigit(lastStoreNumber.charAt(j))||(Character.isDigit(lastStoreNumber.charAt(j))&&j==0)){
                    String numStr=lastStoreNumber;
                    if(j>0||(!Character.isDigit(lastStoreNumber.charAt(j))&&j==0)){
                        numStr=lastStoreNumber.substring(j+1);
                    }
                    String numStr2=String.valueOf(Long.parseLong(numStr));
                    String zero=numStr.substring(0,numStr.indexOf(numStr2));
                    long num=Long.parseLong(numStr2);
                    if(!lastStoreNumber.equals(numStr)){
                        lastStoreNumber=lastStoreNumber.substring(0,j+1);
                    }else{
                        lastStoreNumber="";
                    }
                    if(String.valueOf(num+1).length()>String.valueOf(numStr2).length()&&zero.length()>0){
                        lastStoreNumber+=zero.substring(0,zero.length()-1)+(num+1);
                    }else{
                        lastStoreNumber+=zero+(num+1);
                    }
                    break;
                }
            }
            storeNumber = lastStoreNumber;
        }
        return storeNumber;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addStoreFront(PlatformStoreFrontDto dto) {
        PlatformStoreFront platformStoreFront = new PlatformStoreFront();
        BeanUtil.copyProperties(dto,platformStoreFront);
        String maxCode = baseMapper.getMaxCode(dto.getParentCode());
        String parentCode = dto.getParentCode();
        // 新增的门店，默认都是末级门店
        platformStoreFront.setIsCatalog(CommonConstants.NUMBER_ZERO+"");

        // 如果是启用，则需要判断是否超过了末级门店的启用门店限制数
        if(CommonConstants.NUMBER_ZERO.intValue() == Integer.parseInt(dto.getStatusId())){
            int productMaxNum = 0;
            Result<ShopInfoDto> result = platformShopInfoService.getShopInfo();
            ShopInfoDto shopInfoDto = result.getData();
            if(shopInfoDto!=null){
                String enableStoreMaxNum = shopInfoDto.getEnableStoreMaxNum();
                if(StrUtil.isNotEmpty(enableStoreMaxNum)){
                    try {
                        productMaxNum = Integer.parseInt(AESUtil.decryptDataBase64MD5Key(enableStoreMaxNum, null));
                    } catch (Exception e) {
                        e.printStackTrace();
                        throw new ServiceException("保存失败，获取门店启用限制数量失败", SystemCode.DATA_ADD_FAILED.getCode());
                    }
                }
            }
            // 获取数据库现有已启用的末级门店数量
            int dbCount = this.count(new QueryWrapper<PlatformStoreFront>().eq("status_id", CommonConstants.NUMBER_ZERO.intValue() + "").eq("is_catalog", CommonConstants.NUMBER_ZERO+""));
            // 获取传输过来的未上架的商品数量
            int unCount = 1;
            if(dbCount + unCount > productMaxNum){
                throw new ServiceException("保存失败，启用数量超过限制【" + productMaxNum + "】", SystemCode.DATA_ADD_FAILED.getCode());
            }
        }
        //如果上级classCode不为空，设为第一级
        if(StringUtils.isNotEmpty(parentCode)){
            LambdaQueryWrapper<PlatformStoreFront>wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(PlatformStoreFront::getClassCode,parentCode);
            List<PlatformStoreFront> list = this.list(wrapper);
            if(list!=null&&list.size()>0){
                PlatformStoreFront platformStoreFrontParent = list.get(0);

                //绑定用户关系
                LambdaQueryWrapper<AccountInfo>accountInfoLambdaQueryWrapper = new LambdaQueryWrapper<>();
                accountInfoLambdaQueryWrapper.eq(AccountInfo::getStoreFrontCode,parentCode);
                int accountCount = accountInfoService.count(accountInfoLambdaQueryWrapper);
                if(accountCount>0){
                    throw new ServiceException("保存失败，上级门店存在绑定用户关系", SystemCode.DATA_ADD_FAILED.getCode());
                }
                //门店下过单
                int orderCount = remoteOrderService.getOrderCountByStoreFrontCode(parentCode);
                if(orderCount>0){
                    throw new ServiceException("保存失败，上级门店已下过单", SystemCode.DATA_ADD_FAILED.getCode());
                }

                platformStoreFrontParent.setIsCatalog(CommonConstants.NUMBER_ONE+"");
                this.baseMapper.updateById(platformStoreFrontParent);
            }
        }
        String classCode = getClassCode(maxCode, parentCode);
        platformStoreFront.setClassCode(classCode);
        this.baseMapper.insert(platformStoreFront);


    }

    /**
     * 获取classCode
     * @param maxCode
     * @param parentCode
     * @return
     */
    private String getClassCode (String maxCode,String parentCode){
        String classCode = "";
        if(StringUtils.isEmpty(maxCode)){
            classCode = parentCode+"00001";
        }else{
            int len = maxCode.length();
            int i = len-1;
            char str = 'A';
            while (i>=0){
                str = maxCode.charAt(i);
                if(str!='Z'){
                    char  newChar = 'A';
                    if(str==57){
                        newChar =  'A';
                    }else{
                        newChar = (char)(str+1);
                    }
                    classCode=parentCode+maxCode.substring(0, i)+newChar + "00000".substring(i,len-1);
                    break;
                }
                i = i-1;
            }
        }
        return classCode;
    }
    @Override
    public List<ApiStoreFrontVo> getApiStoreFront(ApiStoreFrontParam param) {
        List<ApiStoreFrontVo> dataList = new ArrayList<>();
        LambdaQueryWrapper<PlatformStoreFront>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PlatformStoreFront::getStatusId,0);
        wrapper.eq(PlatformStoreFront::getIsCatalog, CommonConstants.NUMBER_ZERO);
        if(param.getIsAddress()!=null&&param.getIsAddress() == 1){
            wrapper.isNotNull(PlatformStoreFront::getAddress);
            wrapper.isNotNull(PlatformStoreFront::getMapX);
            wrapper.isNotNull(PlatformStoreFront::getMapY);
        }
        List<PlatformStoreFront> list = this.baseMapper.selectList(wrapper);
        Double lat = param.getLat();
        Double lng = param.getLng();
        if(list!=null&&list.size()>0){
            for (PlatformStoreFront platformStoreFront : list) {
                ApiStoreFrontVo apiStoreFrontVo = new ApiStoreFrontVo();
                String shopId = platformStoreFront.getShopId();
                apiStoreFrontVo.setId(platformStoreFront.getId()+"");
                apiStoreFrontVo.setStoreFullName(platformStoreFront.getStoreFullName());
                apiStoreFrontVo.setMapX(platformStoreFront.getMapX());
                apiStoreFrontVo.setMapY(platformStoreFront.getMapY());

                Double distance = 0d;
                if(null != apiStoreFrontVo.getMapX() && null != apiStoreFrontVo.getMapY() && null != lng && null != lat){
                    distance = DistanceUtil.distanceByLngLat(lng, lat, apiStoreFrontVo.getMapX(), apiStoreFrontVo.getMapY());
                    apiStoreFrontVo.setDistance(distance);
                    apiStoreFrontVo.setFormatDistance(DistanceUtil.formatDistance(distance));
                    apiStoreFrontVo.setAddress(platformStoreFront.getAddress());
                }else{
                    distance = 99999d;
                    apiStoreFrontVo.setDistance(distance);
                    apiStoreFrontVo.setAddress("暂无地址");
                    apiStoreFrontVo.setFormatDistance("暂无距离");
                }
                apiStoreFrontVo.setBusinessFlag(0);
                ShopsPartner shopsPartner = remoteShopsService.getByShopId(Long.valueOf(shopId));
                if(shopsPartner!=null){
                    if(null != shopsPartner.getTradeStatus() && shopsPartner.getTradeStatus() == TradeStatusEnum.START.getType()){
                        // 营业
                        if(StrUtil.isNotBlank(shopsPartner.getTradeDate()) && null != shopsPartner.getBusinessBeginHours() && null != shopsPartner.getBusinessEndHours()){
                            if(shopsPartner.getTradeDate().contains(DateUtils.weekDay(new Date()))){
                                // 营业日期包含当前日期
                                Date beginTime = DateUtil.parse(DateUtil.format(shopsPartner.getBusinessBeginHours(), "HH:mm"), "HH:mm");
                                Date endTime = DateUtil.parse(DateUtil.format(shopsPartner.getBusinessEndHours(), "HH:mm"), "HH:mm");
                                Date nowTime = DateUtil.parse(DateUtil.format(new Date(), "HH:mm"), "HH:mm");
                                if(DateUtil.isIn(nowTime, beginTime, endTime)){
                                    apiStoreFrontVo.setBusinessFlag(1);
                                }
                                apiStoreFrontVo.setBusinessBeginHours(DateUtil.format(shopsPartner.getBusinessBeginHours(), "HH:mm"));
                                apiStoreFrontVo.setBusinessEndHours(DateUtil.format(shopsPartner.getBusinessEndHours(), "HH:mm"));
                            }
                        }
                    }
                }
                dataList.add(apiStoreFrontVo);
            }
        }
        // 排序，先按照可用次数排序，再按照距离进行排序
        Collections.sort(dataList, new Comparator<ApiStoreFrontVo>() {
            @Override
            public int compare(ApiStoreFrontVo o1, ApiStoreFrontVo o2) {
                // 可用次数相等，再比较距离
                return o1.getDistance().compareTo(o2.getDistance());
            }
        });
        return dataList;
    }

    @Override
    public void updateStatusId(OutPlatformStoreFrontDto outPlatformStoreFrontDto) {
        if(null == outPlatformStoreFrontDto.getId()){
            throw new ServiceException("门店信息id不能为空！");
        }else{
            Long id = outPlatformStoreFrontDto.getId();
            PlatformStoreFront platformStoreFront = this.baseMapper.selectById(id);
            if(null == platformStoreFront){
                throw new ServiceException("门店信息不存在！");
            }
            // 如果是启用，则需要判断是否超过了末级门店的启用门店限制数
            if(CommonConstants.NUMBER_ZERO.intValue() == Integer.parseInt(outPlatformStoreFrontDto.getStatusId()) && (CommonConstants.NUMBER_ZERO+"").equals(platformStoreFront.getIsCatalog())){
                int productMaxNum = 0;
                Result<ShopInfoDto> result = platformShopInfoService.getShopInfo();
                ShopInfoDto shopInfoDto = result.getData();
                if(shopInfoDto!=null){
                    String enableStoreMaxNum = shopInfoDto.getEnableStoreMaxNum();
                    if(StrUtil.isNotEmpty(enableStoreMaxNum)){
                        try {
                            productMaxNum = Integer.parseInt(AESUtil.decryptDataBase64MD5Key(enableStoreMaxNum, null));
                        } catch (Exception e) {
                            e.printStackTrace();
                            throw new ServiceException("启用失败，获取门店启用限制数量失败", SystemCode.DATA_ADD_FAILED.getCode());
                        }
                    }
                }
                // 获取数据库现有已启用的末级门店数量
                int dbCount = this.count(new QueryWrapper<PlatformStoreFront>().eq("status_id", CommonConstants.NUMBER_ZERO.intValue() + "").eq("is_catalog", CommonConstants.NUMBER_ZERO+""));
                // 获取传输过来的未上架的商品数量
                int unCount = 1;
                if(dbCount + unCount > productMaxNum){
                    throw new ServiceException("启用失败，启用数量超过限制【" + productMaxNum + "】", SystemCode.DATA_ADD_FAILED.getCode());
                }
            }

            platformStoreFront.setStatusId(outPlatformStoreFrontDto.getStatusId());
            this.baseMapper.updateById(platformStoreFront);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateAddress(StoreFrontAddressDto storeFrontAddressDto) {
        Long id = storeFrontAddressDto.getId();
        if(null==id){
            throw new ServiceException("门店id不能为空！");
        }

        PlatformStoreFront platformStoreFront = this.baseMapper.selectById(id);
        if(null == platformStoreFront){
            throw new ServiceException("门店不存在！");
        }

        platformStoreFront.setAddress(storeFrontAddressDto.getAddress());
        platformStoreFront.setMapX(storeFrontAddressDto.getMapX());
        platformStoreFront.setMapY(storeFrontAddressDto.getMapY());

        this.updateById(platformStoreFront);

        String employeeId = storeFrontAddressDto.getEmployeeId();

        if(StringUtils.isNotEmpty(employeeId)){
            LambdaQueryWrapper<PlatformEmployee>queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(PlatformEmployee::getOutId,employeeId);
            PlatformEmployee platformEmployee = platformEmployeeMapper.selectOne(queryWrapper);
            if(StringUtils.isNotEmpty(platformEmployee.getAccountId())
                    &&StringUtils.isNotEmpty(storeFrontAddressDto.getAccountId())
                    &&!platformEmployee.getAccountId().equals(storeFrontAddressDto.getAccountId())){
                LambdaQueryWrapper<PlatformEmployee>wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(PlatformEmployee::getAccountId,storeFrontAddressDto.getAccountId());
                List<PlatformEmployee> platformEmployees = platformEmployeeMapper.selectList(wrapper);
                if(platformEmployees!=null&&platformEmployees.size()>0){
                    throw new ServiceException("该会员已经绑定其他职员！");
                }
            }
            platformEmployee.setAccountId(storeFrontAddressDto.getAccountId());
            platformEmployee.setAccountName(storeFrontAddressDto.getAccountName());
            platformEmployeeMapper.updateById(platformEmployee);
        }
    }

    @Override
    public PageUtils<StoreFrontVo> pageBindStoreFront(StoreFrontParam storeFrontParam) {
        storeFrontParam.setFirstClassFlag("0");
        storeFrontParam.setIsCatalog("0");
        IPage<StoreFrontVo> page = this.baseMapper.searchStoreFront(new Page<>(storeFrontParam.getCurrent(), storeFrontParam.getSize()),
                storeFrontParam);
        return new PageUtils<>(page);
    }

    @Override
    @Transactional
    public void deleteStoreFront(PlatformStoreFrontDto dto) {
        Long id = dto.getId();
        if(id==null){
            throw new ServiceException("门店id不能为空");
        }

        PlatformStoreFront platformStoreFront = this.getById(id);
        if(platformStoreFront == null){
            throw new ServiceException("门店不存在");
        }
        //判断是否存在下级，存在不允许删除
        String classCode = platformStoreFront.getClassCode();
        if(StringUtils.isNotEmpty(classCode)){
            Integer count =baseMapper.getNextCount(classCode);
            if(count>0){
                throw new ServiceException("存在下级门店不允许删除");
            }
        }
        this.removeById(id);
    }

    @Override
    public void editStoreFront(PlatformStoreFrontDto dto) {
        Long id = dto.getId();
        if(id==null){
            throw new ServiceException("门店id不能为空");
        }
        PlatformStoreFront platformStoreFront = this.baseMapper.selectById(id);
        platformStoreFront.setStoreFullName(dto.getStoreFullName());
        platformStoreFront.setStoreNumber(dto.getStoreNumber());
        platformStoreFront.setStatusId(dto.getStatusId());

        // 如果是启用，则需要判断是否超过了末级门店的启用门店限制数
        if(CommonConstants.NUMBER_ZERO.intValue() == Integer.parseInt(dto.getStatusId()) && (CommonConstants.NUMBER_ZERO+"").equals(platformStoreFront.getIsCatalog())){
            int productMaxNum = 0;
            Result<ShopInfoDto> result = platformShopInfoService.getShopInfo();
            ShopInfoDto shopInfoDto = result.getData();
            if(shopInfoDto!=null){
                String enableStoreMaxNum = shopInfoDto.getEnableStoreMaxNum();
                if(StrUtil.isNotEmpty(enableStoreMaxNum)){
                    try {
                        productMaxNum = Integer.parseInt(AESUtil.decryptDataBase64MD5Key(enableStoreMaxNum, null));
                    } catch (Exception e) {
                        e.printStackTrace();
                        throw new ServiceException("修改失败，获取门店启用限制数量失败", SystemCode.DATA_ADD_FAILED.getCode());
                    }
                }
            }
            // 获取数据库现有已启用的末级门店数量
            int dbCount = this.count(new QueryWrapper<PlatformStoreFront>().eq("status_id", CommonConstants.NUMBER_ZERO.intValue() + "").eq("is_catalog", CommonConstants.NUMBER_ZERO+""));
            // 获取传输过来的未上架的商品数量
            int unCount = 1;
            if(dbCount + unCount > productMaxNum){
                throw new ServiceException("修改失败，启用数量超过限制【" + productMaxNum + "】", SystemCode.DATA_ADD_FAILED.getCode());
            }
        }

        this.updateById(platformStoreFront);
    }
}
