package com.medusa.gruul.account.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 14:15 2025/5/27
 */
@Data
@ApiModel(value = "小程序用户认证实名dto")
public class ApiMiniAccountCardDto {

    /**
     * 身份证人像面URL
     */
    @ApiModelProperty(value = "身份证人像面URL")
    private String idCardFaceUrl;

    /**
     * 身份证背面URL
     */
    @ApiModelProperty(value = "身份证背面URL")
    private String idCardBackUrl;


    /**
     * 身份证
     */
    @ApiModelProperty(value = "身份证")
    private String card;

    /**
     * 客户名称
     */
    @ApiModelProperty(value = "姓名")
    private String userName;

}
