package com.medusa.gruul.platform.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.medusa.gruul.platform.api.entity.PlatformPositionTemplate;
import com.medusa.gruul.platform.model.param.PlatformPositionTemplateParam;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 商家职位模板表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
public interface PlatformPositionTemplateMapper extends BaseMapper<PlatformPositionTemplate> {

    /**
     * 查询职位模板列表
     *
     * @param page 分页对象
     * @param templateParam 入参
     * @return 分页结果
     */
    IPage<PlatformPositionTemplate> selectPositionTemplateList(Page<PlatformPositionTemplate> page, @Param("params")PlatformPositionTemplateParam templateParam);
} 