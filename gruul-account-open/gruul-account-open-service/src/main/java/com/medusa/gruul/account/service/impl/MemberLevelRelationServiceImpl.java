package com.medusa.gruul.account.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.medusa.gruul.account.api.entity.*;
import com.medusa.gruul.account.mapper.MemberLevelRelationMapper;
import com.medusa.gruul.account.model.dto.ApiApplyRegionMemberDto;
import com.medusa.gruul.account.model.param.MemberLevelRelationParam;
import com.medusa.gruul.account.model.param.MemberLevelRuleMessageParam;
import com.medusa.gruul.account.model.vo.*;
import com.medusa.gruul.account.service.*;
import com.medusa.gruul.common.core.constant.CommonConstants;
import com.medusa.gruul.common.core.constant.enums.ApproveStatusEnum;
import com.medusa.gruul.common.core.constant.enums.MemberLevelRuleTypeEnum;
import com.medusa.gruul.common.core.constant.enums.MemberTypeStatusEnum;
import com.medusa.gruul.common.core.exception.ServiceException;
import com.medusa.gruul.common.core.util.CurUserUtil;
import com.medusa.gruul.common.core.util.LocalDateTimeUtils;
import com.medusa.gruul.common.core.util.SystemCode;
import com.medusa.gruul.common.dto.CurUserDto;
import com.medusa.gruul.order.api.feign.RemoteOrderService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 14:18 2025/5/22
 */
@Service
@Slf4j
public class MemberLevelRelationServiceImpl extends ServiceImpl<MemberLevelRelationMapper, MemberLevelRelation>implements IMemberLevelRelationService {

    @Autowired
    private IMiniAccountExtendsService miniAccountExtendsService;
    @Autowired
    private IMiniAccountService miniAccountService;
    @Autowired
    private IMemberTypeService memberTypeService;
    @Autowired
    private IMemberLevelRuleMessageService memberLevelRuleMessageService;
    @Autowired
    private IMemberLevelService memberLevelService;
    @Autowired
    private IMemberLevelRuleService memberLevelRuleService;
    @Autowired
    private IRemoteMiniAccountService remoteMiniAccountService;
    @Autowired
    private RemoteOrderService remoteOrderService;
    @Autowired
    private IMiniAccountApplyRegionMemberService miniAccountApplyRegionMemberService;

    @Override
    public List<MemberLevelRelation> getMemberLevelsByUserId(String userId) {
        LambdaQueryWrapper<MemberLevelRelation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MemberLevelRelation::getUserId,userId);
        List<MemberLevelRelation> memberLevelRelations = baseMapper.selectList(queryWrapper);
        if (memberLevelRelations != null && memberLevelRelations.size() > 0){
            return memberLevelRelations;
        }
        return Collections.emptyList();
    }

    @Override
    public List<MemberLevelRelation> getMemberLevelRelation(MemberLevelRelationParam param) {
        LambdaQueryWrapper<MemberLevelRelation> queryWrapper = new LambdaQueryWrapper<>();
        if(param.getMemberTypeId()!=null){
            queryWrapper.eq(MemberLevelRelation::getMemberTypeId,param.getMemberTypeId());
        }
        if(param.getMemberLevelId()!=null){
            queryWrapper.eq(MemberLevelRelation::getMemberLevelId,param.getMemberLevelId());
        }
        if(param.getUserId()!=null){
            queryWrapper.eq(MemberLevelRelation::getUserId,param.getUserId());
        }
        List<MemberLevelRelation> list = baseMapper.selectList(queryWrapper);
        return list;
    }

    @Override
    public ApiApplyRegionMemberVo getApplyRegionMember() {
        ApiApplyRegionMemberVo apiApplyRegionMemberVo = new ApiApplyRegionMemberVo();

        CurUserDto curUser = CurUserUtil.getHttpCurUser();
        MiniAccount miniAccount = miniAccountService.getByShopUserId(curUser.getUserId());
        if (miniAccount == null) {
            throw new ServiceException("数据错误！", SystemCode.DATA_NOT_EXIST_CODE);
        }
        apiApplyRegionMemberVo.setNikeName(miniAccount.getNikeName());
        apiApplyRegionMemberVo.setPhone(miniAccount.getPhone());
        //1.获取区域会员类型
        MemberType memberType = memberTypeService.getRegionMemberType();
        if(memberType == null){
            throw new ServiceException("区域会员类型不存在！", SystemCode.DATA_NOT_EXIST_CODE);
        }
        if(memberType.getStatus() == MemberTypeStatusEnum.NO.getStatus()){
            throw new ServiceException("区域会员类型已停用！", SystemCode.DATA_NOT_EXIST_CODE);
        }
        //2.查询会员在会员等级关联表里对应区域会员类型记录是否存在
        MemberLevelRelationParam memberLevelRelationParam = new MemberLevelRelationParam();
        memberLevelRelationParam.setUserId(miniAccount.getUserId());
        memberLevelRelationParam.setMemberTypeId(Long.valueOf(memberType.getId()));
        List<MemberLevelRelation> memberLevelRelationList = this.getMemberLevelRelation(memberLevelRelationParam);
        //3.区域会员类型升级规则
        MemberLevelRuleMessageParam memberLevelRuleMessageParam = new MemberLevelRuleMessageParam();
        memberLevelRuleMessageParam.setMemberTypeId(Long.valueOf(memberType.getId()));
        MemberLevelRuleMessageVo memberLevelRuleMessage = memberLevelRuleMessageService.getMemberLevelRuleMessage(memberLevelRuleMessageParam);

        if(memberLevelRuleMessage == null){
            throw new ServiceException("区域会员类型会员等级规则不存在！", SystemCode.DATA_NOT_EXIST_CODE);
        }
        if(StringUtils.isEmpty(memberLevelRuleMessage.getType())){
            throw new ServiceException("区域会员类型会员等级规则升级方式不存在！", SystemCode.DATA_NOT_EXIST_CODE);
        }

        //判断区域会员类型团队业绩升级方式
        Boolean regionUpgradeFlag = false;
        if(memberLevelRelationList!=null&&memberLevelRelationList.size()>0){
            MemberLevelRelation memberLevelRelation = memberLevelRelationList.get(0);
            String applyMemberLevelId = memberLevelRelation.getApplyMemberLevelId();
            if(StringUtils.isEmpty(applyMemberLevelId)){
                regionUpgradeFlag = true;
            }
        }else{
            regionUpgradeFlag = true;
        }

        if(regionUpgradeFlag){
            //区域团队业绩升级方式
            Boolean success = false;
            for (String upgradeType : memberLevelRuleMessage.getType().split(",")) {
                if(upgradeType.equals(MemberLevelRuleTypeEnum.TEAM_PERFORMANCE.getStatus())){
                    success = true;
                }
            }
            if(success){
                //查询区域会员类型排名由低到高排序的升级规则
                LambdaQueryWrapper<MemberLevelRule>wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(MemberLevelRule::getMainId,memberLevelRuleMessage.getId());
                wrapper.eq(MemberLevelRule::getDeleted, CommonConstants.NUMBER_ZERO);
                wrapper.orderByAsc(MemberLevelRule::getSort);
                List<MemberLevelRule> list = memberLevelRuleService.list(wrapper);
                if(list!=null&&list.size()>0){
                    List<MemberLevelRule> memberLevelRuleList = new ArrayList<>();
                    if(memberLevelRelationList!=null&&memberLevelRelationList.size()>0){
                        MemberLevelRelation memberLevelRelation = memberLevelRelationList.get(0);
                        //当前用户会员等级对应的规则
                        MemberLevelRuleVo memberLevelRuleVo = memberLevelRuleService.getByMemberLevelId(Long.valueOf(memberLevelRelation.getMemberLevelId()));
                        for (MemberLevelRule memberLevelRule : list) {
                            //且将比当前会员的区域会员类型的会员等级低的过滤掉
                            if(memberLevelRuleVo.getSort()< memberLevelRule.getSort()){
                                memberLevelRuleList.add(memberLevelRule);
                            }
                        }
                    }else{
                        memberLevelRuleList.addAll(list);
                    }
                    for (MemberLevelRule memberLevelRule : memberLevelRuleList) {
                        MemberLevelRuleVo upgradeMemberLevelRule = new MemberLevelRuleVo();
                        BeanUtils.copyProperties(memberLevelRule,upgradeMemberLevelRule);
                        //是否满足升级
                        Boolean upgradeFlag = false;
                        if(StringUtils.isNotEmpty(upgradeMemberLevelRule.getPreLowMemberLevelId())){
                            upgradeFlag = remoteMiniAccountService.checkUpgradePreLow(upgradeMemberLevelRule, miniAccount);
                        }else{
                            upgradeFlag = true;
                        }
                        if(upgradeFlag){
                            //团队周期
                            Integer teamTimes = memberLevelRule.getTeamTimes();
                            BigDecimal teamAmountStart = memberLevelRule.getTeamAmountStart();
                            if(teamTimes == null){
                                teamTimes = 0;
                            }
                            //当前用户区域等级类型对应升级记录
                            //当前团队业绩金额
                            String applyMemberLevelId = memberLevelRule.getMemberLevelId();
                            MemberLevel applyMemberLevel = memberLevelService.getById(applyMemberLevelId);
                            if(applyMemberLevel == null){
                                throw new ServiceException("升级的会员等级不存在！", SystemCode.DATA_NOT_EXIST_CODE);
                            }
                            Integer applyRegionType = applyMemberLevel.getRegionType();
                            if(applyRegionType == null){
                                throw new ServiceException("升级的会员等级区域类型不存在！", SystemCode.DATA_NOT_EXIST_CODE);
                            }
                            BigDecimal teamsUpgradeAmount = BigDecimal.ZERO;
                            if(memberLevelRelationList!=null&&memberLevelRelationList.size()>0){
                                MemberLevelRelation memberLevelRelation = memberLevelRelationList.get(0);
                                String memberLevelId = memberLevelRelation.getMemberLevelId();
                                MemberLevel memberLevel = memberLevelService.getById(memberLevelId);
                                if(memberLevel == null){
                                    throw new ServiceException("当前会员等级不存在！", SystemCode.DATA_NOT_EXIST_CODE);
                                }
                                Integer regionType = memberLevel.getRegionType();
                                if(regionType == null){
                                    throw new ServiceException("当前会员等级区域类型不存在！", SystemCode.DATA_NOT_EXIST_CODE);
                                }

                                if(memberLevelRelation.getUpLevelTime()!=null){
                                    String upLevelTime = LocalDateTimeUtils.formatTime(memberLevelRelation.getUpLevelTime(), "yyyy-MM-dd HH:mm:ss");
                                    String agentRegionCode = memberLevelRelation.getAgentRegionCode();
                                    if(StringUtils.isEmpty(agentRegionCode)){
                                        throw new ServiceException("代理区域编码不存在！", SystemCode.DATA_NOT_EXIST_CODE);
                                    }
                                    BigDecimal teamsRegionAmount = remoteOrderService.getTeamsRegionAmount(upLevelTime, regionType, agentRegionCode, teamTimes);
                                    if(teamsRegionAmount == null){
                                        teamsRegionAmount = BigDecimal.ZERO;
                                    }
                                    teamsUpgradeAmount = teamsUpgradeAmount.add(teamsRegionAmount);
                                }
                            }
                            if(teamsUpgradeAmount.compareTo(teamAmountStart)>=0){
                                //达到升级条件
                                apiApplyRegionMemberVo.setRegionType(applyRegionType);
                                apiApplyRegionMemberVo.setApplyMemberLevelId(memberLevelRule.getMemberLevelId());
                                List<String> agentRegionCodeList = this.getAgentRegionCodeList(Long.valueOf(memberType.getId()));
                                apiApplyRegionMemberVo.setAgentRegionCodeList(agentRegionCodeList);
                                apiApplyRegionMemberVo.setMemberTypeId(Long.valueOf(memberType.getId()));
                                apiApplyRegionMemberVo.setApplyMemberLevelName(applyMemberLevel.getMemberLevel());
                                break;
                            }
                        }
                    }
                }else{
                    throw new ServiceException("区域会员等级类型下会员等级规则不存在！！", SystemCode.DATA_NOT_EXIST_CODE);
                }
            }
        }
        //团队业绩不满足
        if(StringUtils.isEmpty(apiApplyRegionMemberVo.getApplyMemberLevelId())){
            if(memberLevelRelationList!=null&&memberLevelRelationList.size()>0){
                MemberLevelRelation memberLevelRelation = memberLevelRelationList.get(0);
                String applyMemberLevelId = memberLevelRelation.getApplyMemberLevelId();
                String memberLevelId = memberLevelRelation.getMemberLevelId();
                if(StringUtils.isNotEmpty(applyMemberLevelId)&&!applyMemberLevelId.equals(memberLevelId)){
                    //申请的会员等级
                    MemberLevel applyMemberLevel = memberLevelService.getById(applyMemberLevelId);
                    if(applyMemberLevel == null){
                        throw new ServiceException("申请的会员等级不存在！", SystemCode.DATA_NOT_EXIST_CODE);
                    }
                    //区域类型
                    Integer regionType = applyMemberLevel.getRegionType();
                    //查询该会员类型下已被代理的区域
                    List<String> agentRegionCodeList = this.getAgentRegionCodeList(Long.valueOf(memberType.getId()));

                    apiApplyRegionMemberVo.setRegionType(regionType);
                    apiApplyRegionMemberVo.setAgentRegionCodeList(agentRegionCodeList);
                    apiApplyRegionMemberVo.setApplyMemberLevelId(applyMemberLevelId);
                    apiApplyRegionMemberVo.setMemberTypeId(Long.valueOf(memberType.getId()));
                    apiApplyRegionMemberVo.setApplyMemberLevelName(applyMemberLevel.getMemberLevel());
                }
            }
        }
        if(StringUtils.isEmpty(apiApplyRegionMemberVo.getApplyMemberLevelId())){
            List<MemberLevelRuleProductVo> productList = memberLevelRuleMessage.getProducts();
            Boolean success = false;
            for (String upgradeType : memberLevelRuleMessage.getType().split(",")) {
                if(upgradeType.equals(MemberLevelRuleTypeEnum.AMOUNT.getStatus())||
                        upgradeType.equals(MemberLevelRuleTypeEnum.MEMBER_AMOUNT.getStatus())||
                        upgradeType.equals(MemberLevelRuleTypeEnum.APPOINT_GOODS.getStatus())){
                    success = true;
                }
            }
            //购买升级商品存在-并且审计方式包含消费额升级，商品消费额升级，指定商品数
            if(productList!=null&&productList.size()>0&&success){
                apiApplyRegionMemberVo.setUpgradeFlag(CommonConstants.NUMBER_ZERO);
                apiApplyRegionMemberVo.setMemberTypeId(Long.valueOf(memberType.getId()));
            }else{
                throw new ServiceException("您暂未达到升级条件！", SystemCode.DATA_NOT_EXIST_CODE);
            }
        }else{
            //达到升级区域会员条件
            apiApplyRegionMemberVo.setUpgradeFlag(CommonConstants.NUMBER_ONE);
        }
        LambdaQueryWrapper<MiniAccountApplyRegionMember>queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MiniAccountApplyRegionMember::getDeleted,CommonConstants.NUMBER_ZERO);
        queryWrapper.eq(MiniAccountApplyRegionMember::getAuditStatus,ApproveStatusEnum.AUDIT.getStatus());
        queryWrapper.eq(MiniAccountApplyRegionMember::getUserId,miniAccount.getUserId());
        queryWrapper.eq(MiniAccountApplyRegionMember::getMemberTypeId,memberType.getId());
        queryWrapper.eq(MiniAccountApplyRegionMember::getApplyMemberLevelId,apiApplyRegionMemberVo.getApplyMemberLevelId());
        if(miniAccountApplyRegionMemberService.count(queryWrapper)>0){
            throw new ServiceException("当前区域申请正在审核中，请勿重复提交！", SystemCode.DATA_NOT_EXIST_CODE);
        }
        return apiApplyRegionMemberVo;
    }

    @Override
    public List<String> getAgentRegionCodeList(Long memberTypeId) {
        return this.baseMapper.getAgentRegionCodeList(memberTypeId);
    }

    @Override
    public void applyRegionMember(ApiApplyRegionMemberDto dto) {
        CurUserDto curUser = CurUserUtil.getHttpCurUser();
        MiniAccount miniAccount = miniAccountService.getByShopUserId(curUser.getUserId());
        if (miniAccount == null) {
            throw new ServiceException("数据错误！", SystemCode.DATA_NOT_EXIST_CODE);
        }
        String regionCode = dto.getRegionCode();
        Long memberTypeId = dto.getMemberTypeId();
        Integer regionType = dto.getRegionType();
        String regionName = dto.getRegionName();
        String applyMemberLevelId = dto.getApplyMemberLevelId();
        LambdaQueryWrapper<MemberLevelRelation>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MemberLevelRelation::getAgentRegionCode,regionCode);
        wrapper.eq(MemberLevelRelation::getDeleted,CommonConstants.NUMBER_ZERO);
        int count = this.count(wrapper);
        if(count>0){
            throw new ServiceException("区域已经被其他会员申请，不能重复申请！", SystemCode.DATA_NOT_EXIST_CODE);
        }
        LambdaQueryWrapper<MiniAccountApplyRegionMember>queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MiniAccountApplyRegionMember::getDeleted,CommonConstants.NUMBER_ZERO);
        queryWrapper.eq(MiniAccountApplyRegionMember::getAuditStatus,ApproveStatusEnum.AUDIT.getStatus());
        queryWrapper.eq(MiniAccountApplyRegionMember::getUserId,miniAccount.getUserId());
        queryWrapper.eq(MiniAccountApplyRegionMember::getMemberTypeId,memberTypeId);
        queryWrapper.eq(MiniAccountApplyRegionMember::getApplyMemberLevelId,applyMemberLevelId);
        if(miniAccountApplyRegionMemberService.count(queryWrapper)>0){
            throw new ServiceException("当前区域申请正在审核中，请勿重复提交！", SystemCode.DATA_NOT_EXIST_CODE);
        }

        MiniAccountApplyRegionMember miniAccountApplyRegionMember = new MiniAccountApplyRegionMember();
        miniAccountApplyRegionMember.setUserId(miniAccount.getUserId());
        miniAccountApplyRegionMember.setMemberTypeId(memberTypeId);
        miniAccountApplyRegionMember.setApplyMemberLevelId(Long.valueOf(applyMemberLevelId));
        miniAccountApplyRegionMember.setApplyAgentRegionCode(regionCode);
        miniAccountApplyRegionMember.setAuditStatus(ApproveStatusEnum.AUDIT.getStatus());
        miniAccountApplyRegionMember.setRegionType(regionType);
        miniAccountApplyRegionMember.setApplyAgentRegionName(regionName);
        miniAccountApplyRegionMemberService.save(miniAccountApplyRegionMember);
    }

    @Override
    public Boolean getRegionMemberFlag() {
        Boolean regionFlag = false;
        CurUserDto curUser = CurUserUtil.getHttpCurUser();
        MiniAccount miniAccount = miniAccountService.getByShopUserId(curUser.getUserId());
        if (miniAccount == null) {
            throw new ServiceException("数据错误！", SystemCode.DATA_NOT_EXIST_CODE);
        }
        //1.获取区域会员类型
        MemberType memberType = memberTypeService.getRegionMemberType();
        if(memberType!=null&&memberType.getStatus() == MemberTypeStatusEnum.YES.getStatus()){
            //2.查询会员在会员等级关联表里对应区域会员类型记录是否存在
            LambdaQueryWrapper<MemberLevelRelation>wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(MemberLevelRelation::getUserId,miniAccount.getUserId());
            wrapper.eq(MemberLevelRelation::getMemberTypeId,memberType.getId());
            wrapper.isNotNull(MemberLevelRelation::getUpLevelTime);
            int count = this.count(wrapper);
            if(count>0){
                regionFlag = true;
            }
        }
        return regionFlag;
    }

    @Override
    public RegionMemberLevelVo getRegionMemberLevel() {
        RegionMemberLevelVo regionMemberLevelVo = new RegionMemberLevelVo();
        CurUserDto curUser = CurUserUtil.getHttpCurUser();
        MiniAccount miniAccount = miniAccountService.getByShopUserId(curUser.getUserId());
        if (miniAccount == null) {
            throw new ServiceException("数据错误！", SystemCode.DATA_NOT_EXIST_CODE);
        }
        //1.获取区域会员类型
        MemberType memberType = memberTypeService.getRegionMemberType();
        if(memberType == null){
            throw new ServiceException("区域会员类型不存在！", SystemCode.DATA_NOT_EXIST_CODE);
        }
        if(memberType.getStatus() == MemberTypeStatusEnum.NO.getStatus()){
            throw new ServiceException("区域会员类型已停用！", SystemCode.DATA_NOT_EXIST_CODE);
        }
        regionMemberLevelVo.setAvatarUrl(miniAccount.getAvatarUrl());
        regionMemberLevelVo.setNikeName(miniAccount.getNikeName());
        MemberLevelRelationParam memberLevelRelationParam = new MemberLevelRelationParam();
        memberLevelRelationParam.setUserId(miniAccount.getUserId());
        memberLevelRelationParam.setMemberTypeId(Long.valueOf(memberType.getId()));
        List<MemberLevelRelation> memberLevelRelationList = this.getMemberLevelRelation(memberLevelRelationParam);
        if(memberLevelRelationList!=null&&memberLevelRelationList.size()>0){
            MemberLevelRelation memberLevelRelation = memberLevelRelationList.get(0);
            regionMemberLevelVo.setMemberLevelId(Long.valueOf(memberLevelRelation.getMemberLevelId()));
            MemberLevel memberLevel = memberLevelService.getById(memberLevelRelation.getMemberLevelId());
            regionMemberLevelVo.setMemberLevelName(memberLevel.getMemberLevel());
            regionMemberLevelVo.setMemberTypeId(memberLevelRelation.getMemberTypeId());
            regionMemberLevelVo.setMemberTypeName(memberType.getName());
            regionMemberLevelVo.setAgentRegionCode(memberLevelRelation.getAgentRegionCode());
            regionMemberLevelVo.setAgentRegionName(memberLevelRelation.getAgentRegionName());
            regionMemberLevelVo.setRegionType(memberLevelRelation.getRegionType());
            MemberLevelRuleMessageParam memberLevelRuleMessageParam = new MemberLevelRuleMessageParam();
            memberLevelRuleMessageParam.setMemberTypeId(Long.valueOf(memberType.getId()));
            MemberLevelRuleMessageVo memberLevelRuleMessage = memberLevelRuleMessageService.getMemberLevelRuleMessage(memberLevelRuleMessageParam);
            if(memberLevelRuleMessage == null){
                throw new ServiceException("区域会员类型会员等级规则不存在！", SystemCode.DATA_NOT_EXIST_CODE);
            }
            if(StringUtils.isEmpty(memberLevelRuleMessage.getType())){
                throw new ServiceException("区域会员类型会员等级规则升级方式不存在！", SystemCode.DATA_NOT_EXIST_CODE);
            }
            regionMemberLevelVo.setDescription(memberLevelRuleMessage.getDescription());
        }else{
            throw new ServiceException("当前会员对应区域会员类型等级不存在！", SystemCode.DATA_NOT_EXIST_CODE);
        }
        return regionMemberLevelVo;
    }

    @Override
    public List<MemberLevelRelation> getMemberLevelRelationNotMemberLevelId(MemberLevelRelationParam param) {
        LambdaQueryWrapper<MemberLevelRelation> queryWrapper = new LambdaQueryWrapper<>();
        if(param.getMemberTypeId()!=null){
            queryWrapper.eq(MemberLevelRelation::getMemberTypeId,param.getMemberTypeId());
        }
        if(param.getMemberLevelId()!=null){
            queryWrapper.eq(MemberLevelRelation::getMemberLevelId,param.getMemberLevelId());
        }
        if(param.getUserId()!=null){
            queryWrapper.eq(MemberLevelRelation::getUserId,param.getUserId());
        }
        queryWrapper.isNotNull(MemberLevelRelation::getMemberLevelId);
        List<MemberLevelRelation> list = baseMapper.selectList(queryWrapper);
        return list;
    }

    @Override
    public String getMemberLevelIdNotMemberLevelId(MemberLevelRelationParam param) {
        String memberLevelId = "";
        List<MemberLevelRelation> list = this.getMemberLevelRelationNotMemberLevelId(param);
        if(list!=null&&list.size()>0){
            MemberLevelRelation memberLevelRelation = list.get(0);
            memberLevelId = memberLevelRelation.getMemberLevelId();
        }
        return memberLevelId;
    }

    @Override
    public MemberLevelRelation getMemberLevelRelationByCode(String countyCode, String memberTypeId) {
        LambdaQueryWrapper<MemberLevelRelation>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MemberLevelRelation::getMemberTypeId,memberTypeId);
        wrapper.eq(MemberLevelRelation::getAgentRegionCode,countyCode);
        MemberLevelRelation memberLevelRelation = this.getOne(wrapper);
        return memberLevelRelation;

    }
}
