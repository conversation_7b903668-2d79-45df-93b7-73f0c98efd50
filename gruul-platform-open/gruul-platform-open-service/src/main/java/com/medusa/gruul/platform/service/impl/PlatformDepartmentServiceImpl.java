package com.medusa.gruul.platform.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.medusa.gruul.common.core.constant.enums.SourceTypeEnum;
import com.medusa.gruul.common.core.exception.ServiceException;
import com.medusa.gruul.common.core.util.PageUtils;
import com.medusa.gruul.common.core.util.SystemCode;
import com.medusa.gruul.platform.api.entity.PlatformDepartment;
import com.medusa.gruul.platform.api.model.dto.OutPlatformDepartmentDto;
import com.medusa.gruul.platform.api.model.vo.DepartmentVo;
import com.medusa.gruul.platform.mapper.PlatformDepartmentMapper;
import com.medusa.gruul.platform.model.dto.BindingEmployeeDto;
import com.medusa.gruul.platform.model.param.DepartmentParam;
import com.medusa.gruul.platform.service.IPlatformDepartmentService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 17:43 2024/9/12
 */
@Service
public class PlatformDepartmentServiceImpl extends ServiceImpl<PlatformDepartmentMapper, PlatformDepartment>implements IPlatformDepartmentService {

    @Override
    public PlatformDepartment newAdd(OutPlatformDepartmentDto outPlatformDepartmentDto) {
        PlatformDepartment platformDepartment = new PlatformDepartment();
        if(null == outPlatformDepartmentDto.getId()){
            //新增
            int numberCount = this.count(new LambdaQueryWrapper<PlatformDepartment>()
                    .and(wrapper -> wrapper.eq(PlatformDepartment::getDeptCode, outPlatformDepartmentDto.getDeptCode()).or()
                            .eq(PlatformDepartment::getDeptFullName, outPlatformDepartmentDto.getDeptFullName()))
            );
            if(numberCount > 0){
                throw new ServiceException("部门名称或者编号已存在！", SystemCode.DATA_EXISTED.getCode());
            }
            BeanUtil.copyProperties(outPlatformDepartmentDto, platformDepartment);
            platformDepartment.setDefaultFlag(0);
            platformDepartment.setSourceType(SourceTypeEnum.OTHER.getStatus());
            this.save(platformDepartment);
        }else{
            //更新
            PlatformDepartment dbData = this.baseMapper.selectById(outPlatformDepartmentDto.getId());
            if(null == dbData){
                throw new ServiceException("id对应的记录不存在！", SystemCode.DATA_NOT_EXIST.getCode());
            }
            BeanUtil.copyProperties(outPlatformDepartmentDto, platformDepartment);
            platformDepartment.setId(dbData.getId());
            this.updateById(platformDepartment);
        }
        return platformDepartment;
    }

    @Override
    public PageUtils<DepartmentVo> searchDepartment(DepartmentParam departmentParam) {
        IPage<DepartmentVo> page = this.baseMapper.searchDepartment(new Page<DepartmentVo>(departmentParam.getCurrent(), departmentParam.getSize()),
                departmentParam);
        return new PageUtils<>(page);
    }

    @Override
    public void bindingEmployee(BindingEmployeeDto dto) {
        //部门id
        String departmentId = dto.getDepartmentId();
        //职员id
        String employeeId = dto.getEmployeeId();
        //职员名称
        String employeeFullName = dto.getEmployeeFullName();

        PlatformDepartment platformDepartment = this.baseMapper.selectById(departmentId);
        if(platformDepartment!=null){
            platformDepartment.setEmployeeId(employeeId);
            platformDepartment.setEmployeeFullName(employeeFullName);
            this.baseMapper.updateById(platformDepartment);
        }else{
            throw new ServiceException("部门不存在！");
        }
    }

    @Override
    @Transactional
    public void setDefault(Long id,String employeeId,String employeeName) {
        List<PlatformDepartment> list = this.list();
        if(list!=null&&list.size()>0){
            for (PlatformDepartment platformDepartment : list) {
                platformDepartment.setEmployeeId("");
                platformDepartment.setEmployeeFullName("");
                platformDepartment.setDefaultFlag(0);
                this.updateById(platformDepartment);
            }
        }
        PlatformDepartment department = this.getById(id);
        department.setDefaultFlag(1);
        department.setEmployeeId(employeeId);
        department.setEmployeeFullName(employeeName);
        this.updateById(department);
    }
}
