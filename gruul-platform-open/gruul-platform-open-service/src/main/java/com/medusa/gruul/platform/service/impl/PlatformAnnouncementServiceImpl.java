package com.medusa.gruul.platform.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.medusa.gruul.common.core.exception.ServiceException;
import com.medusa.gruul.common.core.util.SystemCode;
import com.medusa.gruul.platform.api.entity.PlatformAnnouncement;
import com.medusa.gruul.platform.mapper.PlatformAnnouncementMapper;
import com.medusa.gruul.platform.model.dto.PlatformAnnouncementDto;
import com.medusa.gruul.platform.model.param.PlatformAnnouncementParam;
import com.medusa.gruul.platform.model.vo.PlatformAnnouncementVo;
import com.medusa.gruul.platform.service.IPlatformAnnouncementService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2025/7.30
 */
@Service
public class PlatformAnnouncementServiceImpl extends ServiceImpl<PlatformAnnouncementMapper, PlatformAnnouncement> implements IPlatformAnnouncementService {
    @Override
    public IPage<PlatformAnnouncementVo> getAnnouncementPage(PlatformAnnouncementParam param) {
        Page<PlatformAnnouncement> page = new Page<>(param.getCurrent(), param.getSize());
        return baseMapper.getAnnouncementPage(page, param);
    }

    @Override
    public List<PlatformAnnouncementVo> getEffectiveAnnouncements() {
        PlatformAnnouncementParam param = new PlatformAnnouncementParam();
        param.setSize(Integer.MAX_VALUE);
        param.setCurrent(1);
        param.setToday(LocalDate.now());
        Page<PlatformAnnouncement> page = new Page<>(param.getCurrent(), param.getSize());
        IPage<PlatformAnnouncementVo> announcementPage = baseMapper.getAnnouncementPage(page, param);
        if (CollectionUtils.isEmpty(announcementPage.getRecords())){
            return null;
        }
        return announcementPage.getRecords();
       /* LocalDateTime now = LocalDateTime.now();
        LambdaQueryWrapper<PlatformAnnouncement> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PlatformAnnouncement::getStatus, 101) // 审核通过
                .le(PlatformAnnouncement::getEffectiveStartDate, now) // 生效开始日期小于等于当前时间
                .ge(PlatformAnnouncement::getEffectiveEndDate, now); // 生效结束时间大于等于当前时间
        List<PlatformAnnouncement> announcements = this.list(wrapper);
        // 转换为VO对象
        return announcements.stream().map(announcement -> {
            PlatformAnnouncementVo vo = new PlatformAnnouncementVo();
            BeanUtils.copyProperties(announcement, vo);
            return vo;
        }).collect(Collectors.toList());*/
    }
    @Override
    public Boolean addAnnouncement(PlatformAnnouncementDto dto) {
        PlatformAnnouncement announcement = new PlatformAnnouncement();
        ValidationData(dto, announcement);
        return this.save(announcement);
    }


    @Override
    public Boolean updateAnnouncement(PlatformAnnouncementDto dto) {
        Long id = dto.getId();
        ValidationId(id);
        PlatformAnnouncement announcement = this.getById(id);
        if (announcement == null) {
            throw new ServiceException("公告不存在", SystemCode.DATA_NOT_EXIST.getCode());
        }
        ValidationData(dto, announcement);
        return this.updateById(announcement);
    }

    @Override
    public Boolean deleteAnnouncement(Long id) {
        ValidationId(id);
        PlatformAnnouncement announcement = this.getById(id);
        if (announcement == null) {
            throw new ServiceException("公告不存在", SystemCode.DATA_NOT_EXIST.getCode());
        }
        return this.removeById(id);
    }



    @Override
    public PlatformAnnouncementVo getAnnouncement(Long id) {
        PlatformAnnouncement announcement = this.getById(id);
        if (announcement == null) {
            throw new ServiceException("公告不存在", SystemCode.DATA_NOT_EXIST.getCode());
        }
        PlatformAnnouncementVo vo = new PlatformAnnouncementVo();
        BeanUtils.copyProperties(announcement, vo);
        return vo;
    }

    private static void ValidationId(Long id) {
        if (id == null){
            throw new ServiceException("公告ID不能为空", SystemCode.PARAM_VALID_ERROR.getCode());
        }
    }
    private static void ValidationData(PlatformAnnouncementDto dto, PlatformAnnouncement announcement) {

        // 只有审核通过的公告才需要检查时间
        if (dto.getStatus() != null && dto.getStatus() == 101) {
            if (dto.getEffectiveStartDate() == null || dto.getEffectiveEndDate() == null) {
                throw new ServiceException("审核通过的公告必须设置生效时间范围", SystemCode.PARAM_VALID_ERROR.getCode());
            }
            if (dto.getEffectiveStartDate().isAfter(dto.getEffectiveEndDate())) {
                throw new ServiceException("生效开始日期不能晚于结束时间", SystemCode.PARAM_VALID_ERROR.getCode());
            }
        }
        BeanUtils.copyProperties(dto, announcement);
    }
}