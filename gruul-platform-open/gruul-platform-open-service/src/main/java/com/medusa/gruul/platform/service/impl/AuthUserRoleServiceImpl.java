package com.medusa.gruul.platform.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.medusa.gruul.common.core.exception.ServiceException;
import com.medusa.gruul.platform.api.entity.AccountInfo;
import com.medusa.gruul.platform.api.entity.AuthRoleInfo;
import com.medusa.gruul.platform.api.entity.AuthUserRole;
import com.medusa.gruul.platform.mapper.AuthUserRoleMapper;
import com.medusa.gruul.platform.model.dto.AuthUserRoleParamDto;
import com.medusa.gruul.platform.model.vo.AuthUserMenuVo;
import com.medusa.gruul.platform.service.IAccountInfoService;
import com.medusa.gruul.platform.service.IAuthRoleInfoService;
import com.medusa.gruul.platform.service.IAuthUserRoleService;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * @Author: plh
 * @Description: 用户角色服务实现类
 * @Date: Created in 19:05 2023/8/16
 */
@Service
@Log4j2
public class AuthUserRoleServiceImpl extends ServiceImpl<AuthUserRoleMapper, AuthUserRole> implements IAuthUserRoleService {

    @Autowired
    private IAccountInfoService accountInfoService;
    @Autowired
    private IAuthRoleInfoService authRoleInfoService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveAuthUserRole(AuthUserRoleParamDto authUserRoleParamDto) {
        Long userId = authUserRoleParamDto.getUserId();
        this.deleteAuthUserRole(authUserRoleParamDto);
        List<Long> roleIds = authUserRoleParamDto.getRoleIds();
        for (Long roleId  : roleIds) {
            AuthRoleInfo authRoleInfo = authRoleInfoService.getById(roleId);
            if(authRoleInfo==null){
                throw new ServiceException("角色不存在！");
            }
            AuthUserRole authUserRole = new AuthUserRole();
            authUserRole.setUserId(userId);
            authUserRole.setRoleId(roleId);
            this.baseMapper.insert(authUserRole);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteAuthUserRole(AuthUserRoleParamDto authUserRoleParamDto) {
        Long userId = authUserRoleParamDto.getUserId();
        LambdaQueryWrapper<AuthUserRole>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AuthUserRole::getUserId,userId);
        this.baseMapper.delete(wrapper);
    }

    @Override
    public List<AuthUserMenuVo> getAuthUserMenuVo(Long id) {
        return this.baseMapper.getAuthUserMenuVo(id);
    }

    @Override
    public List<AuthUserMenuVo> getAuthUserMenuSecondVo(Long id, Long menuPId) {
        return this.baseMapper.getAuthUserMenuSecondVo(id,menuPId);
    }
}
