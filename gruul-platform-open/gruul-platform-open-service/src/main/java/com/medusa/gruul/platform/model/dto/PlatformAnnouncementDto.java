package com.medusa.gruul.platform.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description
 * @date 2025/7/30
 */
@Data
@ApiModel(description = "平台公告DTO")
public class PlatformAnnouncementDto {

    @ApiModelProperty(value = "公告ID")
    private Long id;

    @NotBlank(message = "公告标题不能为空")
    @ApiModelProperty(value = "公告标题")
    private String title;

    @NotBlank(message = "公告内容不能为空")
    @ApiModelProperty(value = "公告内容（富文本）")
    private String content;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @JsonDeserialize(using = LocalDateDeserializer.class)
    @JsonSerialize(using = LocalDateSerializer.class)
    @NotNull(message = "生效开始日期不能为空")
    @ApiModelProperty(value = "生效开始日期")
    private LocalDate effectiveStartDate;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @JsonDeserialize(using = LocalDateDeserializer.class)
    @JsonSerialize(using = LocalDateSerializer.class)
    @NotNull(message = "生效结束时间不能为空")
    @ApiModelProperty(value = "生效结束时间")
    private LocalDate effectiveEndDate;

    //目前不用
    @ApiModelProperty(value = "状态:0->草稿；100->待审核；101->审核通过、生效中；200->审核不通过;")
    private Integer status;
}