package com.medusa.gruul.platform.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 11:34 2024/10/28
 */
@Data
@ApiModel(value="BindMiniAccountDto对象", description="用户绑定小程序客户dto")
public class BindMiniAccountDto {

    /**用户id*/
    @ApiModelProperty(value = "用户id")
    @NotBlank(message = "用户id不能为空")
    private String accountId;

    /**小程序用户id*/
    @ApiModelProperty(value = "小程序用户id")
    @NotBlank(message = "小程序用户id不能为空")
    private String userId;


    /**小程序用户名称*/
    @ApiModelProperty(value = "小程序用户名称")
    @NotBlank(message = "小程序用户名称不能为空")
    private String nikeName;

}
