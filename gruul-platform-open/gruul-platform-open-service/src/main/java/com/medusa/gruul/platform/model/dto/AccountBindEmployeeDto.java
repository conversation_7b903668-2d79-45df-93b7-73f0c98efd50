package com.medusa.gruul.platform.model.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 15:13 2024/10/29
 */
@Data
@ApiModel(value="AccountBindEmployeeDto对象", description="用户绑定职员部门dto")
public class AccountBindEmployeeDto {

    @ApiModelProperty(value = "用户id")
    @NotBlank(message = "用户id不能为空")
    private String accountId;

    @ApiModelProperty(value = "职员id")
    @NotBlank(message = "职员id不能为空")
    private String employeeId;

    @ApiModelProperty(value = "职员名称")
    @NotBlank(message = "职员名称不能为空")
    private String employeeName;

    @ApiModelProperty(value = "部门标识")
    private String departmentCode;

    @ApiModelProperty(value = "部门名称")
    private String departmentName;

}
