package com.medusa.gruul.account.model.param;

import com.medusa.gruul.common.core.param.QueryParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: plh
 * @Description: 分销订单param
 * @Date: Created in 16:34 2023/8/31
 */
@Data
@ApiModel(value = "DistributionOrderParam 实体", description = "分销订单param")
public class DistributionOrderParam extends QueryParam {

    /**
     * 关键字
     */
    @ApiModelProperty(value = "关键字")
    private String searchValue;

    /**
     * 店铺用户id
     */
    private String shopUserId;

}
