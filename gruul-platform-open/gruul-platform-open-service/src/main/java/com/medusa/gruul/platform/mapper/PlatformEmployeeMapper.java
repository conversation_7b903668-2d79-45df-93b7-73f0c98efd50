package com.medusa.gruul.platform.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.medusa.gruul.platform.api.entity.PlatformDepartment;
import com.medusa.gruul.platform.api.entity.PlatformEmployee;
import com.medusa.gruul.platform.api.model.vo.EmployeeVo;
import com.medusa.gruul.platform.model.param.EmployeeParam;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 17:41 2024/9/12
 */
@Repository
public interface PlatformEmployeeMapper extends BaseMapper<PlatformEmployee> {

    /**
     * 分页查询职员信息
     * @param page
     * @param params
     * @return
     */
    IPage<EmployeeVo>searchEmployee(Page<EmployeeVo> page, @Param("params")EmployeeParam params);

    /**
     * 分页查询可绑定职员信息
     * @param page
     * @param params
     * @return
     */
    IPage<EmployeeVo>searchBindEmployee(Page<EmployeeVo> page, @Param("params")EmployeeParam params);

    /**
     * 取下一个职员编号
     * @return
     */
    String getLastEmpNumber();
}
