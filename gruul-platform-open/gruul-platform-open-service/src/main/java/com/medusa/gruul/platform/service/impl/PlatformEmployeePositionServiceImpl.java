package com.medusa.gruul.platform.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.medusa.gruul.common.core.exception.ServiceException;
import com.medusa.gruul.common.core.util.CurUserUtil;
import com.medusa.gruul.common.dto.CurUserDto;
import com.medusa.gruul.platform.api.entity.PlatformEmployeePosition;
import com.medusa.gruul.platform.api.entity.PlatformPosition;
import com.medusa.gruul.platform.mapper.PlatformEmployeePositionMapper;
import com.medusa.gruul.platform.service.IPlatformEmployeePositionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 职员职位关联表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
@Service
@Slf4j
public class PlatformEmployeePositionServiceImpl extends ServiceImpl<PlatformEmployeePositionMapper, PlatformEmployeePosition> implements IPlatformEmployeePositionService {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveEmployeePositions(Long employeeId, List<Long> positionIds) {
        if (employeeId == null) {
            throw new ServiceException("职员ID不能为空");
        }

        // 先删除原有关联
        LambdaQueryWrapper<PlatformEmployeePosition> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PlatformEmployeePosition::getEmployeeId, employeeId);
        remove(queryWrapper);

        // 如果职位ID列表为空，则只删除不新增
        if (CollectionUtils.isEmpty(positionIds)) {
            return true;
        }

        // 批量新增关联
        List<PlatformEmployeePosition> list = new ArrayList<>(positionIds.size());
        for (Long positionId : positionIds) {
            PlatformEmployeePosition employeePosition = new PlatformEmployeePosition();
            employeePosition.setEmployeeId(employeeId);
            employeePosition.setPositionId(positionId);
            CurUserDto httpCurUser = CurUserUtil.getHttpCurUser();
            employeePosition.setCreateUserId(Long.parseLong(httpCurUser.getUserId()));
            employeePosition.setCreateUserName(httpCurUser.getNikeName());
            list.add(employeePosition);
        }

        return saveBatch(list);
    }

    @Override
    public List<Long> getPositionIdsByEmployeeId(Long employeeId) {
        if (employeeId == null) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<PlatformEmployeePosition> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(PlatformEmployeePosition::getPositionId)
                .eq(PlatformEmployeePosition::getEmployeeId, employeeId);
        List<PlatformEmployeePosition> list = list(queryWrapper);

        return list.stream().map(PlatformEmployeePosition::getPositionId).collect(Collectors.toList());
    }



    @Override
    public List<Long> getEmployeeIdsByPositionId(Long positionId) {
        if (positionId == null) {
            return new ArrayList<>();
        }

        LambdaQueryWrapper<PlatformEmployeePosition> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PlatformEmployeePosition::getPositionId, positionId);
        List<PlatformEmployeePosition> list = list(queryWrapper);

        return list.stream().map(PlatformEmployeePosition::getEmployeeId).collect(Collectors.toList());
    }

    @Override
    public boolean isPositionUsed(Long positionId) {
        if (positionId == null) {
            return false;
        }

        LambdaQueryWrapper<PlatformEmployeePosition> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PlatformEmployeePosition::getPositionId, positionId);

        return count(queryWrapper) > 0;
    }
}