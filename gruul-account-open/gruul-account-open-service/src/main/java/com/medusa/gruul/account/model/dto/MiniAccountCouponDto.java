package com.medusa.gruul.account.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 11:47 2024/8/27
 */
@Data
@ApiModel(value = "新增或修改用户优惠券DTO")
public class MiniAccountCouponDto {

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "用户id")
    private String userId;

    @ApiModelProperty(value = "优惠券id")
    private Long couponId;

    @ApiModelProperty(value = "状态:100->未用;101->已用;200->已失效")
    private Integer status;

    @ApiModelProperty(value = "开始时间")
    private Date startTime;

    @ApiModelProperty(value = "结束时间")
    private Date endTime;

    @ApiModelProperty(value = "订单id")
    private Long orderId;

    @ApiModelProperty(value = "免校验标识  - 领取次数")
    private Boolean notCheckFlag;

}
