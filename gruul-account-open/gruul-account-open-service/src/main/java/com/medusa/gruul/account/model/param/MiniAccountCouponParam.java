package com.medusa.gruul.account.model.param;

import com.medusa.gruul.common.core.param.QueryParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 15:41 2024/8/27
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "MiniAccountCouponParam 实体", description = "会员-优惠券 param")
@Data
public class MiniAccountCouponParam extends QueryParam {

    /**
     * 状态:100->未用;101->已用;200->已失效
     */
    @ApiModelProperty(value = "状态:100->未用;101->已用;200->已失效")
    private Integer status;
}
