package com.medusa.gruul.platform.model.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 17:25 2024/11/6
 */
@Data
@ApiModel(value="UpdateWxMessageTemplateStatusDto对象", description="微信消息模板启用/停用dto")
public class UpdateWxMessageTemplateStatusDto {

    @ApiModelProperty(value = "id")
    private Long id;

    /**
     * 模板状态->1.启用;2.停用
     */
    @ApiModelProperty(value = "模板状态->1.启用;2.停用")
    private Integer status;

}
