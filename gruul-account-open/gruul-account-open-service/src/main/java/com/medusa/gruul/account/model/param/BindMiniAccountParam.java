package com.medusa.gruul.account.model.param;

import com.medusa.gruul.common.core.param.QueryParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 10:44 2024/10/28
 */
@Data
@ApiModel(value = "BindMiniAccountParam 实体", description = "可绑定的客户param")
public class BindMiniAccountParam extends QueryParam {
    @ApiModelProperty(value = "客户名称")
    private String nikeName;
    @ApiModelProperty(value = "客户手机号")
    private String phone;
    @ApiModelProperty(value = "用户id")
    private String accountId;

}
