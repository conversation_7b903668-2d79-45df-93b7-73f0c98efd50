package com.medusa.gruul.account.model.param;

import com.medusa.gruul.common.core.param.QueryParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 11:11 2025/7/24
 */
@ApiModel(value = "RegionCommissionParam 实体", description = "区域会员佣金 param")
@Data
public class RegionCommissionParam extends QueryParam {

    @ApiModelProperty(value = "区域地址")
    private String areaName;

    @ApiModelProperty(value = "开始时间：yyyy-MM-dd")
    private String startTime;

    @ApiModelProperty(value = "结束时间：yyyy-MM-dd")
    private String endTime;

    @ApiModelProperty(value = "会员类型id")
    private String memberTypeId;

    @ApiModelProperty(value = "小程序用户id")
    private String shopUserId;

}
