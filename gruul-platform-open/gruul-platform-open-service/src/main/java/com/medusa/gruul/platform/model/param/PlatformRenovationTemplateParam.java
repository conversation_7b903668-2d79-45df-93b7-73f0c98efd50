package com.medusa.gruul.platform.model.param;

import com.medusa.gruul.common.core.param.QueryParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;


@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "PlatformRenovationTemplateParam 实体", description = "平台装修模板表请求参数实体")
public class PlatformRenovationTemplateParam extends QueryParam {


    @ApiModelProperty("id")
    private Long id;


    /**模版名称*/
    @ApiModelProperty(value = "模版名称")
    private String name;
    /**模版编号*/
    @ApiModelProperty(value = "模版编号")
    private String code;
    /**分类类型：1 系统模版 2 定制模版*/
    @ApiModelProperty(value = "分类类型：1 系统模版 2 定制模版")
    private Integer type;
    /**模版应用类型：1商城，2社区拼团，3门店版*/
    @ApiModelProperty(value = "模版应用类型：1商城，2社区拼团，3门店版")
    private Integer shopTemplateType;
    /**备注*/
    @ApiModelProperty(value = "备注")
    private String remark;

}
