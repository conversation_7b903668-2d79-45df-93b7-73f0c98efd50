package com.medusa.gruul.platform.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.medusa.gruul.common.core.util.PageUtils;
import com.medusa.gruul.platform.api.entity.AccountInfo;
import com.medusa.gruul.platform.api.entity.AuthRoleInfo;
import com.medusa.gruul.platform.model.dto.AuthRoleMessageDto;
import com.medusa.gruul.platform.model.dto.PlatformAccountInfoDto;
import com.medusa.gruul.platform.model.dto.PlatformAuthRoleInfoDto;
import com.medusa.gruul.platform.model.dto.PlatformAuthRoleInfoParamDto;
import com.medusa.gruul.platform.model.param.AuthRoleMessageParam;
import com.medusa.gruul.platform.model.vo.AuthRoleInfoVo;

import java.util.List;

/**
 *  平台角色 服务类
 *  <AUTHOR>
 *  @since 2023-08-15
 */
public interface IAuthRoleInfoService extends IService<AuthRoleInfo> {

    /**
     * 查询平台角色列表
     * @param platformAuthRoleInfoParamDto
     * @return
     */
    PageUtils searchAuthRoleInfo(PlatformAuthRoleInfoParamDto platformAuthRoleInfoParamDto);

    /**
     * 获取所有平台角色
     * @return
     */
    List<AuthRoleInfoVo> getList();

    /**
     * 添加平台角色
     * @param platformAuthRoleInfoDto
     * @return
     */
    AuthRoleInfo addAuthRoleInfo(PlatformAuthRoleInfoDto platformAuthRoleInfoDto);

    /**
     * 编辑平台用户
     * @param platformAuthRoleInfoDto
     * @return
     */
    AuthRoleInfo editAuthRoleInfo(PlatformAuthRoleInfoDto platformAuthRoleInfoDto);

    /**
     * 删除平台角色
     * @param id
     */
    void deleteById(Long id);

    /**
     * 授权角色消息类型
     * @param dto
     */
    void authRoleMessage(AuthRoleMessageDto dto);

    /**
     * 根据角色id获取消息类型
     * @param param
     * @return
     */
    List<Integer>getMessageTypeByRoleId(AuthRoleMessageParam param);
}
