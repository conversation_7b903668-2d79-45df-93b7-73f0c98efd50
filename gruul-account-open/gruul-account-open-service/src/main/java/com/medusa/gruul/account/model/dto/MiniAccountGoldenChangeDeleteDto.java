package com.medusa.gruul.account.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 16:56 2025/7/21
 */
@Data
@ApiModel(value = "删除用户金豆变更DTO")
public class MiniAccountGoldenChangeDeleteDto {

    /**
     * id
     */
    @ApiModelProperty(value = "id")
    private Long id;

}
