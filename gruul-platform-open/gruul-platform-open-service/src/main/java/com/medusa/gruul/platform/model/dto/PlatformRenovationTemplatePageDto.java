package com.medusa.gruul.platform.model.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.medusa.gruul.common.data.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;


/**
 * @Description: 平台装修模板页面表
 * @Author: jeecg-boot
 * @Date:   2023-09-06
 * @Version: V1.0
 */
@Data
@ApiModel(value="PlatformRenovationTemplatePageDto对象", description="平台装修模板页面表")
public class PlatformRenovationTemplatePageDto {
    
	/**id*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
	private Integer id;

	/**平台装修模板ID*/
    @ApiModelProperty(value = "平台装修模板ID")
	private Integer templateId;
	/**页面名称*/
    @ApiModelProperty(value = "页面名称")
	private String pageName;
	/**是否默认首页*/
    @ApiModelProperty(value = "是否默认首页")
	private String isDef;
	/**默认模板copy,控件json配置页面id对应关系*/
    @ApiModelProperty(value = "默认模板copy,控件json配置页面id对应关系")
	private String copyPluginFlag;
	/**分类页*/
    @ApiModelProperty(value = "分类页")
	private Integer type;
	/**modelId*/
    @ApiModelProperty(value = "modelId")
	private String modelId;
}
