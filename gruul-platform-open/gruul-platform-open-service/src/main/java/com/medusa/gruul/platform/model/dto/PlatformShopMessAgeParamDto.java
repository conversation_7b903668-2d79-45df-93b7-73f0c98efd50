package com.medusa.gruul.platform.model.dto;

import com.medusa.gruul.common.core.param.QueryParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: plh
 * @Description: 消息中心查询条件
 * @Date: Created in 10:55 2024/8/20
 */
@Data
public class PlatformShopMessAgeParamDto extends QueryParam {

    /**
     * 消息id
     */
    @ApiModelProperty(value = "消息id")
    private Long id;

    /**
     * 消息状态
     */
    @ApiModelProperty(value = "消息状态")
    private Integer status;
}
