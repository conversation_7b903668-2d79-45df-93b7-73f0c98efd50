package com.medusa.gruul.platform.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 10:56 2024/10/8
 */
@Data
@ApiModel(value="BindingWarehouseDto对象", description="职员绑定仓库dto")
public class BindingWarehouseDto {

    /**职员id*/
    @ApiModelProperty(value = "职员id")
    @NotBlank(message = "职员id不能为空")
    private String employeeId;


    /**仓库标识*/
    @ApiModelProperty(value = "仓库标识")
    @NotBlank(message = "仓库标识不能为空")
    private String stockCode;

    /**仓库名称*/
    @ApiModelProperty(value = "仓库名称")
    @NotBlank(message = "仓库名称不能为空")
    private String warehouseFullName;

}
