package com.medusa.gruul.account.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.medusa.gruul.account.api.entity.MiniAccountShopsApply;
import com.medusa.gruul.account.api.model.message.MiniAccountShopsApplyAddMessage;
import com.medusa.gruul.account.api.model.message.MiniAccountShopsApplyUpdateMessage;
import com.medusa.gruul.account.model.dto.MiniAccountShopsApplyDto;
import com.medusa.gruul.payment.api.model.vo.AggregationAreaVo;
import com.medusa.gruul.payment.api.model.vo.AggregationBankBranchVo;

import java.util.List;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 11:32 2025/7/31
 */
public interface IMiniAccountShopsApplyService extends IService<MiniAccountShopsApply> {
    /**
     * 验证用户商户进件信息
     */
    void vailUserMessage();

    /**
     * 获取聚合支付-区域地址
     * @return
     */
    AggregationAreaVo getAggregationAreaVo(String areaName);

    /**
     * 获取聚合支付-支行
     * @return
     */
    AggregationBankBranchVo getAggregationBankBranchVo(String cityName, String accountBranch);

    /**
     * 商户进件申请
     * @param dto
     */
    void apply(MiniAccountShopsApplyDto dto);

    /**
     * 获取网签地址
     * @return
     */
    String onlineSign(String reqNo);

    /**
     * 获取小程序进件列表
     * @return
     */
    List<MiniAccountShopsApply> getList();

    /**
     * 商户进件编辑
     * @param dto
     */
    void edit(MiniAccountShopsApplyDto dto);

    /**
     * 处理商户进件申请
     * @param message
     */
    void shopsApplyAdd(MiniAccountShopsApplyAddMessage message);
    /**
     * 处理商户进件编辑
     * @param message
     */
    void shopsApplyUpdate(MiniAccountShopsApplyUpdateMessage message);

    /**
     * 商户进件审核状态更新
     * @param miniAccountShopsApply
     */
    void updateShopsApplyStatus(MiniAccountShopsApply miniAccountShopsApply,Integer type);

    /**
     * 分账方关联
     * @param miniAccountShopsApply
     */
    void updateShopsApplyBindFlag(MiniAccountShopsApply miniAccountShopsApply);

    /**
     * 商户进件编辑任务
     */
    void updateEditBaseFlag();

    /**
     * 商户进件结算卡编辑任务
     */
    void updateEditCardFlag();

    /**
     * 商户进件申请任务
     */
    void updateApplyFlag();
}
