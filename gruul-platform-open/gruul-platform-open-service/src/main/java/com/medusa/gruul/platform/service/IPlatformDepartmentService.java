package com.medusa.gruul.platform.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.medusa.gruul.common.core.util.PageUtils;
import com.medusa.gruul.platform.api.entity.PlatformDepartment;
import com.medusa.gruul.platform.api.model.dto.OutPlatformDepartmentDto;
import com.medusa.gruul.platform.api.model.vo.DepartmentVo;
import com.medusa.gruul.platform.model.dto.BindingEmployeeDto;
import com.medusa.gruul.platform.model.param.DepartmentParam;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 17:42 2024/9/12
 */
public interface IPlatformDepartmentService extends IService<PlatformDepartment> {

    /**
     * 接收外部系统部门信息
     * @param outPlatformDepartmentDto
     * @return
     */
    PlatformDepartment newAdd(OutPlatformDepartmentDto outPlatformDepartmentDto);

    /**
     * 分页查询平台部门列表
     * @param departmentParam
     * @return
     */
    PageUtils<DepartmentVo>searchDepartment(DepartmentParam departmentParam);

    /**
     * 部门绑定经手人
     * @param dto
     */
    void bindingEmployee(BindingEmployeeDto dto);

    /**
     * 设置默认仓库
     * @param id
     * @param employeeId
     * @param employeeName
     */
    void setDefault(Long id,String employeeId,String employeeName);
}
