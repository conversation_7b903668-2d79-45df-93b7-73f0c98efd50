package com.medusa.gruul.platform.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.medusa.gruul.platform.api.entity.PlatformAnnouncement;
import com.medusa.gruul.platform.model.dto.PlatformAnnouncementDto;
import com.medusa.gruul.platform.model.param.PlatformAnnouncementParam;
import com.medusa.gruul.platform.model.vo.PlatformAnnouncementVo;

import java.util.List;

/**
 * <AUTHOR>
 * @description 公告
 * @date 2025/7.30
 */
public interface IPlatformAnnouncementService extends IService<PlatformAnnouncement> {

    /**
     * 添加公告
     * @param dto 公告DTO
     * @return Boolean
     */
    Boolean addAnnouncement(PlatformAnnouncementDto dto);

    /**
     * 更新公告
     * @param dto 公告DTO
     * @return Boolean
     */
    Boolean updateAnnouncement(PlatformAnnouncementDto dto);

    /**
     * 删除公告
     * @param id 公告ID
     * @return Boolean
     */
    Boolean deleteAnnouncement(Long id);

    /**
     * 获取公告详情
     * @param id 公告ID
     * @return 公告详情VO
     */
    PlatformAnnouncementVo getAnnouncement(Long id);

    /**
     * 分页获取公告列表
     * @param param 查询参数
     * @return 公告分页数据
     */
    IPage<PlatformAnnouncementVo> getAnnouncementPage(PlatformAnnouncementParam param);

    /**
     * 获取当前有效期内的公告列表
     * @return 有效期内的公告列表
     */
    List<PlatformAnnouncementVo> getEffectiveAnnouncements();
}