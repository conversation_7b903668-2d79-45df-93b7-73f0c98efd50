<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.medusa.gruul.platform.mapper.ReservationOrderMapper">

    <!-- 添加 BaseResultMap -->
    <resultMap id="BaseResultMap" type="com.medusa.gruul.platform.api.entity.ReservationOrder">
        <id column="id" property="id"  />
        <result column="tenant_id" property="tenantId"  />
        <result column="shop_id" property="shopId"  />
        <result column="user_id" property="userId"  />
        <result column="user_name" property="userName"  />
        <result column="mobile" property="mobile"  />
        <result column="reservation_number" property="reservationNumber"  />
        <result column="reservation_time_start" property="reservationTimeStart"  />
        <result column="reservation_time_end" property="reservationTimeEnd"  />
        <result column="service_id" property="serviceId"  />
        <result column="employee_id" property="employeeId"  />
        <result column="content" property="content"  />
        <result column="status" property="status"  />
        <result column="create_user_id" property="createUserId"  />
        <result column="create_user_name" property="createUserName"  />
        <result column="create_time" property="createTime"  />
        <result column="last_modify_user_id" property="lastModifyUserId"  />
        <result column="last_modify_user_name" property="lastModifyUserName"  />
        <result column="update_time" property="updateTime"  />
        <result column="is_deleted" property="deleted"  />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, tenant_id, shop_id, user_id, user_name, mobile, reservation_number,
        reservation_time_start, reservation_time_end, service_id, employee_id, content, status,
        create_user_id, create_user_name, create_time, last_modify_user_id, last_modify_user_name, update_time, is_deleted
    </sql>


    <select id="listPage" resultType="com.medusa.gruul.platform.model.vo.ReservationOrderVo">
        SELECT
        ro.id,
        ro.user_id,
        ro.user_name,
        ro.mobile,
        ro.reservation_number,
        ro.reservation_time_start,
        ro.reservation_time_end,
        ro.service_id,
        sc.service_name,
        ro.employee_id,
        CASE
        WHEN ro.employee_id = 0 THEN  '到店分配'
        ELSE pe.emp_full_name
        END AS employee_name,
        ro.content,
        ro.status
        FROM t_reservation_order ro
        LEFT JOIN t_platform_service_content sc ON ro.service_id = sc.id
        LEFT JOIN t_platform_lyd_employee pe ON ro.employee_id = pe.id
        where ro.is_deleted = 0
        <if test="params.serviceId != null and params.serviceId != ''">
            AND ro.service_id = #{params.serviceId}
        </if>
        <if test="params.serviceName != null and params.serviceName != ''">
            AND sc.service_name like CONCAT('%',#{params.serviceName},'%')
        </if>
        <if test="params.status != null">
            AND ro.status = #{params.status}
        </if>
        <if test="params.employeeId != null and params.employeeId != '' ">
            AND ro.employee_id= #{params.employeeId}
        </if>
        <if test="params.employeeName != null and params.employeeName != '' ">
            AND pe.emp_full_name  like  CONCAT('%',#{params.employeeName},'%')
        </if>
        <if test="params.userId != null and params.userId != '' ">
            AND ro.user_id= #{params.userId}
        </if>
        <if test="params.mobile != null and params.mobile != '' ">
            AND ro.mobile like  CONCAT('%',#{params.mobile},'%')
        </if>
        <if test="params.userName != null and params.userName != '' ">
            AND ro.user_name like  CONCAT('%',#{params.userName},'%')
        </if>
        <if test="params.reservationTimeStartBegin != null and params.reservationTimeStartEnd != '' ">
            AND (
            (reservation_time_start &lt;= #{startTime} AND reservation_time_end &gt; #{startTime})
            OR (reservation_time_start &lt; #{endTime} AND reservation_time_end &gt;= #{endTime})
            OR (reservation_time_start &gt;= #{startTime} AND reservation_time_end &lt;= #{endTime})
            )
        </if>
        ORDER BY ro.id DESC
    </select>

    <!-- 查询指定时间段内职员的预约数 -->
    <select id="countEmployeeReservationInTimeRange" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM t_reservation_order
        WHERE is_deleted = 0 and employee_id = #{employeeId}
          AND (
            (reservation_time_start &lt;= #{startTime} AND reservation_time_end &gt; #{startTime})
                OR (reservation_time_start &lt; #{endTime} AND reservation_time_end &gt;= #{endTime})
                OR (reservation_time_start &gt;= #{startTime} AND reservation_time_end &lt;= #{endTime})
            )
          AND status IN (101, 104)
    </select>

    <!-- 查询指定时间段内所有上班职员的预约情况 -->
    <select id="listBookedEmployeeIds" resultType="java.lang.Long">
        SELECT DISTINCT ro.employee_id
        FROM t_reservation_order ro
                 JOIN t_platform_lyd_employee e ON ro.employee_id = e.id
        WHERE ro.is_deleted = 0 and e.work_status = 1
          AND (
            (ro.reservation_time_start &lt;= #{startTime} AND ro.reservation_time_end &gt; #{startTime})
                OR (ro.reservation_time_start &lt; #{endTime} AND ro.reservation_time_end &gt;= #{endTime})
                OR (ro.reservation_time_start &gt;= #{startTime} AND ro.reservation_time_end &lt;= #{endTime})
            )
          AND ro.status IN (101, 104)
    </select>

    <!-- 查询用户在指定时间段内的预约数 -->
    <select id="countUserReservationInTimeRange" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM t_reservation_order
        WHERE is_deleted = 0 and user_id = #{userId}
          AND (
            (reservation_time_start &lt;= #{startTime} AND reservation_time_end &gt; #{startTime})
                OR (reservation_time_start &lt; #{endTime} AND reservation_time_end &gt;= #{endTime})
                OR (reservation_time_start &gt;= #{startTime} AND reservation_time_end &lt;= #{endTime})
            )
          AND status IN (101, 104)
    </select>

    <!-- 统计职员的好评数（评价满意的预约单数量） -->
    <select id="countEmployeeGoodComments" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM t_reservation_order o JOIN t_reservation_order_evaluate e ON o.id = e.reservation_order_id
        WHERE o.is_deleted = 0 and o.employee_id = #{employeeId} and e.rate >= 3
    </select>

    <!-- 统计职员的服务人数（待评价和已完成状态的预约单数量） -->
    <select id="countEmployeeServiceNumber" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM t_reservation_order
        WHERE is_deleted = 0 and employee_id = #{employeeId}
          AND status IN (104, 105)
    </select>

    <!-- 查询用户的预约列表 -->
    <select id="getUserReservations" resultType="com.medusa.gruul.platform.model.vo.ReservationOrderVo">
        SELECT
        ro.id,
        ro.shop_id,
        ro.user_id,
        ro.user_name,
        ro.mobile,
        ro.reservation_number,
        ro.reservation_time_start,
        ro.reservation_time_end,
        ro.service_id,
        sc.service_name,
        ro.employee_id,
        CASE
        WHEN ro.employee_id = 0 THEN  '到店分配'
        ELSE pe.emp_full_name
        END AS employee_name,
        ro.content,
        ro.status,
        ro.create_time,
        ro.create_user_id,
        ro.create_user_name,
        ro.update_time,
        ro.last_modify_user_id,
        ro.last_modify_user_name,
        sp.name as shop_name
        FROM t_reservation_order ro
        LEFT JOIN t_platform_service_content sc ON ro.service_id = sc.id
        LEFT JOIN t_platform_lyd_employee pe ON ro.employee_id = pe.id
        left join t_shops_partner sp on sp.shop_id = ro.shop_id
        WHERE ro.is_deleted = 0
        <if test="params.userId != null">
            AND ro.user_id =  #{params.userId}
        </if>
        <if test="params.status != null">
            AND ro.status = #{params.status}
        </if>
        <if test="params.keyword != null and params.keyword != ''">
            AND (sc.service_name LIKE CONCAT('%',#{params.keyword},'%')
            or pe.emp_full_name LIKE CONCAT('%',#{params.keyword},'%')
            or sp.name LIKE CONCAT('%',#{params.keyword},'%')
                )
        </if>
        <if test="params.reservationTimeStartBegin != null and params.reservationTimeStartEnd != null">
            AND (ro.reservation_time_start &gt;= #{params.reservationTimeStartBegin}
                    AND ro.reservation_time_end &lt;= #{params.reservationTimeStartEnd} )
        </if>
        ORDER BY ro.id DESC
    </select>

    <!-- 查询预约详情 -->
    <select id="getReservationDetail" resultType="com.medusa.gruul.platform.model.vo.ReservationOrderVo">
        SELECT
            ro.id,
            ro.user_id,
            ro.shop_id,
            ro.user_name,
            ro.mobile,
            ro.reservation_number,
            ro.reservation_time_start,
            ro.reservation_time_end,
            ro.service_id,
            sc.service_name,
            ro.employee_id,
            CASE WHEN ro.employee_id is not null THEN pe.emp_full_name ELSE '到店分配' END AS employee_name,
            ro.content,
            ro.status,
            ro.create_time,
            ro.create_user_id,
            ro.create_user_name,
            ro.update_time,
            ro.last_modify_user_id,
            ro.last_modify_user_name
        FROM t_reservation_order ro
        LEFT JOIN t_platform_service_content sc ON ro.service_id = sc.id
        LEFT JOIN t_platform_lyd_employee pe ON ro.employee_id = pe.id
        WHERE ro.is_deleted = 0 AND ro.id = #{id}
    </select>

</mapper>