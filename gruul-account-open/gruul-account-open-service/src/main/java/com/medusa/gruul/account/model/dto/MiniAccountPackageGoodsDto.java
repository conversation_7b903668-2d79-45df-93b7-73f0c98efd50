package com.medusa.gruul.account.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 17:18 2024/9/10
 */
@Data
@ApiModel(value = "新增或修改用户权益包商品验证码DTO")
public class MiniAccountPackageGoodsDto {

    @ApiModelProperty(value = "用户权益包商品id")
    private Long miniAccountPackageGoodsId;

    @ApiModelProperty(value = "核销商品id")
    private Long verifyGoodsId;

    @ApiModelProperty(value = "核销商品规格id")
    private Long verifySkuId;

}
