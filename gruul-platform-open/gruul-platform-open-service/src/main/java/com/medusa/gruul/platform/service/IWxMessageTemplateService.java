package com.medusa.gruul.platform.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.medusa.gruul.common.core.util.PageUtils;
import com.medusa.gruul.platform.api.entity.WxMessageTemplate;
import com.medusa.gruul.platform.api.model.dto.WxSendMessageDto;
import com.medusa.gruul.platform.model.dto.ApiWxMiniTemplateDto;
import com.medusa.gruul.platform.model.dto.UpdateWxMessageTemplateStatusDto;
import com.medusa.gruul.platform.model.dto.WxMessageTemplateDto;
import com.medusa.gruul.platform.model.param.WxMessageTemplateParam;
import com.medusa.gruul.platform.api.model.vo.WxMessageTemplateVo;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 15:58 2024/11/6
 */
public interface IWxMessageTemplateService extends IService<WxMessageTemplate> {

    /**
     * 获取微信消息模板信息
     * @param param
     * @return
     */
    PageUtils<WxMessageTemplateVo>getWxMessageTemplate(WxMessageTemplateParam param);

    /**
     * 获取微信消息模板详情
     * @param id
     * @return
     */
    WxMessageTemplateVo getWxMessageTemplateDetail(String id);

    /**
     * 添加微信消息模板
     * @param wxMessageTemplateDto
     */
    void addWxMessageTemplate(WxMessageTemplateDto wxMessageTemplateDto);

    /**
     * 删除微信消息模板
     * @param wxMessageTemplateDto
     */
    void deleteWxMessageTemplate(WxMessageTemplateDto wxMessageTemplateDto);

    /**
     * 编辑微信消息模板
     * @param wxMessageTemplateDto
     */
    void updateWxMessageTemplate(WxMessageTemplateDto wxMessageTemplateDto);

    /**
     * 微信消息模板启用/停用
     * @param updateWxMessageTemplateStatusDto
     */
    void updateWxMessageTemplateStatus(UpdateWxMessageTemplateStatusDto updateWxMessageTemplateStatusDto);

    /**
     * 小程序订阅消息推送
     * @param wxSendMessageDto
     */
    void wxSendMessage(WxSendMessageDto wxSendMessageDto);

    /**
     * 小程序模板
     * @param wxSendMessageDto
     */
    void wxMpSendMessage(WxSendMessageDto wxSendMessageDto);

    /**
     * 通过标识获取微信消息模板信息
     * @param code
     * @return
     */
    WxMessageTemplateVo getWxMessageTemplateByCode(String code);

    /**
     * 获取微信订阅消息模板
     * @return
     */
    ApiWxMiniTemplateDto getApiWxMiniTemplate(Integer type);
}
