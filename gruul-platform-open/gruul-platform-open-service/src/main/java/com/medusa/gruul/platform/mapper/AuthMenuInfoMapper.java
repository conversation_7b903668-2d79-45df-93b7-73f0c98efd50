package com.medusa.gruul.platform.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.medusa.gruul.platform.api.entity.AuthMenuInfo;
import com.medusa.gruul.platform.api.model.vo.AuthMenuInfoVo;

import java.util.List;
/**
 * @Author: plh
 * @Description: 系统菜单Mapper类
 * @Date: Created in 14:07 2023/8/16
 */
public interface AuthMenuInfoMapper extends BaseMapper<AuthMenuInfo> {

    /**
     * 获取所有菜单信息
     *
     * @return 所有展示分类list信息
     */
    List<AuthMenuInfoVo> queryAllAuthMenuInfoList();
}

