package com.medusa.gruul.account.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.medusa.gruul.account.api.entity.*;
import com.medusa.gruul.account.api.feign.RemoteMiniAccountService;
import com.medusa.gruul.account.api.model.AccountCouponDto;
import com.medusa.gruul.account.api.model.AccountInfoDto;
import com.medusa.gruul.account.api.model.param.MiniAccountCouponSearchParam;
import com.medusa.gruul.account.api.model.vo.*;
import com.medusa.gruul.account.mapper.MiniAccountCouponMapper;
import com.medusa.gruul.account.api.model.MiniAccountCouponByOrderDto;
import com.medusa.gruul.account.model.dto.MiniAccountCouponDto;
import com.medusa.gruul.account.api.model.MiniAccountCouponOrderItemDto;
import com.medusa.gruul.account.model.param.MiniAccountCouponParam;
import com.medusa.gruul.account.service.*;
import com.medusa.gruul.common.core.constant.CommonConstants;
import com.medusa.gruul.common.core.constant.enums.ApproveStatusEnum;
import com.medusa.gruul.common.core.exception.ServiceException;
import com.medusa.gruul.common.core.util.CurUserUtil;
import com.medusa.gruul.common.core.util.HuToolExcelUtils;
import com.medusa.gruul.common.core.util.SystemCode;
import com.medusa.gruul.common.data.tenant.ShopContextHolder;
import com.medusa.gruul.common.data.tenant.TenantContextHolder;
import com.medusa.gruul.common.dto.CurUserDto;
import com.medusa.gruul.goods.api.entity.MemberLevelGoodsAgainPrice;
import com.medusa.gruul.goods.api.entity.MemberLevelGoodsPrice;
import com.medusa.gruul.goods.api.entity.SkuStock;
import com.medusa.gruul.goods.api.feign.RemoteGoodsService;
import com.medusa.gruul.goods.api.model.dto.api.MiniOrderCouponDto;
import com.medusa.gruul.goods.api.model.vo.manager.ItemVo;
import com.medusa.gruul.goods.api.model.vo.manager.ProductVo;
import com.medusa.gruul.order.api.enums.MemberPriceTypeEnum;
import com.medusa.gruul.order.api.feign.RemoteOrderService;
import com.medusa.gruul.shops.api.entity.*;
import com.medusa.gruul.shops.api.enums.*;
import com.medusa.gruul.shops.api.feign.RemoteShopsService;
import com.medusa.gruul.shops.api.model.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 11:41 2024/8/27
 */
@Service(value = "miniAccountCouponServiceImpl")
@Slf4j
public class MiniAccountCouponServiceImpl extends ServiceImpl<MiniAccountCouponMapper, MiniAccountCoupon> implements IMiniAccountCouponService {

    @Autowired
    private RemoteShopsService remoteShopsService;

    @Autowired
    private RemoteGoodsService remoteGoodsService;

    @Autowired
    private RemoteMiniAccountService remoteMiniAccountService;

    @Autowired
    private IMiniAccountService miniAccountService;

    @Autowired
    private IMiniAccountExtendsService miniAccountExtendsService;

    @Autowired
    private IMiniAccountTagGroupService miniAccountTagGroupService;

    @Autowired
    private RemoteOrderService remoteOrderService;

    @Autowired
    private IMemberLevelRelationService memberLevelRelationService;


    @Override
    public MiniAccountCoupon add(MiniAccountCouponDto miniAccountCouponDto) {

        MiniAccountCoupon miniAccountCoupon = new MiniAccountCoupon();
        //优惠券id
        Long couponId = miniAccountCouponDto.getCouponId();
        // 非shop_user_id，是miniAccount的user_id
        String userId = null;
        if (miniAccountCouponDto.getUserId()==null){
            userId = CurUserUtil.getMiniReqeustAccountInfo().getUserId();
        }else {
            userId = miniAccountCouponDto.getUserId();
        }
        MiniAccountExtends miniAccountExtends = miniAccountExtendsService.findByUserId(userId);

        if(miniAccountExtends==null){
            throw new ServiceException("小程序用户信息不存在", SystemCode.DATA_NOT_EXIST_CODE);
        }

        // 查询优惠券的开始时间和结束时间
        AccountCouponVo shopCoupon = remoteShopsService.getCouponById(couponId);
        if(null == shopCoupon){
            throw new ServiceException("优惠券记录不存在", SystemCode.DATA_NOT_EXIST_CODE);
        }

        //优惠券领取次数
        Integer receiveTimes = shopCoupon.getReceiveTimes();
        LambdaQueryWrapper<MiniAccountCoupon>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MiniAccountCoupon::getCouponId,couponId);
        wrapper.eq(MiniAccountCoupon::getUserId,miniAccountExtends.getShopUserId());
        List<MiniAccountCoupon> list = this.list(wrapper);
        // todo +免校验次数
        if(list!=null&&list.size()>=receiveTimes && !miniAccountCouponDto.getNotCheckFlag()){
            throw new ServiceException("该优惠券已领取"+list.size()+"次，不能再领取", SystemCode.DATA_NOT_EXIST_CODE);
        }
        miniAccountCoupon.setStartTime(shopCoupon.getStartTime());
        miniAccountCoupon.setEndTime(shopCoupon.getEndTime());
        Date newDate = new Date();
        Date startTime = shopCoupon.getStartTime();
        Date endTime = shopCoupon.getEndTime();
        //根据使用时间范围判断优惠卷是未用还是失效状态
        miniAccountCoupon.setStatus(PromotionStatusEnum.EXPIRED.getStatus());
        if(newDate.getTime()>=startTime.getTime()&&newDate.getTime()<=endTime.getTime()){
            miniAccountCoupon.setStatus(PromotionStatusEnum.UN_USE.getStatus());
        }
        miniAccountCoupon.setUserId(Long.valueOf(miniAccountExtends.getShopUserId()));
        miniAccountCoupon.setCouponId(shopCoupon.getId());
        miniAccountCoupon.setCouponName(shopCoupon.getCouponName());
        miniAccountCoupon.setPromotion(shopCoupon.getPromotion());
        boolean success = this.save(miniAccountCoupon);
        return success ? miniAccountCoupon : null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer getMiniAccountCouponNum() {
        String userId = CurUserUtil.getMiniReqeustAccountInfo().getUserId();
        MiniAccountExtends miniAccountExtends = miniAccountExtendsService.findByUserId(userId);
        if(miniAccountExtends==null){
            throw new ServiceException("小程序用户信息不存在", SystemCode.DATA_NOT_EXIST_CODE);
        }
        //更新未用，失效优惠券状态
        LambdaQueryWrapper<MiniAccountCoupon>lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.ne(MiniAccountCoupon::getStatus,PromotionStatusEnum.USED.getStatus());
        List<MiniAccountCoupon> list = this.list(lambdaQueryWrapper);
        if(list!=null&&list.size()>0){
            for (MiniAccountCoupon miniAccountCoupon : list) {
                //判断平台优惠券状态是否未生效
                AccountCouponVo shopCoupon = remoteShopsService.getCouponById(miniAccountCoupon.getCouponId());
                Integer status = shopCoupon.getStatus();
                if(status!=ApproveStatusEnum.APPROVED.getStatus()){
                    miniAccountCoupon.setStatus(PromotionStatusEnum.EXPIRED.getStatus());
                }else{
                    Date startTime = miniAccountCoupon.getStartTime();
                    Date endTime = miniAccountCoupon.getEndTime();
                    Date newDate = new Date();
                    if(newDate.getTime()>=startTime.getTime()&&newDate.getTime()<=endTime.getTime()){
                        miniAccountCoupon.setStatus(PromotionStatusEnum.UN_USE.getStatus());
                    }else{
                        miniAccountCoupon.setStatus(PromotionStatusEnum.EXPIRED.getStatus());
                    }
                }
                this.updateById(miniAccountCoupon);
            }
        }
        LambdaQueryWrapper<MiniAccountCoupon>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MiniAccountCoupon::getUserId,miniAccountExtends.getShopUserId());
        wrapper.eq(MiniAccountCoupon::getStatus,PromotionStatusEnum.UN_USE.getStatus());
        int count = this.count(wrapper);
        return count;
    }

    @Override
    public Integer getMiniAccountCouponNumByCouponId(Long couponId,String userId) {

        MiniAccountExtends miniAccountExtends = miniAccountExtendsService.findByUserId(userId);
        if(miniAccountExtends==null){
            throw new ServiceException("小程序用户信息不存在", SystemCode.DATA_NOT_EXIST_CODE);
        }
        LambdaQueryWrapper<MiniAccountCoupon>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MiniAccountCoupon::getUserId,miniAccountExtends.getShopUserId());
        wrapper.eq(MiniAccountCoupon::getCouponId,couponId);
        int count = this.count(wrapper);
        return count;
    }

    @Override
    public IPage<MiniAccountCouponVo> pageMyCoupon(MiniAccountCouponParam param) {
        String userId = CurUserUtil.getHttpCurUser().getUserId();
        LambdaQueryWrapper<MiniAccountCoupon> wrapper = new LambdaQueryWrapper<>();

        wrapper.eq(MiniAccountCoupon::getUserId, userId).eq(MiniAccountCoupon::getStatus, param.getStatus())
                .orderByAsc(MiniAccountCoupon::getEndTime);
        IPage<MiniAccountCoupon> pageAccountCoupon = this.page(new Page(param.getCurrent(),param.getSize()), wrapper);
        IPage<MiniAccountCouponVo> pageShopCouponVo = new Page<>();
        pageShopCouponVo.setCurrent(pageAccountCoupon.getCurrent());
        pageShopCouponVo.setSize(pageAccountCoupon.getSize());
        pageShopCouponVo.setPages(pageAccountCoupon.getPages());
        pageShopCouponVo.setTotal(pageAccountCoupon.getTotal());
        List<Long> couponIds = pageAccountCoupon.getRecords().stream().map(MiniAccountCoupon::getCouponId).collect(Collectors.toList());
        if(CollectionUtil.isEmpty(couponIds)){
            return pageShopCouponVo;
        }
        List<ShopCoupon> shopCouponList = this.remoteShopsService.getCouponByIds(couponIds);
        Map<Long, ShopCoupon> couponMap = shopCouponList.stream().collect(Collectors.toMap(ShopCoupon::getId, e -> e, (key1, key2) -> key2));
        List<MiniAccountCouponVo> voList = new ArrayList<>(pageAccountCoupon.getRecords().size());
        pageAccountCoupon.getRecords().forEach(e -> {
            MiniAccountCouponVo vo = new MiniAccountCouponVo();
            if(couponMap.get(e.getCouponId()) != null){
                BeanUtil.copyProperties(couponMap.get(e.getCouponId()), vo);
            }
            vo.setMiniAccountCouponId(e.getId());
            vo.setEndTime(e.getEndTime());
            vo.setCouPonId(e.getCouponId());
            voList.add(vo);
        });
        pageShopCouponVo.setRecords(voList);
        return pageShopCouponVo;
    }

    /**
     * 获取用户可用优惠券，过期方法，用新方法逻辑实现getCouponByUser
     * @param miniAccountCouponByOrderDto
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @Deprecated
    public List<MiniAccountCouponByOrderVo> getCouponByUser2(MiniAccountCouponByOrderDto miniAccountCouponByOrderDto) {

        String shopUserId = "";
        if(miniAccountCouponByOrderDto.getReplaceCreateOrderFlag()!=null&&miniAccountCouponByOrderDto.getReplaceCreateOrderFlag() == CommonConstants.NUMBER_ONE){

            if(StringUtils.isEmpty(miniAccountCouponByOrderDto.getUserId())){
                throw new ServiceException("代下单需要认证用户信息！");
            }

            MiniAccountExtends miniAccountExtends = miniAccountExtendsService.findByUserId(miniAccountCouponByOrderDto.getUserId());
            if(miniAccountExtends == null){
                throw new ServiceException("代下单用户信息不存在！");
            }
            shopUserId = miniAccountExtends.getShopUserId();
        }else{
            if(StringUtils.isNotEmpty(miniAccountCouponByOrderDto.getUserId())){
                MiniAccountExtends miniAccountExtends = miniAccountExtendsService.findByUserId(miniAccountCouponByOrderDto.getUserId());
                if(miniAccountExtends == null){
                    throw new ServiceException("用户信息不存在！");
                }
                shopUserId = miniAccountExtends.getShopUserId();
            }else{
                CurUserDto curUserDto = CurUserUtil.getHttpCurUser();
                if (ObjectUtil.isNull(curUserDto)) {
                    throw new ServiceException(SystemCode.UNAUTHORIZED);
                }
                shopUserId = curUserDto.getUserId();
                log.info("当前用户信息:" + curUserDto.toString());
            }

        }


        //查询用户持有的积分、收货地址
        //AccountInfoDto accountInfoDto = remoteMiniAccountService.accountInfo(shopUserId, Arrays.asList(1,2,3, 5));
        //会员价-- 不能一次查出所有
        //List<MemberLevelGoodsPrice> memberLevelGoodsPriceList = remoteGoodsService.selectMemberGoodsPrice(accountInfoDto.getMiniAccountunt().getMemberLevelId());
        //复购价-- 不能一次查出所有
        //List<MemberLevelGoodsAgainPrice> memberLevelGoodsAgainPriceList = remoteGoodsService.selectMemberGoodsAgainPrice(accountInfoDto.getMiniAccountunt().getMemberLevelId());
        // 查出所有会员等级
        //List<MemberLevelRelation> memberLevelRelations = this.memberLevelRelationService.getMemberLevelsByUserId(miniAccountCouponByOrderDto.getUserId());
        // 将会员等级映射成会员类型和会员等级记录关系，方便查找
        //Map<Long, List<MemberLevelRelation>> memberLevelRelationMap = memberLevelRelations.stream().collect(Collectors.groupingBy(MemberLevelRelation::getMemberTypeId));

        List<MiniAccountCouponByOrderVo> dataList = new ArrayList<>();
        //更新未用，失效优惠券状态（只更新自己的）
        LambdaQueryWrapper<MiniAccountCoupon>lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.ne(MiniAccountCoupon::getStatus,PromotionStatusEnum.USED.getStatus());
        lambdaQueryWrapper.eq(MiniAccountCoupon::getUserId,shopUserId);
        List<MiniAccountCoupon> miniAccountCouponList = this.list(lambdaQueryWrapper);
        if(miniAccountCouponList!=null&&miniAccountCouponList.size()>0){
            for (MiniAccountCoupon miniAccountCoupon : miniAccountCouponList) {
                //判断平台优惠券状态是否未生效
                AccountCouponVo shopCoupon = remoteShopsService.getCouponById(miniAccountCoupon.getCouponId());
                Integer status = shopCoupon.getStatus();
                if(status!=ApproveStatusEnum.APPROVED.getStatus()){
                    miniAccountCoupon.setStatus(PromotionStatusEnum.EXPIRED.getStatus());
                }else{
                    Date startTime = miniAccountCoupon.getStartTime();
                    Date endTime = miniAccountCoupon.getEndTime();
                    Date newDate = new Date();
                    if(newDate.getTime()>=startTime.getTime()&&newDate.getTime()<=endTime.getTime()){
                        miniAccountCoupon.setStatus(PromotionStatusEnum.UN_USE.getStatus());
                    }else{
                        miniAccountCoupon.setStatus(PromotionStatusEnum.EXPIRED.getStatus());
                    }
                }
                this.updateById(miniAccountCoupon);
            }
        }

        //1.查询未用的优惠券
        LambdaQueryWrapper<MiniAccountCoupon>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MiniAccountCoupon::getUserId,shopUserId);
        wrapper.eq(MiniAccountCoupon::getStatus,PromotionStatusEnum.UN_USE.getStatus());
        wrapper.eq(null != miniAccountCouponByOrderDto.getMiniAccountCouponId(), MiniAccountCoupon::getId, miniAccountCouponByOrderDto.getMiniAccountCouponId());
        wrapper.orderByDesc(MiniAccountCoupon::getPromotion);
        List<MiniAccountCoupon> list = this.list(wrapper);
        //2.判断优惠券是否可用
        if(list!=null&&list.size()>0){
            for (MiniAccountCoupon miniAccountCoupon : list) {
                Long couponId = miniAccountCoupon.getCouponId();
                AccountCouponVo shopCoupon = remoteShopsService.getCouponById(couponId);
                Integer couponType = shopCoupon.getCouponType();
                BigDecimal totalAmount = BigDecimal.ZERO;

                //是否指定商家
                Boolean shopFlag = shopCoupon.getShopFlag();
                //指定商家id
                List<String> shopIds = shopCoupon.getShopIds();

                //1.新人券，普通券
                if(couponType == CouponTypeEnum.GENERAL.getType()
                        || couponType == CouponTypeEnum.NEW_PEOPLE.getType()
                        || couponType == CouponTypeEnum.FULL_RETURN.getType()){

                    for (MiniAccountCouponOrderItemDto dto : miniAccountCouponByOrderDto.getData()) {
                        //会员类型id
                        Long memberTypeId = dto.getMemberTypeId();
                        //若商品传入的会员类型为空，则将默认会员等级所属的会员类型作为传入的会员类型
                        if(memberTypeId == null){
                            //获取默认会员等级
                            MemberLevel memberLevel = remoteMiniAccountService.getDefaultMemberLevel();
                            if(memberLevel == null){
                                throw new ServiceException("默认会员等级为空！");
                            }
                            memberTypeId = memberLevel.getMemberTypeId();
                            // 设置默认会员类型
                            dto.setMemberTypeId(memberTypeId);
                        }
                        //根据会员类型获取会员等级ID
                        String memberLevelId = remoteMiniAccountService.getMemberLevelId(miniAccountCouponByOrderDto.getUserId(), memberTypeId);
                        if(StrUtil.isEmpty(memberLevelId)){
                            memberLevelId = "0";
                        }

                        BigDecimal number = dto.getNumber();
                        String skuId = dto.getSkuId();
                        SkuStock skuStock = remoteGoodsService.findSkuStockById(Long.valueOf(skuId));
                        ProductVo product = remoteGoodsService.findProductById(Long.valueOf(skuStock.getProductId()));

                        String shopId = skuStock.getShopId();

                        //是否满足优惠券条件
                        Boolean b = false;
                        if(shopFlag){
                            if(shopIds!=null&&shopIds.contains(shopId)){
                                b = true;
                            }else{
                                b = false;
                            }
                        }else{
                            b = true;
                        }
                        //符合商家
                        if(b){
                            BigDecimal price = skuStock.getPrice();
                            // 判断首单
                            Integer count = remoteOrderService.getCountByMemberType(shopUserId,dto.getMemberTypeId());
                            if(count == 0){
                                List<MemberLevelGoodsPrice> memberLevelGoodsPricesList = this.remoteGoodsService.selectMemberGoodsPriceByProductId(memberLevelId, skuStock.getProductId(), skuStock.getId());
                                if(null != memberLevelGoodsPricesList && !memberLevelGoodsPricesList.isEmpty()){
                                    if (memberLevelGoodsPricesList.get(0).getMemberLevelPrice()!=null){
                                        if(MemberPriceTypeEnum.MEMBER_PRICE_FIXED_AMOUNT.getStatus()==product.getMemberPriceType()){
                                            price = memberLevelGoodsPricesList.get(0).getMemberLevelPrice();
                                        }else if(MemberPriceTypeEnum.MEMBER_PRICE_PERCENTAGE.getStatus()==product.getMemberPriceType()){
                                            price = price.multiply(memberLevelGoodsPricesList.get(0).getMemberLevelPrice()).divide(new BigDecimal(100),4, RoundingMode.HALF_UP);
                                        }
                                    }
                                }

                            }else{
                                //复购价
                                List<MemberLevelGoodsAgainPrice> memberLevelGoodsAgainPricesList = this.remoteGoodsService.selectMemberGoodsAgainPriceByProductId(memberLevelId, skuStock.getProductId(), skuStock.getId());
                                if(null != memberLevelGoodsAgainPricesList && !memberLevelGoodsAgainPricesList.isEmpty()){
                                    if (memberLevelGoodsAgainPricesList.get(0).getMemberLevelAgainPrice()!=null){
                                        if(MemberPriceTypeEnum.MEMBER_PRICE_FIXED_AMOUNT.getStatus()==product.getMemberAgainPriceType()){
                                            price = memberLevelGoodsAgainPricesList.get(0).getMemberLevelAgainPrice();
                                        }else if(MemberPriceTypeEnum.MEMBER_PRICE_PERCENTAGE.getStatus()==product.getMemberAgainPriceType()){
                                            price = price.multiply(memberLevelGoodsAgainPricesList.get(0).getMemberLevelAgainPrice()).divide(new BigDecimal(100),4, RoundingMode.HALF_UP);
                                        }
                                    }
                                }

                            }
                            totalAmount = price.multiply(number).add(totalAmount);
                        }
                    }
                }
                //2.商品券
                if(couponType == CouponTypeEnum.PRODUCTS.getType()){
                    List<ShopCouponProduct> shopCouponProductList = remoteShopsService.getShopCouponProduct(couponId);

                    for (MiniAccountCouponOrderItemDto dto : miniAccountCouponByOrderDto.getData()) {

                        //会员类型id
                        Long memberTypeId = dto.getMemberTypeId();
                        //若商品传入的会员类型为空，则将默认会员等级所属的会员类型作为传入的会员类型
                        if(memberTypeId == null){
                            //获取默认会员等级
                            MemberLevel memberLevel = remoteMiniAccountService.getDefaultMemberLevel();
                            if(memberLevel == null){
                                throw new ServiceException("默认会员等级为空！");
                            }
                            memberTypeId = memberLevel.getMemberTypeId();
                            // 设置默认会员类型
                            dto.setMemberTypeId(memberTypeId);
                        }
                        //根据会员类型获取会员等级ID
                        String memberLevelId = remoteMiniAccountService.getMemberLevelId(miniAccountCouponByOrderDto.getUserId(), memberTypeId);
                        if(StrUtil.isEmpty(memberLevelId)){
                            memberLevelId = "0";
                        }

                        BigDecimal number = dto.getNumber();
                        String skuId = dto.getSkuId();
                        List<ShopCouponProduct> productList = shopCouponProductList.stream().filter(e -> e.getSkuId().equals(skuId)).collect(Collectors.toList());

                        if(productList!=null&&productList.size()>0){
                            ShopCouponProduct shopCouponProduct = productList.get(0);
                            SkuStock skuStock = remoteGoodsService.findSkuStockById(Long.valueOf(shopCouponProduct.getSkuId()));
                            ProductVo product = remoteGoodsService.findProductById(Long.valueOf(shopCouponProduct.getProductId()));
                            BigDecimal price = skuStock.getPrice();
                            String shopId = skuStock.getShopId();
                            //是否满足优惠券条件
                            Boolean b = false;
                            if(shopFlag){
                                if(shopIds!=null&&shopIds.contains(shopId)){
                                    b = true;
                                }else{
                                    b = false;
                                }
                            }else{
                                b = true;
                            }
                            if(b){
                                Integer count = remoteOrderService.getCountByMemberType(shopUserId,dto.getMemberTypeId());
                                if(count == 0){
                                    //会员价
                                    List<MemberLevelGoodsPrice> memberLevelGoodsPricesList = this.remoteGoodsService.selectMemberGoodsPriceByProductId(memberLevelId, skuStock.getProductId(), skuStock.getId());
                                    if (CollUtil.isNotEmpty(memberLevelGoodsPricesList)) {
                                        if (memberLevelGoodsPricesList.get(0).getMemberLevelPrice()!=null){
                                            if(MemberPriceTypeEnum.MEMBER_PRICE_FIXED_AMOUNT.getStatus()==product.getMemberPriceType()){
                                                price = memberLevelGoodsPricesList.get(0).getMemberLevelPrice();
                                            }else if(MemberPriceTypeEnum.MEMBER_PRICE_PERCENTAGE.getStatus()==product.getMemberPriceType()){
                                                price = price.multiply(memberLevelGoodsPricesList.get(0).getMemberLevelPrice()).divide(new BigDecimal(100),4, RoundingMode.HALF_UP);
                                            }
                                        }
                                    }

                                }else{
                                    //复购价
                                    List<MemberLevelGoodsAgainPrice> memberLevelGoodsAgainPricesList = this.remoteGoodsService.selectMemberGoodsAgainPriceByProductId(memberLevelId, skuStock.getProductId(), skuStock.getId());
                                    if (CollUtil.isNotEmpty(memberLevelGoodsAgainPricesList)) {
                                        if (memberLevelGoodsAgainPricesList.get(0).getMemberLevelAgainPrice()!=null){
                                            if(MemberPriceTypeEnum.MEMBER_PRICE_FIXED_AMOUNT.getStatus()==product.getMemberAgainPriceType()){
                                                price = memberLevelGoodsAgainPricesList.get(0).getMemberLevelAgainPrice();
                                            }else if(MemberPriceTypeEnum.MEMBER_PRICE_PERCENTAGE.getStatus()==product.getMemberAgainPriceType()){
                                                price = price.multiply(memberLevelGoodsAgainPricesList.get(0).getMemberLevelAgainPrice()).divide(new BigDecimal(100),4, RoundingMode.HALF_UP);
                                            }
                                        }
                                    }
                                }
                                totalAmount = price.multiply(number).add(totalAmount);
                            }
                        }
                    }
                }
                //3.品类券
                if(couponType == CouponTypeEnum.CATEGORYS.getType()){
                    List<ShopCouponCategory> shopCouponCategoryList = remoteShopsService.getShopCouponCategory(couponId);
                    List<SkuStock> skuStockList = new ArrayList<>();
                    for (ShopCouponCategory shopCouponCategory : shopCouponCategoryList) {
                        String categoryId = shopCouponCategory.getCategoryId();
                        List<SkuStock> skuStockByCategoryId = remoteGoodsService.findSkuStockByCategoryId(categoryId);
                        skuStockList.addAll(skuStockByCategoryId);
                    }

                    if(skuStockList!=null&&skuStockList.size()>0){
                        skuStockList = skuStockList.stream().distinct().collect(Collectors.toList());
                    }

                    if(skuStockList!=null&&skuStockList.size()>0){
                        for (MiniAccountCouponOrderItemDto dto : miniAccountCouponByOrderDto.getData()) {
                            //会员类型id
                            Long memberTypeId = dto.getMemberTypeId();
                            //若商品传入的会员类型为空，则将默认会员等级所属的会员类型作为传入的会员类型
                            if(memberTypeId == null){
                                //获取默认会员等级
                                MemberLevel memberLevel = remoteMiniAccountService.getDefaultMemberLevel();
                                if(memberLevel == null){
                                    throw new ServiceException("默认会员等级为空！");
                                }
                                memberTypeId = memberLevel.getMemberTypeId();
                                // 设置默认会员类型
                                dto.setMemberTypeId(memberTypeId);
                            }
                            //根据会员类型获取会员等级ID
                            String memberLevelId = remoteMiniAccountService.getMemberLevelId(miniAccountCouponByOrderDto.getUserId(), memberTypeId);
                            if(StrUtil.isEmpty(memberLevelId)){
                                memberLevelId = "0";
                            }
                            BigDecimal number = dto.getNumber();
                            String skuId = dto.getSkuId();
                            List<SkuStock> stockList = skuStockList.stream().filter(e -> e.getId().equals(Long.valueOf(skuId))).collect(Collectors.toList());
                            if(stockList!=null&&stockList.size()>0){
                                SkuStock skuStock = stockList.get(0);
                                ProductVo product = remoteGoodsService.findProductById(skuStock.getProductId());
                                BigDecimal price = skuStock.getPrice();
                                String shopId = skuStock.getShopId();
                                //是否满足优惠券条件
                                Boolean b = false;
                                if(shopFlag){
                                    if(shopIds!=null&&shopIds.contains(shopId)){
                                        b = true;
                                    }else{
                                        b = false;
                                    }
                                }else{
                                    b = true;
                                }
                                if(b){
                                    Integer count = remoteOrderService.getCountByMemberType(shopUserId,dto.getMemberTypeId());
                                    if(count == 0){
                                        //会员价
                                        List<MemberLevelGoodsPrice> memberLevelGoodsPricesList = this.remoteGoodsService.selectMemberGoodsPriceByProductId(memberLevelId, skuStock.getProductId(), skuStock.getId());
                                        if (CollUtil.isNotEmpty(memberLevelGoodsPricesList)) {
                                            if (memberLevelGoodsPricesList.get(0).getMemberLevelPrice()!=null){
                                                if(MemberPriceTypeEnum.MEMBER_PRICE_FIXED_AMOUNT.getStatus()==product.getMemberPriceType()){
                                                    price = memberLevelGoodsPricesList.get(0).getMemberLevelPrice();
                                                }else if(MemberPriceTypeEnum.MEMBER_PRICE_PERCENTAGE.getStatus()==product.getMemberPriceType()){
                                                    price = price.multiply(memberLevelGoodsPricesList.get(0).getMemberLevelPrice()).divide(new BigDecimal(100),4, RoundingMode.HALF_UP);
                                                }
                                            }
                                        }
                                    }else{
                                        //复购价
                                        List<MemberLevelGoodsAgainPrice> memberLevelGoodsAgainPricesList = this.remoteGoodsService.selectMemberGoodsAgainPriceByProductId(memberLevelId, skuStock.getProductId(), skuStock.getId());
                                        if (CollUtil.isNotEmpty(memberLevelGoodsAgainPricesList)) {
                                            if (memberLevelGoodsAgainPricesList.get(0).getMemberLevelAgainPrice()!=null){
                                                if(MemberPriceTypeEnum.MEMBER_PRICE_FIXED_AMOUNT.getStatus()==product.getMemberAgainPriceType()){
                                                    price = memberLevelGoodsAgainPricesList.get(0).getMemberLevelAgainPrice();
                                                }else if(MemberPriceTypeEnum.MEMBER_PRICE_PERCENTAGE.getStatus()==product.getMemberAgainPriceType()){
                                                    price = price.multiply(memberLevelGoodsAgainPricesList.get(0).getMemberLevelAgainPrice()).divide(new BigDecimal(100),4, RoundingMode.HALF_UP);
                                                }
                                            }
                                        }
                                    }
                                    totalAmount = price.multiply(number).add(totalAmount);
                                }
                            }
                        }
                    }
                }
                BigDecimal promotion = shopCoupon.getPromotion();
                BigDecimal fullAmount = shopCoupon.getFullAmount();
                String couponName = shopCoupon.getCouponName();
                MiniAccountCouponByOrderVo miniAccountCouponByOrderVo = new MiniAccountCouponByOrderVo();
                miniAccountCouponByOrderVo.setUseFlag(CommonConstants.NUMBER_ONE);
                //当前商品总价小于满减金额则不可使用
                if(fullAmount.compareTo(totalAmount)>0){
                    log.info("商品可优惠总价小于满减金额");
                    miniAccountCouponByOrderVo.setUseFlag(CommonConstants.NUMBER_ZERO);
                    miniAccountCouponByOrderVo.setReason("商品可优惠总价小于满减金额");
                }
                BeanUtils.copyProperties(miniAccountCoupon,miniAccountCouponByOrderVo);
                miniAccountCouponByOrderVo.setFullAmount(fullAmount);
                miniAccountCouponByOrderVo.setPromotion(promotion);
                miniAccountCouponByOrderVo.setCouponName(couponName);
                dataList.add(miniAccountCouponByOrderVo);
            }
        }
        return dataList;
    }

    /*@Override
    @Transactional(rollbackFor = Exception.class)
    @Deprecated
    public List<MiniAccountCouponByOrderVo> getCouponByUser(MiniOrderCouponDto miniOrderCouponDto) {

        String shopUserId = "";
        String userId = "";
        if(miniOrderCouponDto.getReplaceCreateOrderFlag()!=null&& miniOrderCouponDto.getReplaceCreateOrderFlag().equals(CommonConstants.NUMBER_ONE)){

            if(StringUtils.isEmpty(miniOrderCouponDto.getUserId())){
                throw new ServiceException("代下单需要认证用户信息！");
            }

            MiniAccountExtends miniAccountExtends = miniAccountExtendsService.findByUserId(miniOrderCouponDto.getUserId());
            if(miniAccountExtends == null){
                throw new ServiceException("代下单用户信息不存在！");
            }
            userId = miniAccountExtends.getUserId();
            shopUserId = miniAccountExtends.getShopUserId();
        }else{
            if(StringUtils.isNotEmpty(miniOrderCouponDto.getUserId())){
                MiniAccountExtends miniAccountExtends = miniAccountExtendsService.findByUserId(miniOrderCouponDto.getUserId());
                if(miniAccountExtends == null){
                    throw new ServiceException("用户信息不存在！");
                }
                userId = miniAccountExtends.getUserId();
                shopUserId = miniAccountExtends.getShopUserId();
            }else{
                CurUserDto curUserDto = CurUserUtil.getHttpCurUser();
                if (ObjectUtil.isNull(curUserDto)) {
                    throw new ServiceException(SystemCode.UNAUTHORIZED);
                }
                shopUserId = curUserDto.getUserId();
                log.info("当前用户信息:" + curUserDto.toString());
            }

        }

        //查询用户持有的积分、收货地址
        //AccountInfoDto accountInfoDto = remoteMiniAccountService.accountInfo(shopUserId, Arrays.asList(1,2,3, 5));
        //会员价-- 不能一次查出所有
        //List<MemberLevelGoodsPrice> memberLevelGoodsPriceList = remoteGoodsService.selectMemberGoodsPrice(accountInfoDto.getMiniAccountunt().getMemberLevelId());
        //复购价-- 不能一次查出所有
        //List<MemberLevelGoodsAgainPrice> memberLevelGoodsAgainPriceList = remoteGoodsService.selectMemberGoodsAgainPrice(accountInfoDto.getMiniAccountunt().getMemberLevelId());
        // 查出所有会员等级
        //List<MemberLevelRelation> memberLevelRelations = this.memberLevelRelationService.getMemberLevelsByUserId(miniOrderCouponDto.getUserId());
        // 将会员等级映射成会员类型和会员等级记录关系，方便查找
        //Map<Long, List<MemberLevelRelation>> memberLevelRelationMap = memberLevelRelations.stream().collect(Collectors.groupingBy(MemberLevelRelation::getMemberTypeId));

        List<MiniAccountCouponByOrderVo> dataList = new ArrayList<>();
        //更新未用，失效优惠券状态（只更新自己的）
        LambdaQueryWrapper<MiniAccountCoupon>lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.ne(MiniAccountCoupon::getStatus,PromotionStatusEnum.USED.getStatus());
        lambdaQueryWrapper.eq(MiniAccountCoupon::getUserId,shopUserId);
        List<MiniAccountCoupon> miniAccountCouponList = this.list(lambdaQueryWrapper);
        if(miniAccountCouponList!=null && !miniAccountCouponList.isEmpty()){
            for (MiniAccountCoupon miniAccountCoupon : miniAccountCouponList) {
                //判断平台优惠券状态是否未生效
                AccountCouponVo shopCoupon = remoteShopsService.getCouponById(miniAccountCoupon.getCouponId());
                Integer status = shopCoupon.getStatus();
                if(status!=ApproveStatusEnum.APPROVED.getStatus()){
                    miniAccountCoupon.setStatus(PromotionStatusEnum.EXPIRED.getStatus());
                }else{
                    Date startTime = miniAccountCoupon.getStartTime();
                    Date endTime = miniAccountCoupon.getEndTime();
                    Date newDate = new Date();
                    if(newDate.getTime()>=startTime.getTime()&&newDate.getTime()<=endTime.getTime()){
                        miniAccountCoupon.setStatus(PromotionStatusEnum.UN_USE.getStatus());
                    }else{
                        miniAccountCoupon.setStatus(PromotionStatusEnum.EXPIRED.getStatus());
                    }
                }
                this.updateById(miniAccountCoupon);
            }
        }

        this.checkCoupon(miniOrderCouponDto.getData(), userId, shopUserId, miniOrderCouponDto.getMiniAccountCouponId());

        // -- 取消后面的逻辑
        //1.查询未用的优惠券
        LambdaQueryWrapper<MiniAccountCoupon>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MiniAccountCoupon::getUserId,shopUserId);
        wrapper.eq(MiniAccountCoupon::getStatus,PromotionStatusEnum.UN_USE.getStatus());
        wrapper.eq(null != miniOrderCouponDto.getMiniAccountCouponId(), MiniAccountCoupon::getId, miniOrderCouponDto.getMiniAccountCouponId());
        wrapper.orderByDesc(MiniAccountCoupon::getPromotion);
        List<MiniAccountCoupon> list = this.list(wrapper);
        //2.判断优惠券是否可用
        if(list!=null&&list.size()>0){
            for (MiniAccountCoupon miniAccountCoupon : list) {
                Long couponId = miniAccountCoupon.getCouponId();
                AccountCouponVo shopCoupon = remoteShopsService.getCouponById(couponId);
                Integer couponType = shopCoupon.getCouponType();
                BigDecimal totalAmount = BigDecimal.ZERO;

                //是否指定商家
                Boolean shopFlag = shopCoupon.getShopFlag();
                //指定商家id
                List<String> shopIds = shopCoupon.getShopIds();

                BigDecimal promotion = shopCoupon.getPromotion();
                BigDecimal fullAmount = shopCoupon.getFullAmount();
                String couponName = shopCoupon.getCouponName();
                MiniAccountCouponByOrderVo miniAccountCouponByOrderVo = new MiniAccountCouponByOrderVo();
                miniAccountCouponByOrderVo.setUseFlag(CommonConstants.NUMBER_ONE);
                //当前商品总价小于满减金额则不可使用
                if(fullAmount.compareTo(totalAmount)>0){
                    log.info("商品可优惠总价小于满减金额");
                    miniAccountCouponByOrderVo.setUseFlag(CommonConstants.NUMBER_ZERO);
                    miniAccountCouponByOrderVo.setReason("商品可优惠总价小于满减金额");
                }
                BeanUtils.copyProperties(miniAccountCoupon,miniAccountCouponByOrderVo);
                miniAccountCouponByOrderVo.setFullAmount(fullAmount);
                miniAccountCouponByOrderVo.setPromotion(promotion);
                miniAccountCouponByOrderVo.setCouponName(couponName);
                dataList.add(miniAccountCouponByOrderVo);
            }
        }
        return dataList;
    }*/

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Deprecated
    public MiniAccountCouponByOrderRespVo getCouponByUser(MiniAccountCouponByOrderDto miniAccountCouponByOrderDto) {

        String shopUserId = "";
        String userId = "";
        if(miniAccountCouponByOrderDto.getReplaceCreateOrderFlag()!=null&& miniAccountCouponByOrderDto.getReplaceCreateOrderFlag().equals(CommonConstants.NUMBER_ONE)){

            if(StringUtils.isEmpty(miniAccountCouponByOrderDto.getUserId())){
                throw new ServiceException("代下单需要认证用户信息！");
            }

            MiniAccountExtends miniAccountExtends = miniAccountExtendsService.findByUserId(miniAccountCouponByOrderDto.getUserId());
            if(miniAccountExtends == null){
                throw new ServiceException("代下单用户信息不存在！");
            }
            userId = miniAccountExtends.getUserId();
            shopUserId = miniAccountExtends.getShopUserId();
        }else{
            if(StringUtils.isNotEmpty(miniAccountCouponByOrderDto.getUserId())){
                MiniAccountExtends miniAccountExtends = miniAccountExtendsService.findByUserId(miniAccountCouponByOrderDto.getUserId());
                if(miniAccountExtends == null){
                    throw new ServiceException("用户信息不存在！");
                }
                userId = miniAccountExtends.getUserId();
                shopUserId = miniAccountExtends.getShopUserId();
            }else{
                CurUserDto curUserDto = CurUserUtil.getHttpCurUser();
                if (ObjectUtil.isNull(curUserDto)) {
                    throw new ServiceException(SystemCode.UNAUTHORIZED);
                }
                MiniAccount miniAccount = this.miniAccountService.getByShopUserId(curUserDto.getUserId());
                if(null != miniAccount){
                    userId = miniAccount.getUserId();
                }
                shopUserId = curUserDto.getUserId();
                log.info("当前用户信息:" + curUserDto.toString());
            }

        }

        //查询用户持有的积分、收货地址
        //AccountInfoDto accountInfoDto = remoteMiniAccountService.accountInfo(shopUserId, Arrays.asList(1,2,3, 5));
        //会员价-- 不能一次查出所有
        //List<MemberLevelGoodsPrice> memberLevelGoodsPriceList = remoteGoodsService.selectMemberGoodsPrice(accountInfoDto.getMiniAccountunt().getMemberLevelId());
        //复购价-- 不能一次查出所有
        //List<MemberLevelGoodsAgainPrice> memberLevelGoodsAgainPriceList = remoteGoodsService.selectMemberGoodsAgainPrice(accountInfoDto.getMiniAccountunt().getMemberLevelId());
        // 查出所有会员等级
        //List<MemberLevelRelation> memberLevelRelations = this.memberLevelRelationService.getMemberLevelsByUserId(miniOrderCouponDto.getUserId());
        // 将会员等级映射成会员类型和会员等级记录关系，方便查找
        //Map<Long, List<MemberLevelRelation>> memberLevelRelationMap = memberLevelRelations.stream().collect(Collectors.groupingBy(MemberLevelRelation::getMemberTypeId));

        //更新未用，失效优惠券状态（只更新自己的）
        LambdaQueryWrapper<MiniAccountCoupon>lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.ne(MiniAccountCoupon::getStatus,PromotionStatusEnum.USED.getStatus());
        lambdaQueryWrapper.eq(MiniAccountCoupon::getUserId,shopUserId);
        List<MiniAccountCoupon> miniAccountCouponList = this.list(lambdaQueryWrapper);
        if(miniAccountCouponList!=null && !miniAccountCouponList.isEmpty()){
            for (MiniAccountCoupon miniAccountCoupon : miniAccountCouponList) {
                //判断平台优惠券状态是否未生效
                AccountCouponVo shopCoupon = remoteShopsService.getCouponById(miniAccountCoupon.getCouponId());
                Integer status = shopCoupon.getStatus();
                if(status!=ApproveStatusEnum.APPROVED.getStatus()){
                    miniAccountCoupon.setStatus(PromotionStatusEnum.EXPIRED.getStatus());
                }else{
                    Date startTime = miniAccountCoupon.getStartTime();
                    Date endTime = miniAccountCoupon.getEndTime();
                    Date newDate = new Date();
                    if(newDate.getTime()>=startTime.getTime()&&newDate.getTime()<=endTime.getTime()){
                        miniAccountCoupon.setStatus(PromotionStatusEnum.UN_USE.getStatus());
                    }else{
                        miniAccountCoupon.setStatus(PromotionStatusEnum.EXPIRED.getStatus());
                    }
                }
                this.updateById(miniAccountCoupon);
            }
        }

        List<MiniAccountCouponByOrderVo> dataList = this.checkCoupon(miniAccountCouponByOrderDto.getData(), userId, shopUserId,
                miniAccountCouponByOrderDto.getMiniAccountCouponId(), miniAccountCouponByOrderDto.isAllFlag());

        // -- 取消后面的逻辑
        //1.查询未用的优惠券
        /*LambdaQueryWrapper<MiniAccountCoupon>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MiniAccountCoupon::getUserId,shopUserId);
        wrapper.eq(MiniAccountCoupon::getStatus,PromotionStatusEnum.UN_USE.getStatus());
        wrapper.eq(null != miniOrderCouponDto.getMiniAccountCouponId(), MiniAccountCoupon::getId, miniOrderCouponDto.getMiniAccountCouponId());
        wrapper.orderByDesc(MiniAccountCoupon::getPromotion);
        List<MiniAccountCoupon> list = this.list(wrapper);
        //2.判断优惠券是否可用
        if(list!=null&&list.size()>0){
            for (MiniAccountCoupon miniAccountCoupon : list) {
                Long couponId = miniAccountCoupon.getCouponId();
                AccountCouponVo shopCoupon = remoteShopsService.getCouponById(couponId);
                Integer couponType = shopCoupon.getCouponType();
                BigDecimal totalAmount = BigDecimal.ZERO;

                //是否指定商家
                Boolean shopFlag = shopCoupon.getShopFlag();
                //指定商家id
                List<String> shopIds = shopCoupon.getShopIds();

                BigDecimal promotion = shopCoupon.getPromotion();
                BigDecimal fullAmount = shopCoupon.getFullAmount();
                String couponName = shopCoupon.getCouponName();
                MiniAccountCouponByOrderVo miniAccountCouponByOrderVo = new MiniAccountCouponByOrderVo();
                miniAccountCouponByOrderVo.setUseFlag(CommonConstants.NUMBER_ONE);
                //当前商品总价小于满减金额则不可使用
                if(fullAmount.compareTo(totalAmount)>0){
                    log.info("商品可优惠总价小于满减金额");
                    miniAccountCouponByOrderVo.setUseFlag(CommonConstants.NUMBER_ZERO);
                    miniAccountCouponByOrderVo.setReason("商品可优惠总价小于满减金额");
                }
                BeanUtils.copyProperties(miniAccountCoupon,miniAccountCouponByOrderVo);
                miniAccountCouponByOrderVo.setFullAmount(fullAmount);
                miniAccountCouponByOrderVo.setPromotion(promotion);
                miniAccountCouponByOrderVo.setCouponName(couponName);
                dataList.add(miniAccountCouponByOrderVo);
            }
        }
        return dataList;*/
        log.info("查询个人优惠券返回的信息：{}", JSONObject.toJSONString(dataList));
        MiniAccountCouponByOrderRespVo miniAccountCouponByOrderRespVo = new MiniAccountCouponByOrderRespVo();
        miniAccountCouponByOrderRespVo.setCouponByOrderVoList(dataList);
        miniAccountCouponByOrderRespVo.setCouponByOrderDto(miniAccountCouponByOrderDto);
        return miniAccountCouponByOrderRespVo;
    }


    private List<MiniAccountCouponByOrderVo> checkCoupon(List<MiniAccountCouponOrderItemDto> itemVoList, String userId, String shopUserId, Long miniAccountCouponId, boolean allFlag){
        // 查询审核通过在有效期内且允许会员所属会员等级能使用的优惠券
        // 1、查询符合会员等级的有效的优惠券--优惠券暂未有会员等级的限制条件
        //List<MemberLevelRelation> memberLevelRelations = this.remoteMiniAccountService.getMemberLevelsByUserId(userId);
        // 获取会员等级id
        //List<String> memberLevelIds = memberLevelRelations.stream().map(MemberLevelRelation::getMemberLevelId).collect(Collectors.toList());

        //1.查询未用的用户优惠券或者指定的未用的某张用户优惠券
        LambdaQueryWrapper<MiniAccountCoupon>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MiniAccountCoupon::getUserId,shopUserId);
        wrapper.eq(MiniAccountCoupon::getStatus,PromotionStatusEnum.UN_USE.getStatus());
        wrapper.eq(null != miniAccountCouponId, MiniAccountCoupon::getId, miniAccountCouponId);
        wrapper.orderByDesc(MiniAccountCoupon::getPromotion);
        List<MiniAccountCoupon> miniAccountCouponAllList = this.list(wrapper);

        List<MiniAccountCouponByOrderVo> dataList = new ArrayList<>();
        log.info("查询未用的用户优惠券：{}", JSONObject.toJSONString(miniAccountCouponAllList));
        //
        if(miniAccountCouponAllList!=null && !miniAccountCouponAllList.isEmpty()){
            boolean chooseFlag = false;
            // 判断订单明细满足哪个优惠券
            for (int i = 0; i < miniAccountCouponAllList.size() && (!chooseFlag || allFlag); i++) {
                MiniAccountCoupon miniAccountCoupon = miniAccountCouponAllList.get(i);
                Long couponId = miniAccountCoupon.getCouponId();
                AccountCouponVo shopCoupon = remoteShopsService.getCouponById(couponId);
                // 符合优惠券的商品明细
                List<MiniAccountCouponOrderItemDto> validItemVoList = new ArrayList<>();
                // 商品明细以规格作为分组
                Map<Long, List<MiniAccountCouponOrderItemDto>> orderItemDataMap = new HashMap<>();
                // 合并数量和金额商品明细
                List<MiniAccountCouponOrderItemDto> orderItemGroupList = new ArrayList<>();

                String reason = "";
                // 是否指定参与商家
                if(!shopCoupon.getShopFlag()){
                    // 不指定商家，所有商家都参与
                    validItemVoList = itemVoList;
                }else if(shopCoupon.getShopFlag()){
                    // 指定参与商家，获取商家列表
                    //指定商家id
                    List<String> shopIds = shopCoupon.getShopIds();
                    // 将不在商家shopIds的商品过滤
                    validItemVoList = itemVoList.stream().filter(itemVo -> shopIds.contains(itemVo.getShopId())).collect(Collectors.toList());
                }
                if(validItemVoList.isEmpty()){
                    reason = "店铺不在优惠券指定商家内";
                }

                // 是否指定商品
                if(shopCoupon.getCouponType() == CouponTypeEnum.PRODUCTS.getType()){
                    // 商品优惠券
                    List<ShopCouponProduct> shopCouponProductList = remoteShopsService.getShopCouponProduct(couponId);
                    // 获取指定商品的skuId列表
                    List<Long> productSkuIdList = shopCouponProductList.stream().map(shopCouponProduct -> Long.parseLong(shopCouponProduct.getSkuId())).collect(Collectors.toList());
                    // 将不在商品列表中的商品过滤
                    validItemVoList = validItemVoList.stream().filter(itemVo -> productSkuIdList.contains(Long.valueOf(itemVo.getSkuId()))).collect(Collectors.toList());
                }else if(shopCoupon.getCouponType() == CouponTypeEnum.CATEGORYS.getType()){
                    // 品类优惠券
                    List<ShopCouponCategory> shopCouponCategoryList = remoteShopsService.getShopCouponCategory(couponId);
                    List<SkuStock> skuStockList = new ArrayList<>();
                    shopCouponCategoryList.parallelStream().forEach(shopCouponCategory -> {
                        String categoryId = shopCouponCategory.getCategoryId();
                        List<SkuStock> skuStockByCategoryId = remoteGoodsService.findSkuStockByCategoryId(categoryId);
                        skuStockList.addAll(skuStockByCategoryId);
                    });
                    // 获取指定商品的skuId列表
                    List<Long> productSkuIdList = skuStockList.stream().map(SkuStock::getId).collect(Collectors.toList());
                    // 将不在商品列表中的商品过滤
                    validItemVoList = validItemVoList.stream().filter(itemVo -> productSkuIdList.contains(Long.valueOf(itemVo.getSkuId()))).collect(Collectors.toList());
                }
                if(StrUtil.isEmpty(reason) && validItemVoList.isEmpty()){
                    reason = "购买商品不在优惠券指定商品内";
                }

                log.info("自身可用优惠券信息：{}，符合优惠券过滤的商品信息：{}", JSONObject.toJSONString(shopCoupon), JSONObject.toJSONString(validItemVoList));
                orderItemDataMap = this.groupGoodsItemData(orderItemGroupList, orderItemDataMap, validItemVoList);

                //判断按商品照规格分组后的商品详情是否满足优惠券规则
                chooseFlag = this.chooseItemCoupon(orderItemGroupList, orderItemDataMap, shopCoupon, miniAccountCoupon, allFlag);
                if(allFlag || chooseFlag){
                    //要查询个人名下所有优惠券或者该优惠券符合使用条件
                    BigDecimal promotion = shopCoupon.getPromotion();
                    BigDecimal fullAmount = shopCoupon.getFullAmount();
                    String couponName = shopCoupon.getCouponName();
                    MiniAccountCouponByOrderVo miniAccountCouponByOrderVo = new MiniAccountCouponByOrderVo();
                    miniAccountCouponByOrderVo.setUseFlag(chooseFlag ? CommonConstants.NUMBER_ONE :CommonConstants.NUMBER_ZERO);
                    //当前商品总价小于满减金额则不可使用
                    if(!chooseFlag){
                        if(StrUtil.isEmpty(reason)){
                            reason = "商品可优惠总价小于优惠券满减金额";
                        }
                        log.info("判断优惠券是否符合使用条件，不符合的原因：{}", reason);
                        miniAccountCouponByOrderVo.setReason(reason);
                    }
                    BeanUtils.copyProperties(miniAccountCoupon,miniAccountCouponByOrderVo);
                    miniAccountCouponByOrderVo.setFullAmount(fullAmount);
                    miniAccountCouponByOrderVo.setPromotion(promotion);
                    miniAccountCouponByOrderVo.setCouponName(couponName);
                    miniAccountCouponByOrderVo.setTicketType(shopCoupon.getTicketType());
                    dataList.add(miniAccountCouponByOrderVo);
                }

            }

        }
        return dataList;
    }

    /**
     * 按商品规格组装数据
     * @param goodsItemGroupList 商品按规格合并后的商品详情
     * @param orderItemDataMap 商品按规格分组
     * @param itemVoList 商品详情
     */
    private Map<Long, List<MiniAccountCouponOrderItemDto>> groupGoodsItemData(List<MiniAccountCouponOrderItemDto> goodsItemGroupList, Map<Long, List<MiniAccountCouponOrderItemDto>> orderItemDataMap,
                                                       List<MiniAccountCouponOrderItemDto> itemVoList){
        if(null == itemVoList || itemVoList.isEmpty()){
            return orderItemDataMap;
        }
        // 以规格作为分组
        orderItemDataMap = itemVoList.stream().collect(Collectors.groupingBy(item -> Long.valueOf(item.getSkuId())));
        for (Map.Entry<Long, List<MiniAccountCouponOrderItemDto>> entry : orderItemDataMap.entrySet()) {
            Long key = entry.getKey();
            List<MiniAccountCouponOrderItemDto> list = entry.getValue();
            // 同规格数量相加
            Integer number = 0;
            // 同规格金额相加
            BigDecimal amount = BigDecimal.ZERO;
            //BigDecimal youhuiAmount = BigDecimal.ZERO;
            // 将同规格产品的数量相加
            for (MiniAccountCouponOrderItemDto itemDto : list) {
                number = number + itemDto.getNumber().intValue();
                BigDecimal realAmount = itemDto.getProductPrice() != null ? itemDto.getProductPrice().multiply(itemDto.getNumber())
                        .setScale(2, RoundingMode.HALF_UP) : BigDecimal.ZERO;
                //BigDecimal singleYouhuiAmount = itemDto.getYouhuiPrice() != null ? itemDto.getYouhuiPrice() : BigDecimal.ZERO;
                //realAmount = realAmount.subtract(singleYouhuiAmount);
                amount = amount.add(realAmount);
                //youhuiAmount = youhuiAmount.add(singleYouhuiAmount);
            }
            MiniAccountCouponOrderItemDto newItemVo = new MiniAccountCouponOrderItemDto();
            BeanUtil.copyProperties(list.get(0), newItemVo);
            newItemVo.setNumber(new BigDecimal(number));
            newItemVo.setAmount(amount);
            //newItemVo.setYouhuiPrice(youhuiAmount);
            goodsItemGroupList.add(newItemVo);
        }
        return orderItemDataMap;
    }


    /**
     * 订单明细是否满足满减满赠活动规则
     * @param itemVoGroupList 商品按规格合并后的商品详情
     * @param orderItemDataMap
     * @param shopCoupon
     * @param allFlag 是否查询该用户的所有优惠券，0-否，1-是
     */
    private Boolean chooseItemCoupon(List<MiniAccountCouponOrderItemDto> itemVoGroupList, Map<Long, List<MiniAccountCouponOrderItemDto>> orderItemDataMap,
                                     AccountCouponVo shopCoupon, MiniAccountCoupon miniAccountCoupon, boolean allFlag){
        if(null == itemVoGroupList || itemVoGroupList.isEmpty()){
            return false;
        }
        log.info("判断订单明细是否优惠券规则，优惠券信息：{}，商品规格分组信息：{}", JSONObject.toJSONString(shopCoupon), JSONObject.toJSONString(orderItemDataMap));

        if(null != orderItemDataMap && !orderItemDataMap.isEmpty()){
            // 算出商品明细金额之和，就判断是否满足满额条件
            BigDecimal totalAmount = itemVoGroupList.stream().map(MiniAccountCouponOrderItemDto::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            if(totalAmount.compareTo(shopCoupon.getFullAmount()) >= 0){
                // 满足条件，进行优惠
                log.info("满足优惠券规则，优惠券信息：{}，由这些商品满足优惠券规则：{}，商品规格分组信息：{}", JSONObject.toJSONString(shopCoupon),
                        JSONObject.toJSONString(itemVoGroupList), JSONObject.toJSONString(orderItemDataMap));
                if(!allFlag){
                    this.setItemCoupon(itemVoGroupList, orderItemDataMap, shopCoupon, miniAccountCoupon);
                }
                return true;
            }

        }
        return false;
    }

    /**
     * 将满足优惠券规则的商品明细赋值优惠券
     * @param itemVoGroupList
     * @param orderItemDataMap
     * @param shopCoupon
     */
    private void setItemCoupon(List<MiniAccountCouponOrderItemDto> itemVoGroupList, Map<Long, List<MiniAccountCouponOrderItemDto>> orderItemDataMap,
                               AccountCouponVo shopCoupon, MiniAccountCoupon miniAccountCoupon){
        // 满足条件，进行优惠
        itemVoGroupList.parallelStream().forEach(itemVo -> {
            // 用户优惠券id
            itemVo.setMiniAccountCouponId(miniAccountCoupon.getId());
            // 优惠券信息
            MiniAccountCouponVo accountCouponVo = new MiniAccountCouponVo();
            BeanUtil.copyProperties(shopCoupon, accountCouponVo);
            itemVo.setAccountCouponVo(accountCouponVo);
            List<MiniAccountCouponOrderItemDto> itemVos = orderItemDataMap.get(Long.valueOf(itemVo.getSkuId()));
            if(itemVos != null && !itemVos.isEmpty()){
                itemVos.parallelStream().forEach(itemVo2 -> {
                    itemVo2.setMiniAccountCouponId(miniAccountCoupon.getId());
                    itemVo2.setAccountCouponVo(accountCouponVo);
                });
            }
            // 将已经赋值过满减满赠活动的商品详情移除，以便后面不再循环，节省时间
            orderItemDataMap.remove(Long.valueOf(itemVo.getSkuId()));
        });
    }

    @Override
    public Boolean updateAccountCouponOk(AccountCouponDto accountCouponDto) {
        String userId = accountCouponDto.getUserId();

        MiniAccount miniAccount = miniAccountService.getByShopUserId(userId);

        LambdaQueryWrapper<MiniAccountCoupon>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MiniAccountCoupon::getUserId,userId);
        wrapper.eq(MiniAccountCoupon::getId,accountCouponDto.getAccountCouponId());
        List<MiniAccountCoupon> list = this.list(wrapper);
            if(list == null || list.isEmpty()){
            throw new ServiceException("优惠券不存在！");
        }
        MiniAccountCoupon miniAccountCoupon = list.get(0);
        if(!miniAccountCoupon.getStatus().equals(PromotionStatusEnum.UN_USE.getStatus())){
            throw new ServiceException("优惠券不可用！");
        }
        miniAccountCoupon.setStatus(PromotionStatusEnum.USED.getStatus());
        miniAccountCoupon.setOrderId(accountCouponDto.getOrderId());
        miniAccountCoupon.setOrderGroupId(accountCouponDto.getOrderGroupId());
        miniAccountCoupon.setUseName(miniAccount.getNikeName());
        miniAccountCoupon.setUseTime(new Date());
        //可以多商家使用
        //ShopsPartner shopsPartner = remoteShopsService.getShopsPartner();
        //miniAccountCoupon.setUseShopName(shopsPartner.getName());
        boolean b = this.updateById(miniAccountCoupon);
        return b;
    }

    @Override
    public Boolean updateAccountCouponNo(AccountCouponDto accountCouponDto) {
        String userId = accountCouponDto.getUserId();
        LambdaQueryWrapper<MiniAccountCoupon>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MiniAccountCoupon::getUserId,userId);
        wrapper.eq(MiniAccountCoupon::getId,accountCouponDto.getAccountCouponId());
        wrapper.eq(null!= accountCouponDto.getOrderGroupId(), MiniAccountCoupon::getOrderGroupId,accountCouponDto.getOrderGroupId());
        wrapper.eq(null!= accountCouponDto.getOrderId(), MiniAccountCoupon::getOrderGroupId,accountCouponDto.getOrderGroupId());
        List<MiniAccountCoupon> list = this.list(wrapper);
        if(list==null|| list.isEmpty()){
            throw new ServiceException("优惠券不存在！");
        }
        MiniAccountCoupon miniAccountCoupon = list.get(0);
        miniAccountCoupon.setStatus(PromotionStatusEnum.UN_USE.getStatus());
        miniAccountCoupon.setOrderId(null);
        miniAccountCoupon.setOrderGroupId(null);
        miniAccountCoupon.setUseShopName(null);
        miniAccountCoupon.setUseTime(null);
        miniAccountCoupon.setUseShopName(null);
        boolean b = this.updateById(miniAccountCoupon);
        return b;
    }

    @Override
    public IPage<MiniAccountCouponSearchVo> getMiniAccountCouponSearchVo(MiniAccountCouponSearchParam miniAccountCouponSearchParam) {

        String sourceShopId = ShopContextHolder.getShopId();
        ShopsPartner shopsPartner = remoteShopsService.getByShopId(Long.valueOf(sourceShopId));
        if(shopsPartner!=null&&shopsPartner.getMainFlag()!=null&&shopsPartner.getMainFlag()==1){
            ShopContextHolder.setShopId(CommonConstants.DEFAULT_SHOP_ID);
        }
        IPage<MiniAccountCouponSearchVo> page = this.baseMapper.getMiniAccountCouponSearchVo(
                new Page<>(miniAccountCouponSearchParam.getCurrent(), miniAccountCouponSearchParam.getSize()), miniAccountCouponSearchParam);
        ShopContextHolder.setShopId(sourceShopId);
        return page;
    }

    @Override
    public void sendCoupon(SendCouponMessage message) {
        Long couponId = message.getCouponId();
        String tenantId = message.getTenantId();
        TenantContextHolder.setTenantId(tenantId);
        // 查询优惠券的开始时间和结束时间
        AccountCouponVo shopCoupon = remoteShopsService.getCouponById(couponId);

        if(null == shopCoupon){
            throw new ServiceException("优惠券记录不存在", SystemCode.DATA_NOT_EXIST_CODE);
        }
        Date newDate = new Date();

        Integer grantType = shopCoupon.getGrantType();

        if(grantType!=null){

            List<String>list = new ArrayList<>();
            List<ShopCouponAccount> shopCouponAccountList = remoteShopsService.getShopCouponAccount(couponId);
            if(grantType == GrantTypeEnum.ALL.getType()){//全部客户
                LambdaQueryWrapper<MiniAccount>queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(MiniAccount::getDeleted, CommonConstants.NUMBER_ZERO);
                queryWrapper.eq(MiniAccount::getWhetherAuthorization,CommonConstants.NUMBER_ONE);
                List<MiniAccount> miniAccountList = miniAccountService.list(queryWrapper);
                for (MiniAccount miniAccount : miniAccountList) {
                    MiniAccountExtends miniAccountExtends = miniAccountExtendsService.findByUserId(miniAccount.getUserId());
                    list.add(miniAccountExtends.getShopUserId());
                }
            }else if(grantType == GrantTypeEnum.TAG.getType()){//标签客户
                for (ShopCouponAccount shopCouponAccount : shopCouponAccountList) {
                    //标签id
                    String tagId = shopCouponAccount.getSourceId();

                    LambdaQueryWrapper<MiniAccountTagGroup>wrapper = new LambdaQueryWrapper<>();
                    wrapper.eq(MiniAccountTagGroup::getDeleted,CommonConstants.NUMBER_ZERO);
                    wrapper.eq(MiniAccountTagGroup::getTagId,tagId);

                    List<MiniAccountTagGroup> miniAccountTagGroupList = miniAccountTagGroupService.list(wrapper);

                    for (MiniAccountTagGroup miniAccountTagGroup : miniAccountTagGroupList) {
                        String userId = miniAccountTagGroup.getUserId();
                        MiniAccount miniAccount = miniAccountService.getByShopUserId(userId);
                        if(miniAccount!=null){
                            if(miniAccount.getWhetherAuthorization()){
                                if(!list.contains(miniAccountTagGroup.getUserId())){
                                    list.add(miniAccountTagGroup.getUserId());
                                }
                            }
                        }
                    }
                }
            }else if(grantType == GrantTypeEnum.MEMBER.getType()){//会员等级
                for (ShopCouponAccount shopCouponAccount : shopCouponAccountList) {
                    String memberLevelId = shopCouponAccount.getSourceId();//会员等级id
                    LambdaQueryWrapper<MiniAccount>queryWrapper = new LambdaQueryWrapper<>();
                    queryWrapper.eq(MiniAccount::getDeleted,CommonConstants.NUMBER_ZERO);
                    queryWrapper.eq(MiniAccount::getMemberLevelId,memberLevelId);
                    queryWrapper.eq(MiniAccount::getWhetherAuthorization,CommonConstants.NUMBER_ONE);
                    List<MiniAccount> miniAccountList = miniAccountService.list(queryWrapper);
                    for (MiniAccount miniAccount : miniAccountList) {
                        MiniAccountExtends miniAccountExtends = miniAccountExtendsService.findByUserId(miniAccount.getUserId());
                        list.add(miniAccountExtends.getShopUserId());
                    }

                }
            }else if(grantType == GrantTypeEnum.APPOINT.getType()){//指定客户
                for (ShopCouponAccount shopCouponAccount : shopCouponAccountList) {
                    String userId = shopCouponAccount.getSourceId();
                    LambdaQueryWrapper<MiniAccount>queryWrapper = new LambdaQueryWrapper<>();
                    queryWrapper.eq(MiniAccount::getDeleted,CommonConstants.NUMBER_ZERO);
                    queryWrapper.eq(MiniAccount::getUserId,userId);
                    queryWrapper.eq(MiniAccount::getWhetherAuthorization,CommonConstants.NUMBER_ONE);
                    List<MiniAccount> miniAccountList = miniAccountService.list(queryWrapper);
                    for (MiniAccount miniAccount : miniAccountList) {
                        MiniAccountExtends miniAccountExtends = miniAccountExtendsService.findByUserId(miniAccount.getUserId());
                        list.add(miniAccountExtends.getShopUserId());
                    }
                }
            }


            if(list!=null&&list.size()>0){
                for (String shopUserId : list) {

                    MiniAccountCoupon miniAccountCoupon = new MiniAccountCoupon();
                    miniAccountCoupon.setStartTime(shopCoupon.getStartTime());
                    miniAccountCoupon.setEndTime(shopCoupon.getEndTime());

                    miniAccountCoupon.setStartTime(shopCoupon.getStartTime());
                    miniAccountCoupon.setEndTime(shopCoupon.getEndTime());

                    Date startTime = shopCoupon.getStartTime();
                    Date endTime = shopCoupon.getEndTime();
                    //根据使用时间范围判断优惠卷是未用还是失效状态
                    miniAccountCoupon.setStatus(PromotionStatusEnum.EXPIRED.getStatus());
                    if(newDate.getTime()>=startTime.getTime()&&newDate.getTime()<=endTime.getTime()){
                        miniAccountCoupon.setStatus(PromotionStatusEnum.UN_USE.getStatus());
                    }
                    miniAccountCoupon.setUserId(Long.valueOf(shopUserId));
                    miniAccountCoupon.setCouponId(shopCoupon.getId());
                    miniAccountCoupon.setCouponName(shopCoupon.getCouponName());
                    miniAccountCoupon.setPromotion(shopCoupon.getPromotion());
                    this.save(miniAccountCoupon);
                }
            }

        }else{
            log.info("指定发券对象为空");
        }
    }


    @Override
    public void exportMiniAccountCouponSearch(MiniAccountCouponSearchParam param) {
        // 设置导出最大限制
        HuToolExcelUtils.exportParamToMax(param);

        // 查询数据
        IPage<MiniAccountCouponSearchVo> page = this.getMiniAccountCouponSearchVo(param);
        List<MiniAccountCouponSearchVo> dataList = page.getRecords();

        // 使用Lambda转换导出
        HuToolExcelUtils.exportData(dataList, "优惠券明细", (MiniAccountCouponSearchVo source) -> {
            MiniAccountCouponSearchExcelVo target = new MiniAccountCouponSearchExcelVo();
            target.setOrderId(source.getOrderId()+"");
            target.setUseTime(source.getUseTime() != null ? DateUtil.format(source.getUseTime(), "yyyy-MM-dd HH:mm:ss") : "");
            return target;
        });
    }


}
