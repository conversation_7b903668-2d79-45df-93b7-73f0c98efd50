package com.medusa.gruul.platform.model.param;

import com.medusa.gruul.common.core.param.QueryParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 商家职位查询参数
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "PositionParam对象", description = "商家职位查询参数")
public class PlatformPositionParam extends QueryParam {

    /**
     * 职位名称
     */
    @ApiModelProperty(value = "职位名称")
    private String positionName;

    /**
     * 状态 0-停用，1-启用
     */
    @ApiModelProperty(value = "状态 0-停用，1-启用")
    private Integer status;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;


    /**
     * 员工Id
     */
    @ApiModelProperty(value = "员工Id")
    private Long employeeId;
} 