package com.medusa.gruul.account.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 14:35 2025/5/27
 */
@Data
@ApiModel(value = "审核用户实名dto")
public class AuditMiniAccountCardDto {

    /**
     * id
     */
    @ApiModelProperty(value = "id")
    private String id;

    /**
     * 实名状态:-1->审核不通过;0->未实名;1->待审核;2->已实名
     */
    @ApiModelProperty(value = "实名状态:-1->审核不通过;0->未实名;1->待审核;2->已实名")
    private Integer cardAuthorization;

    /**
     * 审核意见
     */
    @ApiModelProperty(value = "审核意见")
    private String cardAuditReason;


}
