package com.medusa.gruul.account.service.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.medusa.gruul.account.api.entity.MiniAccount;
import com.medusa.gruul.account.api.entity.MiniAccountBank;
import com.medusa.gruul.account.api.entity.MiniAccountExtends;
import com.medusa.gruul.account.api.entity.MiniAccountShopsApply;
import com.medusa.gruul.account.api.enums.*;
import com.medusa.gruul.account.api.model.message.MiniAccountShopsApplyAddMessage;
import com.medusa.gruul.account.api.model.message.MiniAccountShopsApplyUpdateMessage;
import com.medusa.gruul.account.mapper.MiniAccountShopsApplyMapper;
import com.medusa.gruul.account.model.dto.MiniAccountShopsApplyDto;
import com.medusa.gruul.account.mq.Sender;
import com.medusa.gruul.account.service.IMiniAccountBankService;
import com.medusa.gruul.account.service.IMiniAccountExtendsService;
import com.medusa.gruul.account.service.IMiniAccountService;
import com.medusa.gruul.account.service.IMiniAccountShopsApplyService;
import com.medusa.gruul.common.core.constant.CommonConstants;
import com.medusa.gruul.common.core.constant.enums.CardAuthorizationEnum;
import com.medusa.gruul.common.core.exception.ServiceException;
import com.medusa.gruul.common.core.util.CurUserUtil;
import com.medusa.gruul.common.core.util.Result;
import com.medusa.gruul.common.data.tenant.TenantContextHolder;
import com.medusa.gruul.payment.api.enums.CardTypeEnum;
import com.medusa.gruul.payment.api.enums.ChannelStatusEnum;
import com.medusa.gruul.payment.api.enums.FileTypeEnum;
import com.medusa.gruul.payment.api.feign.RemotePaymentService;
import com.medusa.gruul.payment.api.model.dto.ShopsApplyDto;
import com.medusa.gruul.payment.api.model.vo.AggregationAreaVo;
import com.medusa.gruul.payment.api.model.vo.AggregationBankBranchVo;
import com.medusa.gruul.platform.api.feign.RemoteMiniInfoService;
import com.medusa.gruul.platform.api.model.dto.ShopConfigDto;
import com.medusa.gruul.platform.api.model.dto.ShopInfoDto;
import com.medusa.gruul.platform.api.model.vo.PayInfoVo;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Random;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 11:33 2025/7/31
 */
@Service
@Log4j2
public class MiniAccountShopsApplyServiceImpl extends ServiceImpl<MiniAccountShopsApplyMapper, MiniAccountShopsApply> implements IMiniAccountShopsApplyService {

    @Autowired
    private IMiniAccountService miniAccountService;
    @Autowired
    private IMiniAccountExtendsService miniAccountExtendsService;
    @Autowired
    private IMiniAccountBankService miniAccountBankService;
    @Autowired
    private RemoteMiniInfoService remoteMiniInfoService;
    @Autowired
    private RemotePaymentService remotePaymentService;
    @Autowired
    private Sender sender;
    @Override
    public void vailUserMessage() {
        //小程序用户id
        String userId = CurUserUtil.getHttpCurUser().getUserId();

        //验证小程序用户
        MiniAccount miniAccount = miniAccountService.getByShopUserId(userId);
        if(miniAccount == null){
            throw new ServiceException("小程序用户信息不存在！");
        }
        MiniAccountExtends miniAccountExtends = miniAccountExtendsService.findByShopUserId(userId);
        //验证小程序用户扩展信息
        if(miniAccount == null){
            throw new ServiceException("小程序用户扩展信息不存在！");
        }
        //验证是否进行身份证验证
        if(miniAccountExtends.getCardAuthorization()!= CardAuthorizationEnum.YES.getStatus()){
            throw new ServiceException("请先进行身份证认证！");
        }
        //验证是否添加银行卡
        LambdaQueryWrapper<MiniAccountBank>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MiniAccountBank::getUserId,miniAccount.getUserId());
        int count = miniAccountBankService.count(wrapper);
        if(count<=0){
            throw new ServiceException("请先添加银行卡！");
        }
        //验证平台是否添加行业类目
        Result<ShopInfoDto> result = remoteMiniInfoService.getShopInfo();
        ShopInfoDto shopInfoDto = result.getData();
        if(shopInfoDto == null || (shopInfoDto!=null&& StringUtils.isEmpty(shopInfoDto.getMccCode()))){
            throw new ServiceException("平台尚未添加行业类目！");
        }
    }

    @Override
    public AggregationAreaVo getAggregationAreaVo(String areaName) {
        return remotePaymentService.getAggregationAreaVo(areaName);
    }

    @Override
    public AggregationBankBranchVo getAggregationBankBranchVo(String cityName, String accountBranch) {
        return remotePaymentService.getAggregationBankBranchVo(cityName,accountBranch);
    }

    @Override
    public void apply(MiniAccountShopsApplyDto dto) {
        if(StringUtils.isEmpty(dto.getBankName())){
            throw new ServiceException("开户行不能为空！");
        }
        if(StringUtils.isEmpty(dto.getBankNo())){
            throw new ServiceException("银行卡号不能为空！");
        }

        if(StringUtils.isEmpty(dto.getBankNo())){
            throw new ServiceException("银行卡号不能为空！");
        }
        if(StringUtils.isEmpty(dto.getAccountUnionpay())){
            throw new ServiceException("开户银行支行的联行号不能为空！");
        }
        if(StringUtils.isEmpty(dto.getProvinceName())){
            throw new ServiceException("省名不能为空！");
        }
        if(StringUtils.isEmpty(dto.getCityName())){
            throw new ServiceException("市名不能为空！");
        }
        if(StringUtils.isEmpty(dto.getAreaName())){
            throw new ServiceException("区域名不能为空！");
        }
        if(StringUtils.isEmpty(dto.getAddress())){
            throw new ServiceException("详细地址不能为空！");
        }
        if(StringUtils.isEmpty(dto.getEmail())){
            throw new ServiceException("邮箱不能为空！");
        }

        //小程序用户id
        String userId = CurUserUtil.getHttpCurUser().getUserId();

        //验证小程序用户
        MiniAccount miniAccount = miniAccountService.getByShopUserId(userId);
        if(miniAccount == null){
            throw new ServiceException("小程序用户信息不存在！");
        }
        Result<ShopInfoDto> result = remoteMiniInfoService.getShopInfo();
        ShopInfoDto shopInfoDto = result.getData();
        if(shopInfoDto == null || (shopInfoDto!=null&& StringUtils.isEmpty(shopInfoDto.getMccCode()))){
            throw new ServiceException("平台尚未添加行业类目！");
        }

        MiniAccountExtends miniAccountExtends = miniAccountExtendsService.findByShopUserId(userId);
        MiniAccountShopsApply miniAccountShopsApply = new MiniAccountShopsApply();
        miniAccountShopsApply.setUserId(miniAccount.getUserId());
        Random rand = new Random();
        int randomNum = rand.nextInt(90000)+ 10000;

        //请求流水号
        miniAccountShopsApply.setMchReqNo(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS"))+randomNum);
        //商户类型
        miniAccountShopsApply.setMerType(MerTypeEnum.PERSON.getType());
        //商户简称
        miniAccountShopsApply.setMerName("个人账户"+miniAccount.getUserName());
        //客服电话
        miniAccountShopsApply.setMerPhone(miniAccount.getPhone());
        //MCC码商户行业编号
        miniAccountShopsApply.setMcc(shopInfoDto.getMccCode());
        //法人姓名
        miniAccountShopsApply.setLegalName(miniAccount.getUserName());
        //证件类型
        miniAccountShopsApply.setLegalCertType(CardTypeEnum.CARD.getType());
        //证件号码
        miniAccountShopsApply.setLegalNo(miniAccount.getCard());
        //法人身份证正面
        miniAccountShopsApply.setLegalFrontPic(remotePaymentService.uploadFile(miniAccountExtends.getIdCardFaceUrl(), FileTypeEnum.OPERATE.getType()));
        //法人身份证反面
        miniAccountShopsApply.setLegalBackPic(remotePaymentService.uploadFile(miniAccountExtends.getIdCardBackUrl(), FileTypeEnum.OPERATE.getType()));
        try {
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
            //证件有效期开始日期
            miniAccountShopsApply.setLegalCardS(format.parse(miniAccountExtends.getStartTime()).getTime()/1000+"");
            //证件有效期结束日期
            miniAccountShopsApply.setLegalCardE(format.parse(miniAccountExtends.getEndTime()).getTime()/1000+"");
        }catch (Exception e){
            e.printStackTrace();
        }
        //联系人姓名
        miniAccountShopsApply.setContactName(miniAccount.getUserName());
        //联系手机号码
        miniAccountShopsApply.setContactPone(miniAccount.getPhone());
        //商户所在省编码
        miniAccountShopsApply.setProvince(dto.getProvinceCode());
        //商户所在省名称
        miniAccountShopsApply.setProvinceName(dto.getProvinceName());
        //商户所在市编码
        miniAccountShopsApply.setCity(dto.getCityCode());
        //商户所在市名称
        miniAccountShopsApply.setCityName(dto.getCityName());
        //商户所在区县编码
        miniAccountShopsApply.setDistrict(dto.getAreaCode());
        //商户所在区县名称
        miniAccountShopsApply.setDistrictName(dto.getAreaName());
        //商户详细地址
        miniAccountShopsApply.setAddress(dto.getAddress());
        //账户类型
        miniAccountShopsApply.setAccountType(AccountTypeEnum.PERSON.getType());
        //结算类型
        miniAccountShopsApply.setAccountFlag(AccountFlagEnum.NO_LEGAL_PERSON.getType());
        //开户银行支行名称
        miniAccountShopsApply.setAccountBranch(dto.getBankName());
        //开户银行支行的联行号
        miniAccountShopsApply.setAccountUnionpay(dto.getAccountUnionpay());
        //开户名
        miniAccountShopsApply.setAccountHolder(dto.getBankUserName());
        //开户银行卡号
        miniAccountShopsApply.setBankNo(dto.getBankNo());
        //经营场所内设照片
        miniAccountShopsApply.setInsidePic(remotePaymentService.uploadFile(null, FileTypeEnum.OPERATE.getType()));
        //门头照
        miniAccountShopsApply.setDoorPic(remotePaymentService.uploadFile(null, FileTypeEnum.DOOR_HEADER.getType()));
        //收银台照片
        miniAccountShopsApply.setCashPic(remotePaymentService.uploadFile(null, FileTypeEnum.CASH_REGISTER.getType()));
        //邮箱
        miniAccountShopsApply.setEmail(dto.getEmail());
        miniAccountShopsApply.setBindFlag(BindFlagEnum.NO.getType());
        miniAccountShopsApply.setEditBaseFlag(EditBaseFlagEnum.NO.getType());
        miniAccountShopsApply.setEditCardFlag(EditCardFlagEnum.NO.getType());
        miniAccountShopsApply.setEditCardFailTimes(CommonConstants.NUMBER_ZERO);
        miniAccountShopsApply.setEditBaseFailTimes(CommonConstants.NUMBER_ZERO);

        miniAccountShopsApply.setApplyFlag(ApplyFlagEnum.YES.getType());
        miniAccountShopsApply.setApplyFailTimes(CommonConstants.NUMBER_ZERO);

        this.save(miniAccountShopsApply);

        MiniAccountShopsApplyAddMessage message = new MiniAccountShopsApplyAddMessage();
        message.setId(miniAccountShopsApply.getId());
        message.setTenantId(TenantContextHolder.getTenantId());
        sender.sendShopsApplyAdd(message);

    }
    @Override
    public String onlineSign(String reqNo) {
        String onlineSignUrl = "";
        //小程序用户id
        String userId = CurUserUtil.getHttpCurUser().getUserId();
        //验证小程序用户
        MiniAccount miniAccount = miniAccountService.getByShopUserId(userId);
        if(miniAccount == null){
            throw new ServiceException("小程序用户信息不存在！");
        }
        LambdaQueryWrapper<MiniAccountShopsApply>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MiniAccountShopsApply::getReqNo,reqNo);
        MiniAccountShopsApply miniAccountShopsApply = this.getOne(wrapper);
        if(miniAccountShopsApply!=null){
            log.info("聚合支付-网签开始:{}",reqNo);
            String sr = remotePaymentService.onlineSign(reqNo);
            log.info("聚合支付-网签结束:{}",sr);
            JSONObject jsonObject = JSON.parseObject(sr);
            String status = jsonObject.getString("status");
            if(status.equals(CommonConstants.APIZAA_RETURN_STATUS_SUCCESS)){
                onlineSignUrl = jsonObject.getString("online_sign_url");
            }
            return onlineSignUrl;
        }else{
            throw new ServiceException("小程序商户进件数据不存在！");
        }
    }

    @Override
    public List<MiniAccountShopsApply> getList() {
        //小程序用户id
        String userId = CurUserUtil.getHttpCurUser().getUserId();
        //验证小程序用户
        MiniAccount miniAccount = miniAccountService.getByShopUserId(userId);
        if(miniAccount == null){
            throw new ServiceException("小程序用户信息不存在！");
        }
        LambdaQueryWrapper<MiniAccountShopsApply>wrapper = new LambdaQueryWrapper<>();

        wrapper.eq(MiniAccountShopsApply::getUserId,miniAccount.getUserId());
        List<MiniAccountShopsApply> list = this.list(wrapper);
        if(list!=null&&list.size()>0){
            for (MiniAccountShopsApply miniAccountShopsApply : list) {
                //进件待审核
                //更新进件审核状态
                updateShopsApplyStatus(miniAccountShopsApply,CommonConstants.NUMBER_ZERO);
                //分账方关联
                updateShopsApplyBindFlag(miniAccountShopsApply);
            }
        }
        return list;
    }


    @Override
    public void edit(MiniAccountShopsApplyDto dto) {
        if(StringUtils.isEmpty(dto.getMchReqNo())){
            throw new ServiceException("请求流水号不能为空！");
        }
        if(StringUtils.isEmpty(dto.getBankName())){
            throw new ServiceException("开户行不能为空！");
        }
        if(StringUtils.isEmpty(dto.getBankNo())){
            throw new ServiceException("银行卡号不能为空！");
        }

        if(StringUtils.isEmpty(dto.getBankNo())){
            throw new ServiceException("银行卡号不能为空！");
        }
        if(StringUtils.isEmpty(dto.getAccountUnionpay())){
            throw new ServiceException("开户银行支行的联行号不能为空！");
        }
        if(StringUtils.isEmpty(dto.getProvinceName())){
            throw new ServiceException("省名不能为空！");
        }
        if(StringUtils.isEmpty(dto.getCityName())){
            throw new ServiceException("市名不能为空！");
        }
        if(StringUtils.isEmpty(dto.getAreaName())){
            throw new ServiceException("区域名不能为空！");
        }
        if(StringUtils.isEmpty(dto.getAddress())){
            throw new ServiceException("详细地址不能为空！");
        }
        if(StringUtils.isEmpty(dto.getEmail())){
            throw new ServiceException("邮箱不能为空！");
        }

        LambdaQueryWrapper<MiniAccountShopsApply>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MiniAccountShopsApply::getMchReqNo,dto.getMchReqNo());
        MiniAccountShopsApply miniAccountShopsApply = this.getOne(wrapper);

        if(miniAccountShopsApply.getChannelStatus()!=null&&
                miniAccountShopsApply.getChannelStatus().equals(ChannelStatusEnum.APPROVAL.getType())){
            throw new ServiceException("待审核状态不能编辑！");
        }


        if(miniAccountShopsApply!=null){

            MiniAccountShopsApplyUpdateMessage miniAccountShopsApplyUpdateMessage = new MiniAccountShopsApplyUpdateMessage();
            miniAccountShopsApplyUpdateMessage.setTenantId(TenantContextHolder.getTenantId());
            miniAccountShopsApplyUpdateMessage.setId(miniAccountShopsApply.getId());
            miniAccountShopsApplyUpdateMessage.setEditAddress(CommonConstants.NUMBER_ZERO);
            miniAccountShopsApplyUpdateMessage.setEditBank(CommonConstants.NUMBER_ZERO);
            if(!miniAccountShopsApply.getProvince().equals(dto.getProvinceCode())){
                //商户所在省编码
                miniAccountShopsApply.setProvince(dto.getProvinceCode());
                miniAccountShopsApply.setChannelStatus(ChannelStatusEnum.APPROVAL.getType());
                miniAccountShopsApply.setEditBaseFailTimes(CommonConstants.NUMBER_ZERO);
                miniAccountShopsApply.setEditBaseFlag(EditBaseFlagEnum.YES.getType());
                miniAccountShopsApplyUpdateMessage.setEditAddress(CommonConstants.NUMBER_ONE);
            }
            if(!miniAccountShopsApply.getCity().equals(dto.getCityCode())){
                //商户所在市编码
                miniAccountShopsApply.setCity(dto.getCityCode());
                miniAccountShopsApply.setChannelStatus(ChannelStatusEnum.APPROVAL.getType());
                miniAccountShopsApply.setEditBaseFailTimes(CommonConstants.NUMBER_ZERO);
                miniAccountShopsApply.setEditBaseFlag(EditBaseFlagEnum.YES.getType());
                miniAccountShopsApplyUpdateMessage.setEditAddress(CommonConstants.NUMBER_ONE);
            }
            if(!miniAccountShopsApply.getDistrict().equals(dto.getAreaCode())){
                //商户所在区县编码
                miniAccountShopsApply.setDistrict(dto.getAreaCode());
                miniAccountShopsApply.setChannelStatus(ChannelStatusEnum.APPROVAL.getType());
                miniAccountShopsApply.setEditBaseFailTimes(CommonConstants.NUMBER_ZERO);
                miniAccountShopsApply.setEditBaseFlag(EditBaseFlagEnum.YES.getType());
                miniAccountShopsApplyUpdateMessage.setEditAddress(CommonConstants.NUMBER_ONE);
            }
            if(!miniAccountShopsApply.getProvinceName().equals(dto.getProvinceName())){
                //商户所在省名称
                miniAccountShopsApply.setProvinceName(dto.getProvinceName());
            }
            if(!miniAccountShopsApply.getCityName().equals(dto.getCityName())){
                //商户所在市名称
                miniAccountShopsApply.setCityName(dto.getCityName());
            }
            if(!miniAccountShopsApply.getDistrictName().equals(dto.getAreaName())){
                //商户所在区县名称
                miniAccountShopsApply.setDistrictName(dto.getAreaName());
            }
            if(!miniAccountShopsApply.getAddress().equals(dto.getAddress())){
                //商户详细地址
                miniAccountShopsApply.setAddress(dto.getAddress());
                miniAccountShopsApply.setChannelStatus(ChannelStatusEnum.APPROVAL.getType());
                miniAccountShopsApply.setEditBaseFailTimes(CommonConstants.NUMBER_ZERO);
                miniAccountShopsApply.setEditBaseFlag(EditBaseFlagEnum.YES.getType());
                miniAccountShopsApplyUpdateMessage.setEditAddress(CommonConstants.NUMBER_ONE);
            }
            if(!miniAccountShopsApply.getEmail().equals(dto.getEmail())){
                //邮箱
                miniAccountShopsApply.setEmail(dto.getEmail());
                miniAccountShopsApply.setChannelStatus(ChannelStatusEnum.APPROVAL.getType());
                miniAccountShopsApply.setEditBaseFailTimes(CommonConstants.NUMBER_ZERO);
                miniAccountShopsApply.setEditBaseFlag(EditBaseFlagEnum.YES.getType());
                miniAccountShopsApplyUpdateMessage.setEditAddress(CommonConstants.NUMBER_ONE);
            }
            if(!miniAccountShopsApply.getAccountBranch().equals(dto.getBankName())){
                //开户银行支行名称
                miniAccountShopsApply.setAccountBranch(dto.getBankName());
                miniAccountShopsApply.setChannelStatus(ChannelStatusEnum.APPROVAL.getType());
                miniAccountShopsApply.setEditCardFailTimes(CommonConstants.NUMBER_ZERO);
                miniAccountShopsApply.setEditCardFlag(EditCardFlagEnum.YES.getType());
                miniAccountShopsApplyUpdateMessage.setEditBank(CommonConstants.NUMBER_ONE);
            }
            if(!miniAccountShopsApply.getAccountUnionpay().equals(dto.getAccountUnionpay())){
                //开户银行支行的联行号
                miniAccountShopsApply.setAccountUnionpay(dto.getAccountUnionpay());
                miniAccountShopsApply.setChannelStatus(ChannelStatusEnum.APPROVAL.getType());
                miniAccountShopsApply.setEditCardFailTimes(CommonConstants.NUMBER_ZERO);
                miniAccountShopsApply.setEditCardFlag(EditCardFlagEnum.YES.getType());
                miniAccountShopsApplyUpdateMessage.setEditBank(CommonConstants.NUMBER_ONE);
            }
            if(!miniAccountShopsApply.getAccountHolder().equals(dto.getBankUserName())){
                //开户名
                miniAccountShopsApply.setAccountHolder(dto.getBankUserName());
                miniAccountShopsApply.setChannelStatus(ChannelStatusEnum.APPROVAL.getType());
                miniAccountShopsApply.setEditCardFailTimes(CommonConstants.NUMBER_ZERO);
                miniAccountShopsApply.setEditCardFlag(EditCardFlagEnum.YES.getType());
                miniAccountShopsApplyUpdateMessage.setEditBank(CommonConstants.NUMBER_ONE);
            }
            if(!miniAccountShopsApply.getBankNo().equals(dto.getBankNo())){
                //开户银行卡号
                miniAccountShopsApply.setBankNo(dto.getBankNo());
                miniAccountShopsApply.setChannelStatus(ChannelStatusEnum.APPROVAL.getType());
                miniAccountShopsApply.setEditCardFailTimes(CommonConstants.NUMBER_ZERO);
                miniAccountShopsApply.setEditCardFlag(EditCardFlagEnum.YES.getType());
                miniAccountShopsApplyUpdateMessage.setEditBank(CommonConstants.NUMBER_ONE);
            }
            this.updateById(miniAccountShopsApply);

            if(miniAccountShopsApply.getChannelStatus()!=null){
                sender.sendShopsApplyUpdate(miniAccountShopsApplyUpdateMessage);
            }else{

                MiniAccountShopsApplyAddMessage message = new MiniAccountShopsApplyAddMessage();
                message.setId(miniAccountShopsApply.getId());
                message.setTenantId(TenantContextHolder.getTenantId());
                sender.sendShopsApplyAdd(message);
            }


        }else{
            throw new ServiceException("小程序商户进件数据不存在！");
        }
    }


    @Override
    public void shopsApplyAdd(MiniAccountShopsApplyAddMessage message) {

        MiniAccountShopsApply miniAccountShopsApply = this.getById(message.getId());
        if(miniAccountShopsApply != null){

            try {
                ShopsApplyDto shopsApplyDto = new ShopsApplyDto();
                BeanUtils.copyProperties(miniAccountShopsApply,shopsApplyDto);
                String jsonStr = remotePaymentService.shopsApply(shopsApplyDto);
                JSONObject jsonObject = JSON.parseObject(jsonStr);
                String status = jsonObject.getString("status");
                if(status.equals(CommonConstants.APIZAA_RETURN_STATUS_SUCCESS)){
                    miniAccountShopsApply.setReqNo(jsonObject.getString("req_no"));
                    miniAccountShopsApply.setChannelStatus(jsonObject.getString("channel_status"));
                    miniAccountShopsApply.setMerId(jsonObject.getString("mer_id"));
                    miniAccountShopsApply.setChannel(jsonObject.getString("channel"));
                    miniAccountShopsApply.setUsername(jsonObject.getString("username"));
                    miniAccountShopsApply.setPassword(jsonObject.getString("password"));
                    miniAccountShopsApply.setApikey(jsonObject.getString("apikey"));
                    miniAccountShopsApply.setSignkey(jsonObject.getString("signkey"));
                    miniAccountShopsApply.setApplyFlag(ApplyFlagEnum.NO.getType());
                    miniAccountShopsApply.setApplyFailTimes(CommonConstants.NUMBER_ZERO);
                    this.updateById(miniAccountShopsApply);
                    //更新进件审核状态
                    updateShopsApplyStatus(miniAccountShopsApply,CommonConstants.NUMBER_ZERO);
                    //分账方关联
                    updateShopsApplyBindFlag(miniAccountShopsApply);
                }else{
                    Integer applyFailTimes = miniAccountShopsApply.getApplyFailTimes() == null ? CommonConstants.NUMBER_ZERO : miniAccountShopsApply.getApplyFailTimes();
                    miniAccountShopsApply.setApplyFailTimes(applyFailTimes + 1);
                    this.updateById(miniAccountShopsApply);
                }
            }catch (Exception e){
                log.error("商户进件申请失败："+e.getMessage());
                Integer applyFailTimes = miniAccountShopsApply.getApplyFailTimes() == null ? CommonConstants.NUMBER_ZERO : miniAccountShopsApply.getApplyFailTimes();
                miniAccountShopsApply.setApplyFailTimes(applyFailTimes + 1);
                this.updateById(miniAccountShopsApply);
            }
        }
    }

    @Override
    public void shopsApplyUpdate(MiniAccountShopsApplyUpdateMessage message) {
        MiniAccountShopsApply miniAccountShopsApply = this.getById(message.getId());
        if(miniAccountShopsApply != null){
            ShopsApplyDto shopsApplyDto = new ShopsApplyDto();
            BeanUtils.copyProperties(miniAccountShopsApply,shopsApplyDto);
            //商户进件编辑
            if(message.getEditAddress()!=null
                    &&message.getEditAddress() == CommonConstants.NUMBER_ONE){
                try {
                    log.info("商户进件编辑:{}",shopsApplyDto.toString());
                    String jsonStr = remotePaymentService.shopsEdit(shopsApplyDto);
                    JSONObject jsonObject = JSON.parseObject(jsonStr);
                    String status = jsonObject.getString("status");
                    if(status.equals(CommonConstants.APIZAA_RETURN_STATUS_SUCCESS)){
                        miniAccountShopsApply.setEditBaseFlag(EditBaseFlagEnum.NO.getType());
                        miniAccountShopsApply.setEditBaseFailTimes(CommonConstants.NUMBER_ZERO);
                        if(miniAccountShopsApply.getEditBaseFlag()!=null
                                &&miniAccountShopsApply.getEditBaseFlag().equals(EditBaseFlagEnum.NO.getType())
                                &&miniAccountShopsApply.getEditCardFlag()!=null
                                &&miniAccountShopsApply.getEditCardFlag().equals(EditCardFlagEnum.NO.getType())){
                            miniAccountShopsApply.setChannelStatus(jsonObject.getString("edit_status"));
                        }
                        this.updateById(miniAccountShopsApply);
                    }else{
                        Integer editBaseFailTimes = miniAccountShopsApply.getEditBaseFailTimes() == null ? CommonConstants.NUMBER_ZERO : miniAccountShopsApply.getEditBaseFailTimes();
                        miniAccountShopsApply.setEditBaseFailTimes(editBaseFailTimes + 1);
                        this.updateById(miniAccountShopsApply);
                    }
                }catch (Exception e){
                    log.error("商户进件编辑失败："+e.getMessage());
                    Integer editBaseFailTimes = miniAccountShopsApply.getEditBaseFailTimes() == null ? CommonConstants.NUMBER_ZERO : miniAccountShopsApply.getEditBaseFailTimes();
                    miniAccountShopsApply.setEditBaseFailTimes(editBaseFailTimes + 1);
                    this.updateById(miniAccountShopsApply);
                }
            }
            //商户进件结算卡编辑
            if(message.getEditBank()!=null
                    &&message.getEditBank() == CommonConstants.NUMBER_ONE){
                try {
                    log.info("商户进件结算卡编辑:{}",shopsApplyDto.toString());
                    String jsonStr = remotePaymentService.cardEdit(shopsApplyDto);
                    JSONObject jsonObject = JSON.parseObject(jsonStr);
                    String status = jsonObject.getString("status");
                    if(status.equals(CommonConstants.APIZAA_RETURN_STATUS_SUCCESS)){
                        miniAccountShopsApply.setEditCardFlag(EditCardFlagEnum.NO.getType());
                        miniAccountShopsApply.setEditCardFailTimes(CommonConstants.NUMBER_ZERO);
                        if(miniAccountShopsApply.getEditBaseFlag()!=null
                                &&miniAccountShopsApply.getEditBaseFlag().equals(EditBaseFlagEnum.NO.getType())
                                &&miniAccountShopsApply.getEditCardFlag()!=null
                                &&miniAccountShopsApply.getEditCardFlag().equals(EditCardFlagEnum.NO.getType())){
                            miniAccountShopsApply.setChannelStatus(jsonObject.getString("edit_status"));
                        }
                        this.updateById(miniAccountShopsApply);
                    }else{
                        Integer editCardFailTimes = miniAccountShopsApply.getEditCardFailTimes() == null ? CommonConstants.NUMBER_ZERO : miniAccountShopsApply.getEditCardFailTimes();
                        miniAccountShopsApply.setEditCardFailTimes(editCardFailTimes + 1);
                        this.updateById(miniAccountShopsApply);
                    }
                }catch (Exception e){
                    log.error("商户进件结算卡编辑失败："+e.getMessage());
                    Integer editCardFailTimes = miniAccountShopsApply.getEditCardFailTimes() == null ? CommonConstants.NUMBER_ZERO : miniAccountShopsApply.getEditCardFailTimes();
                    miniAccountShopsApply.setEditCardFailTimes(editCardFailTimes + 1);
                    this.updateById(miniAccountShopsApply);
                }


            }
            //更新进件审核状态
            updateShopsApplyStatus(miniAccountShopsApply,CommonConstants.NUMBER_ONE);
            //分账方关联
            updateShopsApplyBindFlag(miniAccountShopsApply);
        }
    }

    @Override
    public void updateShopsApplyStatus(MiniAccountShopsApply miniAccountShopsApply,Integer type) {
        //进件待审核
        if(miniAccountShopsApply.getChannelStatus()!=null
                &&miniAccountShopsApply.getChannelStatus().equals(ChannelStatusEnum.APPROVAL.getType())
                &&miniAccountShopsApply.getEditBaseFlag()!=null
                &&miniAccountShopsApply.getEditBaseFlag().equals(EditBaseFlagEnum.NO.getType())
                &&miniAccountShopsApply.getEditCardFlag()!=null
                &&miniAccountShopsApply.getEditCardFlag().equals(EditCardFlagEnum.NO.getType())
                &&miniAccountShopsApply.getApplyFlag()!=null
                &&miniAccountShopsApply.getApplyFlag().equals(ApplyFlagEnum.NO.getType())){
            if(type == CommonConstants.NUMBER_ZERO){
                log.info("聚合支付-商户进件状态查询开始:{}",miniAccountShopsApply.getReqNo());
                String sr = remotePaymentService.getShopsApply(miniAccountShopsApply.getReqNo());
                log.info("聚合支付-商户进件状态查询结束:{}",sr);
                JSONObject srJsonObject = JSON.parseObject(sr);
                String srStatus = srJsonObject.getString("status");
                if(srStatus.equals(CommonConstants.APIZAA_RETURN_STATUS_SUCCESS)){
                    miniAccountShopsApply.setChannelStatus(srJsonObject.getString("channel_status"));
                    this.updateById(miniAccountShopsApply);
                }
            }
            if(type == CommonConstants.NUMBER_ONE){
                String sr = remotePaymentService.editQuery(miniAccountShopsApply.getReqNo());
                JSONObject srJsonObject = JSON.parseObject(sr);
                String srStatus = srJsonObject.getString("status");
                if(srStatus.equals(CommonConstants.APIZAA_RETURN_STATUS_SUCCESS)){
                    miniAccountShopsApply.setChannel(srJsonObject.getString("edit_status"));
                    this.updateById(miniAccountShopsApply);
                }
            }
        }

    }

    @Override
    public void updateShopsApplyBindFlag(MiniAccountShopsApply miniAccountShopsApply) {
        //进件审核通过
        if(miniAccountShopsApply.getChannelStatus()!=null&&miniAccountShopsApply.getChannelStatus().equals(ChannelStatusEnum.SUCCESS.getType())
                &&!miniAccountShopsApply.getBindFlag().equals(BindFlagEnum.YES.getType())){

            ShopConfigDto shopConfig = remoteMiniInfoService.getShopConfig();
            if(shopConfig == null){
                throw new ServiceException("店铺相关配置不存在");
            }
            PayInfoVo payInfo = shopConfig.getPayInfo();
            if(payInfo == null){
                throw new ServiceException("支付相关配置不存在");
            }

            if(StringUtils.isEmpty(payInfo.getZzaMchNo())){
                throw new ServiceException("聚合支付收款商户E商户号不存在");
            }
            String sharingMerchantId = payInfo.getZzaMchNo();
            String receiverMerchantId = miniAccountShopsApply.getMerId();
            String receiverMerchantName = miniAccountShopsApply.getMerName();
            log.info("聚合支付-分账方关联开始:{},{},{}",sharingMerchantId,receiverMerchantId,receiverMerchantName);
            String sr = remotePaymentService.sharingReceiverBind(sharingMerchantId,receiverMerchantId,receiverMerchantName);
            log.info("聚合支付-分账方关联结束:{}",sr);
            JSONObject srJsonObject = JSON.parseObject(sr);
            String srStatus = srJsonObject.getString("status");
            if(srStatus.equals(CommonConstants.APIZAA_RETURN_STATUS_SUCCESS)){
                miniAccountShopsApply.setBindFlag(BindFlagEnum.YES.getType());
                this.updateById(miniAccountShopsApply);
            }
        }
    }

    @Override
    public void updateEditBaseFlag() {
        LambdaQueryWrapper<MiniAccountShopsApply>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MiniAccountShopsApply::getEditBaseFlag,EditBaseFlagEnum.YES.getType());
        wrapper.le(MiniAccountShopsApply::getEditBaseFailTimes,CommonConstants.NUMBER_FIVE);
        List<MiniAccountShopsApply> list = this.list(wrapper);
        if(list!=null&&list.size()>0){
            for (MiniAccountShopsApply miniAccountShopsApply : list) {
                MiniAccountShopsApplyUpdateMessage message = new MiniAccountShopsApplyUpdateMessage();
                message.setTenantId(miniAccountShopsApply.getTenantId());
                message.setId(miniAccountShopsApply.getId());
                message.setEditAddress(CommonConstants.NUMBER_ONE);
                sender.sendShopsApplyUpdate(message);
            }
        }
    }
    @Override
    public void updateEditCardFlag() {
        LambdaQueryWrapper<MiniAccountShopsApply>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MiniAccountShopsApply::getEditCardFlag,EditCardFlagEnum.YES.getType());
        wrapper.le(MiniAccountShopsApply::getEditCardFailTimes,CommonConstants.NUMBER_FIVE);
        List<MiniAccountShopsApply> list = this.list(wrapper);
        if(list!=null&&list.size()>0){
            for (MiniAccountShopsApply miniAccountShopsApply : list) {
                MiniAccountShopsApplyUpdateMessage message = new MiniAccountShopsApplyUpdateMessage();
                message.setTenantId(miniAccountShopsApply.getTenantId());
                message.setId(miniAccountShopsApply.getId());
                message.setEditBank(CommonConstants.NUMBER_ONE);
                sender.sendShopsApplyUpdate(message);
            }
        }
    }

    @Override
    public void updateApplyFlag() {
        LambdaQueryWrapper<MiniAccountShopsApply>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MiniAccountShopsApply::getApplyFlag,ApplyFlagEnum.YES.getType());
        wrapper.le(MiniAccountShopsApply::getApplyFailTimes,CommonConstants.NUMBER_FIVE);
        List<MiniAccountShopsApply> list = this.list(wrapper);
        if(list!=null&&list.size()>0){
            for (MiniAccountShopsApply miniAccountShopsApply : list) {
                MiniAccountShopsApplyAddMessage message = new MiniAccountShopsApplyAddMessage();
                message.setId(miniAccountShopsApply.getId());
                message.setTenantId(TenantContextHolder.getTenantId());
                sender.sendShopsApplyAdd(message);
            }
        }
    }
}
