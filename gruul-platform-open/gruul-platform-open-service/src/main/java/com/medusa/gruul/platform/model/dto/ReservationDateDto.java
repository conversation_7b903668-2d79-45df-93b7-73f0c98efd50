package com.medusa.gruul.platform.model.dto;

import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;


@Data
public class ReservationDateDto {



    /**
     * 预约开始时间
     */
    private LocalDateTime startTime;
    /**
     * 预约结束时间
     */
    private LocalDateTime endTime;


    /**
     * 当前显示的日期
     */
    private LocalDate showDayDate;
    /**
     * 显示的开始时间
     */
    private LocalTime showDayStart;
    /**
     * 显示的结束时间
     */
    private LocalTime showDayEnd;
    /**
     * 显示的日期列表
     */
    private List<LocalDate> showDateList; // 日期列表

}