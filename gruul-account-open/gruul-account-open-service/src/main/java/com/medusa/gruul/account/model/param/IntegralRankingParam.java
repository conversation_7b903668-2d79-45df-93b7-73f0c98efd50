package com.medusa.gruul.account.model.param;

import com.medusa.gruul.common.core.param.QueryParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 14:41 2025/3/19
 */
@Data
@ApiModel(value = "IntegralRankingParam 实体", description = "积分排行 param")
public class IntegralRankingParam  extends QueryParam {

    @ApiModelProperty(value = "创建时间开始时间")
    private String startTime;

    @ApiModelProperty(value = "创建时间结束时间")
    private String endTime;

    @ApiModelProperty(value = "客户昵称")
    private String nikeName;

    @ApiModelProperty(value = "客户号码")
    private String phone;

    @ApiModelProperty(value = "根据销售积分排序：1.正序；2.倒序")
    private Integer saleIntegralSort;

    @ApiModelProperty(value = "根据普通积分排序：1.正序；2.倒序")
    private Integer integralSort;

    @ApiModelProperty(value = "根据总积分排序：1.正序；2.倒序")
    private Integer allIntegralSort;

    @ApiModelProperty(value = "客户总积分为零不显示")
    private Boolean allIntegralFlag;

}
