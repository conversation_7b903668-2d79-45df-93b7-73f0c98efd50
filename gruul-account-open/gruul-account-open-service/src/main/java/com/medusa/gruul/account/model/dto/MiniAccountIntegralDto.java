package com.medusa.gruul.account.model.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Author: plh
 * @Description: 用户积分Dto
 * @Date: Created in 15:30 2023/8/22
 */
@Data
public class MiniAccountIntegralDto {

    @ApiModelProperty(value = "id")
    private String id;

    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id")
    private String userId;

    /**
     * 订单id
     */
    @ApiModelProperty(value = "订单id")
    private String orderId;

    /**
     * 积分类型：1.销售获取积分；2.手动添加积分；3.手动减少积分；4.销售积分兑换；5.推荐下级；6.注册；7.登录；8.购通惠证；9.下级下单；99.订单删除
     */
    @ApiModelProperty(value = "积分类型：1.销售获取积分；2.手动添加积分；3.手动减少积分；4.销售积分兑换；5.推荐下级；6.注册；7.登录；8.购通惠证；9.下级下单；99.订单删除")
    private Integer type;

    /**
     * 积分
     */
    @ApiModelProperty(value = "积分")
    private BigDecimal integral;

    /**
     *备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;


    /**
     * 变动前积分
     */
    @ApiModelProperty(value = "变动前积分")
    private BigDecimal lastIntegral;

    /**
     * 变动后积分
     */
    @ApiModelProperty(value = "变动后积分")
    private BigDecimal totalIntegral;

    /**
     * 积分来源:0-系统，1-后台
     */
    @ApiModelProperty(value = "积分来源:0-系统，1-后台")
    private Integer source;

    /**
     * 平台操作用户id
     */
    @ApiModelProperty(value = "平台操作用户id")
    private String platformUserId;

    /**
     * 平台操作用户名
     */
    @ApiModelProperty(value = "平台操作用户名")
    private String platformUserName;

}
