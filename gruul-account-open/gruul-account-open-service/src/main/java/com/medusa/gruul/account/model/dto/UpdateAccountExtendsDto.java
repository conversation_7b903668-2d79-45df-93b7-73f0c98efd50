package com.medusa.gruul.account.model.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 9:30 2025/5/27
 */
@Data
@ApiModel(value = "上传用户微信收款码/支付宝收款码")
public class UpdateAccountExtendsDto {

    /**
     * 微信收款码URL
     */
    @ApiModelProperty(value = "微信收款码URL")
    private String wxAccountUrl;

    /**
     * 支付宝收款码URL
     */
    @ApiModelProperty(value = "支付宝收款码URL")
    private String alipayAccountUrl;


}
