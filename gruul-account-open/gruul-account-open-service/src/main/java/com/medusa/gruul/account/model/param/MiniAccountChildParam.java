package com.medusa.gruul.account.model.param;

import com.medusa.gruul.common.core.param.QueryParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 17:02 2024/5/13
 */
@Data
public class MiniAccountChildParam extends QueryParam {

    @ApiModelProperty(value = "会员昵称")
    private String nikeName;

    @ApiModelProperty(value = "会员电话")
    private String phone;

    @ApiModelProperty(value = "客户等级")
    private String memberLevel;

    @ApiModelProperty(value = "上级用户Id")
    private String parentId;

    @ApiModelProperty(value = "用户Id")
    private String userId;

    @ApiModelProperty(value = "当前用户上级用户Id")
    private String tParentId;

    @ApiModelProperty(value = "当前用户上上级用户Id")
    private String tAboveParentId;

}
