package com.medusa.gruul.account.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class MemberLevelRightsDto {

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "类型")
    private String type;

    @ApiModelProperty(value = "权益名称")
    private String name;

    @ApiModelProperty(value = "权益说明")
    private String powerExplain;

    @ApiModelProperty(value = "是否启用")
    private Integer enable;

    @ApiModelProperty(value = "图标")
    private String icon;

    @ApiModelProperty(value = "值")
    private String value;

    @ApiModelProperty(value = "是否选择  0--未选择1--已选择")
    private Integer isSelected;

}
