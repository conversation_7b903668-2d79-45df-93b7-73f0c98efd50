package com.medusa.gruul.platform.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;

/**
 * 职员排班DTO
 * 
 * <AUTHOR>
 * @date 2025-01-25
 */
@Data
@ApiModel(value = "EmployeeScheduleDto对象", description = "职员排班DTO")
public class EmployeeScheduleDto {

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "职员ID")
    private Long employeeId;

    @ApiModelProperty(value = "职员ID,设置多位")
    private List<Long> employeeIds;

    @ApiModelProperty(value = "排班日期")
    private LocalDate scheduleDate;

    @ApiModelProperty(value = "工作状态：1-上班，2-休息，3-请假")
    @NotNull(message = "工作状态不能为空")
    private Integer workStatus;

    @ApiModelProperty(value = "班次：1-上午，2-下午，3-晚上")
    private Integer shiftType;

    @ApiModelProperty(value = "上班时间")
    private LocalTime startTime;

    @ApiModelProperty(value = "下班时间")
    private LocalTime endTime;

    @ApiModelProperty(value = "门店ID")
    private String storeFrontId;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "shopId")
    private String shopId;
}
