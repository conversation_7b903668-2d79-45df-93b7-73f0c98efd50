package com.medusa.gruul.platform.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.medusa.gruul.platform.api.entity.ReservationOrderEvaluate;

import com.medusa.gruul.platform.model.param.ReservationOrderEvaluateQueryParam;
import com.medusa.gruul.platform.model.vo.ReservationOrderEvaluateVo;
import org.apache.ibatis.annotations.Param;

/**
 * 预约单评价表 Mapper 接口
 *
 * <AUTHOR>
 * @Date: 2025-06-11
 */
public interface ReservationOrderEvaluateMapper extends BaseMapper<ReservationOrderEvaluate> {

    /**
     * 分页查询预约单评价列表
     *
     * @param page  分页参数
     * @param param 查询参数
     * @return 预约单评价列表
     */
    IPage<ReservationOrderEvaluateVo> getPage(IPage<ReservationOrderEvaluateVo> page, @Param("param") ReservationOrderEvaluateQueryParam param);
} 