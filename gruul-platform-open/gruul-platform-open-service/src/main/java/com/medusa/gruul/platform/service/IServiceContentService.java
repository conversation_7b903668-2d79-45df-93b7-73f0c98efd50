package com.medusa.gruul.platform.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.medusa.gruul.platform.api.entity.ServiceContent;
import com.medusa.gruul.platform.api.model.dto.MessageTemplateCopyDto;
import com.medusa.gruul.platform.model.dto.ServiceContentDto;
import com.medusa.gruul.platform.model.param.ServiceContentParam;
import com.medusa.gruul.platform.model.vo.ServiceContentVo;


/**
 * 服务项目Service接口
 */
public interface IServiceContentService extends IService<ServiceContent> {

    /**
     * 分页查询服务项目列表
     *
     * @param queryDto 查询条件
     * @return 分页结果
     */
    IPage<ServiceContentVo> page(ServiceContentParam queryDto);

    /**
     * 新增服务项目
     *
     * @param dto 服务项目DTO
     * @return 是否成功
     */
    boolean add(ServiceContentDto dto);

    /**
     * 修改服务项目
     *
     * @param dto 服务项目DTO
     * @return 是否成功
     */
    boolean update(ServiceContentDto dto);

    /**
     * 删除服务项目
     *
     * @param id ID
     * @return 是否成功
     */
    boolean delete(Long id);

    /**
     * 检查服务项目名称是否重复
     *
     * @param serviceName 服务项目名称
     * @param id ID，新增时为null
     * @return 是否重复
     */
    boolean checkServiceNameExists(String serviceName, Long id);

    /**
     * 复制模板
     *
     * @param messageTemplateCopyDto 适用类型
     */
    void copyFromTemplate(MessageTemplateCopyDto messageTemplateCopyDto);
} 