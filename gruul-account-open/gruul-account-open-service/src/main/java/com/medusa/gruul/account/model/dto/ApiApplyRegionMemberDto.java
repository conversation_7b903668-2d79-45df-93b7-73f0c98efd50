package com.medusa.gruul.account.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 16:26 2025/6/28
 */
@Data
@ApiModel(value = "区域会员申请参数")
public class ApiApplyRegionMemberDto {

    @ApiModelProperty(value = "区域类型->1-区/县、2-市级、3-省级")
    private Integer regionType;

    @ApiModelProperty(value = "代理的区域代码")
    private String regionCode;

    @ApiModelProperty(value = "申请会员等级ID")
    private String applyMemberLevelId;

    @ApiModelProperty(value = "会员类型id")
    private Long memberTypeId;

    @ApiModelProperty(value = "代理的区域名称")
    private String regionName;
}
