package com.medusa.gruul.platform.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.medusa.gruul.common.core.util.Result;
import com.medusa.gruul.platform.api.entity.ReservationOrder;
import com.medusa.gruul.platform.api.enums.ReservationEnums;
import com.medusa.gruul.platform.model.dto.*;

import com.medusa.gruul.platform.model.vo.*;

import java.util.List;

/**
 * 预约服务接口
 *
 * <AUTHOR>
 * @since 2025-06-10
 */
public interface IReservationOrderService extends IService<ReservationOrder> {

    /**
     * 查询启用状态的服务项目列表
     *
     * @return 服务项目列表
     */
    List<ServiceContentVo> listEnabledServices();

    /**
     * 根据工作状态查询职员列表包含职位信息，null可查询全
     *
     * @return 职员列表
     */
    List<ReservationEmployeeVo> listEmployeesWithPosition(EmployeeScheduleDto params);

    /**
     * 获取可预约时间段列表
     *  废弃
     * @param queryDto 查询参数
     * @return 时间段列表
     */
    List<ReservationEmployeeVo>  listAvailableEmployees(TimeSlotQueryDto queryDto);
    /**
     * 获取当前时间段可预约时间段列表
     * @param queryDto 查询参数
     * @return 时间段列表
     */
    ReservationTimeSlotVo getTimeSlots(TimeSlotQueryDto queryDto);
    /**
     * 提交预约
     *
     * @param submitDto 预约提交DTO
     * @return 预约结果
     */
    Result<String> submitReservation(ReservationSubmitDto submitDto);

    /**
     * 获取用户预约列表
     *
     * @param queryDto 查询参数
     * @return 预约列表
     */
    List<ReservationOrderVo> getUserReservations(ReservationPageQueryDto queryDto);

    /**
     * 取消预约
     *
     * @param id 预约ID
     * @return result
     */
    Result<String>  cancelReservation(Long id,boolean isAutoCancel);

    /**
     * 完成预约（将状态变更为待评价）
     *
     * @param id 预约ID
     * @return result
     */
    Result<String> completeReservation(Long id);
    
    /**
     * 分页查询预约记录列表
     *
     * @param queryDto 查询参数
     * @return 分页预约记录列表
     */
    IPage<ReservationOrderVo> pageReservations(ReservationPageQueryDto queryDto);
    
    /**
     * 获取预约详情
     *
     * @param id 预约ID
     * @return 预约详情
     */
    ReservationOrderVo getReservationDetail(Long id);
} 