package com.medusa.gruul.account.model.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 15:18 2025/5/20
 */
@Data
@ApiModel(value = "MemberLevelParam 实体", description = "会员等级查询 param")
public class MemberLevelParam {

    /**
     * 会员类型id
     */
    @ApiModelProperty(value = "会员类型id")
    private Long memberTypeId;

}
