<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.medusa.gruul.platform.mapper.PlatformDepartmentMapper">
    <!--平台部门信息结果集 -->
    <resultMap id="DepartmentVoMap" type="com.medusa.gruul.platform.api.model.vo.DepartmentVo">
        <id column="id" property="id"/>
        <result column="dept_code" property="deptCode"/>
        <result column="dept_full_name" property="deptFullName"/>
        <result column="department_name" property="departmentName"/>
        <result column="stop_open_state" property="stopOpenState"/>
        <result column="employee_id" property="employeeId"/>
        <result column="employee_full_name" property="employeeFullName"/>
        <result column="default_flag" property="defaultFlag"/>
        <result column="class_code" property="classCode"/>
    </resultMap>
    <select id="searchDepartment" resultMap="DepartmentVoMap">
        select
            t1.id,t1.dept_code,t1.dept_full_name,t1.department_name,t1.stop_open_state,t1.employee_id,t1.employee_full_name,
            t1.default_flag,t1.class_code
        from
            t_platform_lyd_department t1
        where t1.is_deleted = 0
        <if test="params.deptCode!=null">
            and  t1.dept_code LIKE CONCAT('%',#{params.deptCode},'%')
        </if>
        <if test="params.deptFullName!=null">
            and  t1.dept_full_name LIKE CONCAT('%',#{params.deptFullName},'%')
        </if>
        <if test="params.departmentName!=null">
            and  t1.department_name LIKE CONCAT('%',#{params.departmentName},'%')
        </if>
        <if test="params.stopOpenState!=null and params.stopOpenState!=''">
            and  t1.stop_open_state = #{params.stopOpenState}
        </if>
        <if test="params.employeeFullName!=null and params.employeeFullName!=''">
            and  t1.employee_full_name LIKE CONCAT('%',#{params.employeeFullName},'%')
        </if>
    </select>
</mapper>
