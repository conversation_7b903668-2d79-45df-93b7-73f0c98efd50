package com.medusa.gruul.platform.model.param;

import com.medusa.gruul.common.core.param.QueryParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 16:09 2024/11/6
 */
@Data
public class WxMessageTemplateParam extends QueryParam {

    @ApiModelProperty(value = "模板标识->1.活动通知;2.生日祝福提醒;3.商品上新;4.卖家发货提醒")
    private String code;

    @ApiModelProperty(value = "模板名称")
    private String name;

    @ApiModelProperty(value = "模板类型->1.订阅消息;2.公众号模板消息")
    private Integer type;

    @ApiModelProperty(value = "模板状态->1.启用;2.停用")
    private Integer status;

}
