package com.medusa.gruul.platform.aspect;

import com.medusa.gruul.common.core.exception.ServiceException;
import com.medusa.gruul.common.core.util.StringUtil;
import com.medusa.gruul.common.data.tenant.ShopContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;

@Aspect
@Component
@Slf4j
public class AspectShopIdAutoSet{

    @Around("execution(* com.medusa.gruul.platform.web.controller.api.ApiReservationOrderController.*(..))")
    public Object autoSetShouId(ProceedingJoinPoint pjp) throws Throwable {
        log.info("拦截请求 AspectShopIdAutoSet 中的方法，设置 shopId 到上下文");
        if (ShopContextHolder.getShopId()!= null){
            return pjp.proceed(pjp.getArgs());
        }
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = null;
        String shouId = null;

        // 获取请求的路径
        String requestURI = request.getRequestURI();

        // 如果是 user-list 接口，直接放行
        if (requestURI.contains("user-list")) {
            return pjp.proceed(pjp.getArgs());
        }
        if (attributes != null) {
            request = attributes.getRequest();
            shouId = request.getHeader("shopId");
        }

        if (StringUtil.isBlank(shouId)) {
            if ("GET".equalsIgnoreCase(request.getMethod())) {
                log.info("成功获取shopId：{}",shouId);
                shouId = request.getParameter("shopId") ;
            } else {
                Object[] args = pjp.getArgs();
                if (args.length > 0 && args[0] != null) {
                    try {
                        Method getShouId = args[0].getClass().getMethod("getShouId");
                        if (getShouId != null) {
                            Object invoke = getShouId.invoke(args[0]);
                            shouId = invoke != null ? invoke.toString() : null;
                            log.info("成功获取shopId：{}",shouId);
                        }
                    } catch (NoSuchMethodException | IllegalAccessException e) {
                        throw new ServiceException("缺少参数：商铺信息shopId");
                    }
                }
            }
        }

        if (StringUtil.isBlank(shouId)) {
            throw new ServiceException("缺少参数：商铺信息shopId");
        } else {
            ShopContextHolder.setShopId(shouId);
        }

        return pjp.proceed(pjp.getArgs()); // 确保参数正确传递给目标方法
    }

}