package com.medusa.gruul.account.model.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 9:56 2025/3/18
 */
@Data
@ApiModel(value = "新增/修改会员等级规则商品dto")
public class MemberLevelRuleProductDto {



    /**
     * 商品id
     */
    @ApiModelProperty(value = "商品id")
    private String productId;

    /**
     * 规格id
     */
    @ApiModelProperty(value = "规格id")
    private String skuId;

    /**
     * 会员类型id
     */
    @ApiModelProperty(value = "会员类型id")
    private Long memberTypeId;

}
