package com.medusa.gruul.platform.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: plh
 * @Description: 小程序备案信息dto
 * @Date: Created in 15:25 2024/5/29
 */
@Data
public class MiniReserveUpdateDto {
    @ApiModelProperty(value = "id", hidden = true)
    private String id;
    /**
     * 小程序预备号
     */
    @ApiModelProperty(value = "小程序预备号")
    private String reserveNumber;
    /**
     * 公司
     */
    @ApiModelProperty(value = "公司")
    private String company;

    /**
     * 技术公司
     */
    @ApiModelProperty(value = "技术公司")
    private String technologyCompany;

    /**
     * 隐私协议
     */
    @ApiModelProperty(value = "隐私协议")
    private String privacyAgreement;
    /**
     * 隐私协议地址
     */
    @ApiModelProperty(value = "隐私协议地址")
    private String privacyAgreementUrl;

}
