<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.medusa.gruul.platform.mapper.SpecialSettingMapper">
    <select id="getSpecialSettingShopIds" resultType="java.lang.String">
        select
            shop_id
        from
            t_special_setting
        where shop_allow_platform_search_order = 1 and shop_id != #{shopId}

    </select>
</mapper>
