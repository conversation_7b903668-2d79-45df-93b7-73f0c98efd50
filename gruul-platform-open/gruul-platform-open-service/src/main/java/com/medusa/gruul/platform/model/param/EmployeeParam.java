package com.medusa.gruul.platform.model.param;

import com.medusa.gruul.common.core.param.QueryParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;

/**
 * @Author: plh
 * @Description: 职员信息查询参数
 * @Date: Created in 10:59 2024/9/13
 */
@Data
public class EmployeeParam extends QueryParam {

    /**
     * 职员编号
     */
    @ApiModelProperty(value = "职员编号")
    private String empNumber;

    /**
     * 部门
     */
    @ApiModelProperty(value = "部门")
    private String departmentName;

    /**
     * 部门标识
     */
    @ApiModelProperty(value = "部门标识")
    private String departmentCode;

    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    private String empFullName;

    /**
     * 绑定仓库名称
     */
    @ApiModelProperty(value = "绑定仓库名称")
    private String warehouseFullName;

    /**
     * 绑定的用户id
     */
    @ApiModelProperty(value = "用户id")
    private String userId;

    /**
     * 工作状态：1-上班，2-休息，3-请假
     */
    @ApiModelProperty(value = "工作状态：1-上班，2-休息，3-请假")
    private Integer workStatus;

    /**
     * 排班日期 默认为当天
     */
    @ApiModelProperty(value = "排班日期")
    private LocalDate scheduleDate = LocalDate.now();

}
