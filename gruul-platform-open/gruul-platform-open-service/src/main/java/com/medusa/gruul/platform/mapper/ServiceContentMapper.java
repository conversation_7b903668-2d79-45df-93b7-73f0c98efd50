package com.medusa.gruul.platform.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.medusa.gruul.platform.api.entity.ServiceContent;
import com.medusa.gruul.platform.model.param.ServiceContentParam;
import com.medusa.gruul.platform.model.vo.ServiceContentVo;
import org.apache.ibatis.annotations.Param;

/**
 * 服务项目Mapper接口
 */
public interface ServiceContentMapper extends BaseMapper<ServiceContent> {
    
    /**
     * 分页查询服务项目列表
     *
     * @param page 分页参数
     * @param queryDto 查询条件
     * @return 分页结果
     */
    IPage<ServiceContentVo> selectServiceContentList(Page<ServiceContent> page,
                                                   @Param("params") ServiceContentParam queryDto);


} 