package com.medusa.gruul.account.model.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @description
 * @data: 2019/11/27
 */
@Data
public class UpdateUserBaseInfoDto {
    @ApiModelProperty(value = "昵称")
    private String nickName;
    @ApiModelProperty(value = "预留昵称字段")
    private String nikeName;
    @ApiModelProperty(value = "性别")
    private Integer gender;
    @ApiModelProperty(value = "语言")
    private String language;
    @ApiModelProperty(value = "城市")
    private String city;
    @ApiModelProperty(value = "省")
    private String province;
    @ApiModelProperty(value = "国家")
    private String country;
    @ApiModelProperty(value = "头像")
    private String avatarUrl;
    @ApiModelProperty(value = "手机号")
    private String phone;
    @ApiModelProperty(value = "短信验证码")
    private String smsCode;
    @ApiModelProperty(value = "邀请人邀请码")
    private String inviterInviteCode;
    @ApiModelProperty(value = "校验类型,1001-短信登录校验," +
            "1002-账号注册验证手机号," +
            "1003-用户手机号换绑校验," +
            "1004-用户修改密码," +
            "1005-用户信息修改," +
            "1006-用户删除自身店铺," +
            "1007-用户忘记密码找回," +
            "1008-代理申请," +
            "1009-代理银行卡手机号校验," +
            "1010-代理银行卡手机号换绑," +
            "1011-代理账号换绑," +
            "1012-代理修改密码," +
            "1013-修改支付密码," +
            "1014-验证代用户下单," +
            "1015-支付短信验证码," +
            "")
    private Integer type;

    @ApiModelProperty(value = "权益包分享参数")
    private String packageCodeShareParam;
}
