package com.medusa.gruul.platform.model.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 服务项目查询DTO
 */
@Data
@ApiModel(value = "服务项目查询DTO", description = "服务项目查询DTO")
public class ServiceContentParam {

    /**
     * 服务项目名称
     */
    @ApiModelProperty(value = "服务项目名称")
    private String serviceName;

    /**
     * 状态(0-停用, 1-启用)
     */
    @ApiModelProperty(value = "状态(0-停用, 1-启用)")
    private Integer status;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    
    /**
     * 当前页
     */
    @ApiModelProperty(value = "当前页")
    private Integer current = 1;
    
    /**
     * 每页条数
     */
    @ApiModelProperty(value = "每页条数")
    private Integer size = 10;
} 