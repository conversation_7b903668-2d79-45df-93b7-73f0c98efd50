package com.medusa.gruul.platform.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.medusa.gruul.platform.api.entity.PlatformPositionTemplate;
import com.medusa.gruul.platform.model.param.PlatformPositionTemplateParam;

/**
 * <p>
 * 商家职位模板表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
public interface IPlatformPositionTemplateService extends IService<PlatformPositionTemplate> {

    /**
     * 查询职位模板列表
     *
     * @param templateParam 入参
     * @return 分页结果
     */
    IPage<PlatformPositionTemplate> getPositionTemplateList(PlatformPositionTemplateParam templateParam);

    /**
     * 新增职位模板
     *
     * @param platformPositionTemplate 职位模板信息
     * @return 是否成功
     */
    boolean savePositionTemplate(PlatformPositionTemplate platformPositionTemplate);

    /**
     * 修改职位模板
     *
     * @param platformPositionTemplate 职位模板信息
     * @return 是否成功
     */
    boolean updatePositionTemplate(PlatformPositionTemplate platformPositionTemplate);

    /**
     * 校验职位名称是否存在
     *
     * @param positionName 职位名称
     * @param id 职位ID (为null时为新增校验)
     * @return 是否存在
     */
    boolean checkPositionNameExists(String positionName, Long id);

    boolean removePositionTemplate(Long id);
} 