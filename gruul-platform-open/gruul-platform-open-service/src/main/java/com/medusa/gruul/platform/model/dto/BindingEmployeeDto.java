package com.medusa.gruul.platform.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 14:35 2024/10/8
 */
@Data
@ApiModel(value="BindingEmployeeDto对象", description="部门绑定职员dto")
public class BindingEmployeeDto {

    /**部门id*/
    @ApiModelProperty(value = "部门id")
    @NotBlank(message = "部门id不能为空")
    private String departmentId;

    /**职员id*/
    @ApiModelProperty(value = "职员id")
    @NotBlank(message = "职员id不能为空")
    private String employeeId;


    /**职员名称*/
    @ApiModelProperty(value = "职员名称")
    @NotBlank(message = "职员名称不能为空")
    private String employeeFullName;

}
