package com.medusa.gruul.platform.model.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 19:57 2024/10/9
 */
@ApiModel(value = "ApiStoreFrontParam 实体", description = "小程序门店 param")
@Data
public class ApiStoreFrontParam {

    @ApiModelProperty(value = "用户选址纬度")
    private Double lat;

    @ApiModelProperty(value = "用户选址经度")
    private Double lng;

    @ApiModelProperty(value = "查看有地址的门店：1.是")
    private Integer isAddress;

}
