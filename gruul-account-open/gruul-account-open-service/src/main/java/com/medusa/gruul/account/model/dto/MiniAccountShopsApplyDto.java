package com.medusa.gruul.account.model.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 9:24 2025/8/1
 */
@Data
@ApiModel(value = "小程序商户进件Dto")
public class MiniAccountShopsApplyDto {


    @ApiModelProperty(value = "系统流水号")
    private String reqNo;

    @ApiModelProperty(value = "请求流水号")
    private String mchReqNo;

    @ApiModelProperty(value = "开户行")
    private String bankName;

    @ApiModelProperty(value = "卡号")
    private String bankNo;

    @ApiModelProperty(value = "开户人姓名")
    private String bankUserName;

    @ApiModelProperty(value = "开户银行支行的联行号")
    private String accountUnionpay;

    @ApiModelProperty(value = "省名")
    private String provinceName;

    @ApiModelProperty(value = "市名")
    private String cityName;

    @ApiModelProperty(value = "区域名")
    private String areaName;

    @ApiModelProperty(value = "省编码")
    private String provinceCode;

    @ApiModelProperty(value = "市编码")
    private String cityCode;

    @ApiModelProperty(value = "区域编码")
    private String areaCode;

    @ApiModelProperty(value = "详细地址")
    private String address;

    @ApiModelProperty(value = "邮箱")
    private String email;
}
