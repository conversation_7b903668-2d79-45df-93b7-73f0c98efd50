package com.medusa.gruul.platform.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.medusa.gruul.platform.api.entity.AuthMenuButton;
import com.medusa.gruul.platform.api.model.vo.AuthMenuButtonVo;
import io.lettuce.core.dynamic.annotation.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @Author: plh
 * @Description: 菜单按钮Mapper类
 * @Date: Created in 9:41 2023/9/21
 */
@Repository
public interface AuthMenuButtonMapper extends BaseMapper<AuthMenuButton> {

    /**
     * 获取菜单按钮
     * @param menuId
     * @return
     */
    List<AuthMenuButtonVo> getAuthMenuButtonVo(@Param("menuId") Long menuId);

    /**
     * 获取用户菜单按钮
     * @param userId
     * @return
     */
    List<AuthMenuButtonVo> getAuthMenuButtonVoByUserId(@Param("userId")Long userId);
}
