package com.medusa.gruul.platform.model.param;

import com.medusa.gruul.common.core.param.QueryParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "PlatformPositionTemplateParam对象", description = "模版查询参数")
public class PlatformPositionTemplateParam extends QueryParam {
    @ApiModelProperty(value = "id")
    private String id;
    @ApiModelProperty(value = "职位名称")
    private String positionName;
    @ApiModelProperty(value = "状态")
    private Integer status;
    @ApiModelProperty(value = "备注")
    private String remark;

}
