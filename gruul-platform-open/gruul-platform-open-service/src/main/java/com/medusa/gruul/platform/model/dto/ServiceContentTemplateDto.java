package com.medusa.gruul.platform.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 服务项目模板DTO
 */
@Data
@ApiModel(value = "服务项目模板DTO", description = "服务项目模板DTO")
public class ServiceContentTemplateDto {

    @ApiModelProperty(value = "ID")
    private Long id;

    /**
     * 服务项目名称
     */
    @NotBlank(message = "服务项目名称不能为空")
    @ApiModelProperty(value = "服务项目名称", required = true)
    private String serviceName;

    /**
     * 适用商家类型(0-加盟, 1-子公司, 2-运营商)
     */
  /*  @NotBlank(message = "适用商家类型不能为空")*/
    @ApiModelProperty(value = "适用商家类型(0-加盟, 1-子公司, 2-运营商)", required = true)
    private String partnerModel;

    /**
     * 状态(0-停用, 1-启用)
     */
    @ApiModelProperty(value = "状态(0-停用, 1-启用)")
    private Integer status = 1;  // 默认为启用状态

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
} 