package com.medusa.gruul.platform.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.medusa.gruul.common.core.exception.ServiceException;
import com.medusa.gruul.common.core.util.CurUserUtil;
import com.medusa.gruul.common.core.util.SystemCode;
import com.medusa.gruul.common.dto.CurPcUserInfoDto;
import com.medusa.gruul.platform.api.entity.AuthMenuButton;
import com.medusa.gruul.platform.api.entity.AuthMenuInfo;
import com.medusa.gruul.platform.api.entity.AuthRoleMenu;
import com.medusa.gruul.platform.api.model.dto.AuthMenuButtonDto;
import com.medusa.gruul.platform.api.model.dto.AuthMenuInfoDto;
import com.medusa.gruul.platform.api.model.vo.AuthMenuButtonVo;
import com.medusa.gruul.platform.mapper.AuthMenuButtonMapper;
import com.medusa.gruul.platform.mapper.AuthRoleMenuMapper;
import com.medusa.gruul.platform.service.IAuthMenuButtonService;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Author: plh
 * @Description: 菜单按钮服务实现层
 * @Date: Created in 9:45 2023/9/21
 */
@Log4j2
@Service
public class AuthMenuButtonServiceImpl extends ServiceImpl<AuthMenuButtonMapper, AuthMenuButton> implements IAuthMenuButtonService {

    @Autowired
    private AuthRoleMenuMapper authRoleMenuMapper;

    @Override
    public void saveAuthMenuButton(AuthMenuButtonDto authMenuButtonDto) {
        AuthMenuButton authMenuButton = ifAuthMenuButtonNameAndCode(authMenuButtonDto);
        CurPcUserInfoDto pcUserInfoDto = CurUserUtil.getPcRqeustAccountInfo();
        authMenuButton.setCreateUserId(Long.valueOf(pcUserInfoDto.getUserId()));
        authMenuButton.setCreateUserName(pcUserInfoDto.getNikeName());
        int insert = this.baseMapper.insert(authMenuButton);
        if (insert == 0) {
            throw new ServiceException("新增失败！", SystemCode.DATA_ADD_FAILED.getCode());
        }
    }

    @Override
    public void editAuthMenuButton(AuthMenuButtonDto authMenuButtonDto) {
        if(authMenuButtonDto.getId()==null){
            throw new ServiceException("菜单按钮id不能为空！");
        }
        AuthMenuButton authMenuButton = this.getById(authMenuButtonDto.getId());
        if(authMenuButton==null){
            throw new ServiceException("菜单按钮不存在");
        }
        LambdaQueryWrapper<AuthMenuButton>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AuthMenuButton::getButtonName,authMenuButtonDto.getButtonName());
        wrapper.eq(AuthMenuButton::getMenuId,authMenuButtonDto.getMenuId());
        wrapper.ne(AuthMenuButton::getId,authMenuButtonDto.getId());
        AuthMenuButton searchAuthMenuButton = this.baseMapper.selectOne(wrapper);
        if(!BeanUtil.isEmpty(searchAuthMenuButton)){
            throw new ServiceException("菜单按钮名称已存在！", SystemCode.DATA_EXISTED.getCode());
        }
        LambdaQueryWrapper<AuthMenuButton>wrapper1 = new LambdaQueryWrapper<>();
        wrapper1.eq(AuthMenuButton::getButtonCode,authMenuButtonDto.getButtonCode());
        wrapper1.eq(AuthMenuButton::getMenuId,authMenuButtonDto.getMenuId());
        wrapper1.ne(AuthMenuButton::getId,authMenuButtonDto.getId());
        AuthMenuButton searchAuthMenuButton1 = this.baseMapper.selectOne(wrapper1);
        if(!BeanUtil.isEmpty(searchAuthMenuButton1)){
            throw new ServiceException("菜单按钮编码已存在！", SystemCode.DATA_EXISTED.getCode());
        }
        BeanUtils.copyProperties(authMenuButtonDto,authMenuButton);
        this.updateById(authMenuButton);

    }

    @Override
    public void deleteAuthMenuButton(Long id) {
        AuthMenuButton authMenuButton = this.getById(id);
        if(authMenuButton==null){
            throw new ServiceException("菜单按钮不存在");
        }

        LambdaQueryWrapper<AuthRoleMenu>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AuthRoleMenu::getType,1);
        wrapper.eq(AuthRoleMenu::getMenuId,id);
        Integer num = authRoleMenuMapper.selectCount(wrapper);
        if (num > 0){
            throw new ServiceException("该按钮下关联有角色，暂时无法删除");
        }
        this.baseMapper.deleteById(id);
        //情况角色菜单表
        authRoleMenuMapper.delete(wrapper);
    }

    @Override
    public List<AuthMenuButtonVo> getAuthMenuButtonVo(Long menuId) {
        return this.baseMapper.getAuthMenuButtonVo(menuId);
    }

    @Override
    public List<AuthMenuButtonVo> getAuthMenuButtonVoByUserId(Long userId) {
        return this.baseMapper.getAuthMenuButtonVoByUserId(userId);
    }

    /**
     * 判断查询分类名称是否已存在
     *
     * @param authMenuButtonDto
     * @return sortingCategory
     */
    private AuthMenuButton ifAuthMenuButtonNameAndCode(AuthMenuButtonDto authMenuButtonDto) {

        LambdaQueryWrapper<AuthMenuButton>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AuthMenuButton::getButtonName,authMenuButtonDto.getButtonName());
        wrapper.eq(AuthMenuButton::getMenuId,authMenuButtonDto.getMenuId());
        AuthMenuButton searchAuthMenuButton = this.baseMapper.selectOne(wrapper);
        if(!BeanUtil.isEmpty(searchAuthMenuButton)){
            throw new ServiceException("菜单按钮名称已存在！", SystemCode.DATA_EXISTED.getCode());
        }

        LambdaQueryWrapper<AuthMenuButton>wrapper1 = new LambdaQueryWrapper<>();
        wrapper1.eq(AuthMenuButton::getButtonCode,authMenuButtonDto.getButtonCode());
        wrapper1.eq(AuthMenuButton::getMenuId,authMenuButtonDto.getMenuId());
        AuthMenuButton searchAuthMenuButton1 = this.baseMapper.selectOne(wrapper1);
        if(!BeanUtil.isEmpty(searchAuthMenuButton1)){
            throw new ServiceException("菜单按钮编码已存在！", SystemCode.DATA_EXISTED.getCode());
        }
        AuthMenuButton authMenuButton = authMenuButtonDto.coverAuthMenuButton();
        return authMenuButton;
    }
}
