package com.medusa.gruul.platform.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.medusa.gruul.platform.api.entity.ServiceContentTemplate;
import com.medusa.gruul.platform.model.param.ServiceContentTemplateParam;
import com.medusa.gruul.platform.model.vo.ServiceContentTemplateVo;
import org.apache.ibatis.annotations.Param;

/**
 * 服务项目模板Mapper接口
 */
public interface ServiceContentTemplateMapper extends BaseMapper<ServiceContentTemplate> {
    
    /**
     * 分页查询服务项目模板列表
     *
     * @param page 分页参数
     * @param queryDto 查询条件
     * @return 分页结果
     */
    IPage<ServiceContentTemplateVo> selectServiceContentTemplateList(Page<ServiceContentTemplate> page,
                                                                    @Param("params") ServiceContentTemplateParam queryDto);

} 