package com.medusa.gruul.account.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 8:49 2025/5/27
 */
@Data
@ApiModel(value = "修改用户支付密码")
public class UpdateAccountPayPwdDto {



    @ApiModelProperty(value = "手机短信验证码")
    private String smsCode;

    @ApiModelProperty(value = "校验类型,1001-短信登录校验," +
            "1002-账号注册验证手机号," +
            "1003-用户手机号换绑校验," +
            "1004-用户修改密码," +
            "1005-用户信息修改," +
            "1006-用户删除自身店铺," +
            "1007-用户忘记密码找回," +
            "1008-代理申请," +
            "1009-代理银行卡手机号校验," +
            "1010-代理银行卡手机号换绑," +
            "1011-代理账号换绑," +
            "1012-代理修改密码," +
            "1013-修改支付密码," +
            "1014-验证代用户下单," +
            "1015-支付短信验证码," +
            "")
    private Integer type;

    @ApiModelProperty(value = "支付密码")
    private String payPwd;


    @ApiModelProperty(value = "确认密码")
    private String payPwdAgain;


}
