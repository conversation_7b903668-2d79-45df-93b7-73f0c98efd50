package com.medusa.gruul.platform.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.medusa.gruul.common.core.exception.ServiceException;
import com.medusa.gruul.common.core.util.SystemCode;
import com.medusa.gruul.platform.api.entity.AuthRoleInfo;
import com.medusa.gruul.platform.api.entity.AuthRoleMessage;
import com.medusa.gruul.platform.mapper.AuthRoleInfoMapper;
import com.medusa.gruul.platform.mapper.AuthRoleMessageMapper;
import com.medusa.gruul.platform.model.dto.AuthRoleMessageDto;
import com.medusa.gruul.platform.service.IAuthRoleInfoService;
import com.medusa.gruul.platform.service.IAuthRoleMessageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 16:05 2025/5/27
 */
@Service
public class AuthRoleMessageServiceImpl extends ServiceImpl<AuthRoleMessageMapper, AuthRoleMessage> implements IAuthRoleMessageService {

    /**
     * 根据角色id获取消息类型
     * @param roleId
     * @return
     */
    @Override
    public List<Integer> getMessageTypeByRoleId(Long roleId) {

        List<Integer>messageTypes = new ArrayList<>();
        LambdaQueryWrapper<AuthRoleMessage>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AuthRoleMessage::getRoleId,roleId);
        List<AuthRoleMessage> list = this.list(wrapper);
        if(list!=null&&list.size()>0){
            for (AuthRoleMessage authRoleMessage : list) {
                messageTypes.add(authRoleMessage.getMessageType());
            }
        }
        return messageTypes;
    }
}
