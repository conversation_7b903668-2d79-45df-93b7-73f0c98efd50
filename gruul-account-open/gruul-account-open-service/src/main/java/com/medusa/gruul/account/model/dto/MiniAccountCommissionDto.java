package com.medusa.gruul.account.model.dto;

import com.baomidou.mybatisplus.annotation.*;
import com.medusa.gruul.common.data.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 用户佣金明细DTO
 * <AUTHOR>
 */
@Data
@ApiModel(value = "新增或修改用户佣金明细DTO")
public class MiniAccountCommissionDto {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id")
    private Long id;

    /**
     * 小程序用户id（实际存的是用户信息扩展表的shop_user_id）
     */
    @ApiModelProperty(value = "小程序用户id")
    private String userId;

    /**
     * 订单id
     */
    @ApiModelProperty(value = "订单id")
    private String orderId;

    /**
     * 佣金类型:100->团队分佣;佣金类型:101->奖励佣金;佣金类型:102->奖励提成;200->佣金提现;300->删除订单
     */
    @ApiModelProperty(value = "佣金类型:100->团队分佣;佣金类型:101->奖励佣金;佣金类型:102->奖励提成;200->佣金提现;300->删除订单")
    private Integer commissionType;

    /**
     * 佣金
     */
    @ApiModelProperty(value = "佣金")
    private BigDecimal amount;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 佣金来源用户id
     */
    @ApiModelProperty(value = "佣金来源用户id")
    private String sourceShopUserId;
    /**
     * 奖励活动id
     */
    @ApiModelProperty(value = "奖励活动id")
    private String rewardId;
    /**
     * 奖励活动明细id
     */
    @ApiModelProperty(value = "奖励活动明细id")
    private String rewardDetId;


    /**
     * 转赠人用户ID
     */
    @ApiModelProperty(value = "转赠人用户ID")
    private String transferShopUserId;

    /**
     * 接收人用户ID
     */
    @ApiModelProperty(value = "接收人用户ID")
    private String receiveShopUserId;

    /**
     * 后台备注
     */
    @ApiModelProperty(value = "后台备注")
    private String platformRemark;

    /**
     * 数据来源，0-系统，1-后台
     */
    @ApiModelProperty(value = "数据来源，0-系统，1-后台")
    private Integer source;

    /**
     * 平台操作用户id
     */
    @ApiModelProperty(value = "平台操作用户id")
    private String platformUserId;

    /**
     * 平台操作用户名
     */
    @ApiModelProperty(value = "平台操作用户名")
    private String platformUserName;


    /**
     * 会员类型id
     */
    @ApiModelProperty(value = "会员类型id")
    private Long memberTypeId;
}
